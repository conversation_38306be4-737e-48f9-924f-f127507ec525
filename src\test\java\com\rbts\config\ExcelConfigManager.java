package com.rbts.config;

import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * Excel-Based Configuration Manager
 * Reads all configuration from Excel sheets for maximum flexibility
 */
@Slf4j
public class ExcelConfigManager {
    
    private static ExcelConfigManager instance;
    private final ExcelUtils excelUtils;
    private final String configFilePath;
    
    // Configuration caches
    private Map<String, String> generalConfig = new HashMap<>();
    private Map<String, List<String>> serviceTablesConfig = new HashMap<>();
    private Map<String, String> servicePatternConfig = new HashMap<>();
    private Map<String, String> urlConfig = new HashMap<>();
    private Map<String, String> databaseConfig = new HashMap<>();
    private Map<String, String> validationConfig = new HashMap<>();
    private Map<String, Map<String, String>> tableEndpointConfig = new HashMap<>();
    private Map<String, List<String>> constraintConfig = new HashMap<>();
    private Map<String, Map<String, FieldMapping>> fieldMappingConfig = new HashMap<>();
    private Map<String, TestExecutionControl> testExecutionControls = new HashMap<>();

    private ExcelConfigManager() {
        this.excelUtils = new ExcelUtils();
        this.configFilePath = "config/Framework_Configuration.xlsx";
        loadAllConfigurations();
    }
    
    public static synchronized ExcelConfigManager getInstance() {
        if (instance == null) {
            instance = new ExcelConfigManager();
        }
        return instance;
    }
    
    /**
     * Load all configurations from Excel file
     */
    public void loadAllConfigurations() {
        log.info("Loading configurations from Excel file: {}", configFilePath);
        
        try {
            loadGeneralConfiguration();
            loadServiceConfiguration();
            loadUrlConfiguration();
            loadDatabaseConfiguration();
            loadValidationConfiguration();
            loadTableEndpointConfiguration();
            loadConstraintConfiguration();
            loadTestExecutionControlConfiguration();
            loadFieldMappingConfiguration();

            log.info("✅ All configurations loaded successfully from Excel");
            
        } catch (Exception e) {
            log.error("❌ Error loading configurations from Excel: {}", e.getMessage());
            loadDefaultConfigurations();
        }
    }
    
    /**
     * Load general framework configuration
     */
    private void loadGeneralConfiguration() {
        log.info("Loading general configuration from 'General_Config' sheet");
        
        try {
            int row = 2; // Start from row 2 (assuming headers in row 1)
            String configKey;
            
            while ((configKey = excelUtils.getCellData(configFilePath, "General_Config", row, 1)) != null 
                   && !configKey.trim().isEmpty()) {
                
                String configValue = excelUtils.getCellData(configFilePath, "General_Config", row, 2);
                String description = excelUtils.getCellData(configFilePath, "General_Config", row, 3);
                
                generalConfig.put(configKey.trim(), configValue != null ? configValue.trim() : "");
                
                log.debug("Loaded general config: {} = {} ({})", configKey, configValue, description);
                row++;
            }
            
            log.info("✅ Loaded {} general configuration items", generalConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load general configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load service and table configuration
     */
    private void loadServiceConfiguration() {
        log.info("Loading service configuration from 'Service_Config' sheet");
        
        try {
            int row = 2;
            String serviceName;
            
            while ((serviceName = excelUtils.getCellData(configFilePath, "Service_Config", row, 1)) != null 
                   && !serviceName.trim().isEmpty()) {
                
                String tablesStr = excelUtils.getCellData(configFilePath, "Service_Config", row, 2);
                String pattern = excelUtils.getCellData(configFilePath, "Service_Config", row, 3);
                String description = excelUtils.getCellData(configFilePath, "Service_Config", row, 4);
                
                // Parse tables
                List<String> tables = new ArrayList<>();
                if (tablesStr != null && !tablesStr.trim().isEmpty()) {
                    String[] tableArray = tablesStr.split(",");
                    for (String table : tableArray) {
                        tables.add(table.trim());
                    }
                }
                
                serviceTablesConfig.put(serviceName.trim(), tables);
                servicePatternConfig.put(serviceName.trim(), pattern != null ? pattern.trim() : "direct");
                
                log.debug("Loaded service config: {} = {} tables, pattern: {} ({})", 
                         serviceName, tables.size(), pattern, description);
                row++;
            }
            
            log.info("✅ Loaded {} service configurations", serviceTablesConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load service configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load URL configuration
     */
    private void loadUrlConfiguration() {
        log.info("Loading URL configuration from 'URL_Config' sheet");
        
        try {
            int row = 2;
            String urlKey;
            
            while ((urlKey = excelUtils.getCellData(configFilePath, "URL_Config", row, 1)) != null 
                   && !urlKey.trim().isEmpty()) {
                
                String urlValue = excelUtils.getCellData(configFilePath, "URL_Config", row, 2);
                String description = excelUtils.getCellData(configFilePath, "URL_Config", row, 3);
                
                urlConfig.put(urlKey.trim(), urlValue != null ? urlValue.trim() : "");
                
                log.debug("Loaded URL config: {} = {} ({})", urlKey, urlValue, description);
                row++;
            }
            
            log.info("✅ Loaded {} URL configurations", urlConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load URL configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load database configuration
     */
    private void loadDatabaseConfiguration() {
        log.info("Loading database configuration from 'Database_Config' sheet");
        
        try {
            int row = 2;
            String dbKey;
            
            while ((dbKey = excelUtils.getCellData(configFilePath, "Database_Config", row, 1)) != null 
                   && !dbKey.trim().isEmpty()) {
                
                String dbValue = excelUtils.getCellData(configFilePath, "Database_Config", row, 2);
                String description = excelUtils.getCellData(configFilePath, "Database_Config", row, 3);
                
                databaseConfig.put(dbKey.trim(), dbValue != null ? dbValue.trim() : "");
                
                log.debug("Loaded database config: {} = {} ({})", dbKey, dbValue, description);
                row++;
            }
            
            log.info("✅ Loaded {} database configurations", databaseConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load database configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load validation configuration
     */
    private void loadValidationConfiguration() {
        log.info("Loading validation configuration from 'Validation_Config' sheet");
        
        try {
            int row = 2;
            String validationKey;
            
            while ((validationKey = excelUtils.getCellData(configFilePath, "Validation_Config", row, 1)) != null 
                   && !validationKey.trim().isEmpty()) {
                
                String validationValue = excelUtils.getCellData(configFilePath, "Validation_Config", row, 2);
                String description = excelUtils.getCellData(configFilePath, "Validation_Config", row, 3);
                
                validationConfig.put(validationKey.trim(), validationValue != null ? validationValue.trim() : "");
                
                log.debug("Loaded validation config: {} = {} ({})", validationKey, validationValue, description);
                row++;
            }
            
            log.info("✅ Loaded {} validation configurations", validationConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load validation configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load table endpoint configuration
     */
    private void loadTableEndpointConfiguration() {
        log.info("Loading table endpoint configuration from 'Table_Endpoints' sheet");
        
        try {
            int row = 2;
            String tableName;
            
            while ((tableName = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 1)) != null 
                   && !tableName.trim().isEmpty()) {
                
                String postEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 2);
                String putEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 3);
                String patchEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 4);
                String getEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 5);
                String getAllEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 6);
                String deleteEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 7);

                Map<String, String> endpoints = new HashMap<>();
                endpoints.put("post", postEndpoint != null ? postEndpoint.trim() : "");
                endpoints.put("put", putEndpoint != null ? putEndpoint.trim() : "");
                endpoints.put("patch", patchEndpoint != null ? patchEndpoint.trim() : "");
                endpoints.put("get", getEndpoint != null ? getEndpoint.trim() : "");
                endpoints.put("getall", getAllEndpoint != null ? getAllEndpoint.trim() : "");
                endpoints.put("delete", deleteEndpoint != null ? deleteEndpoint.trim() : "");
                
                tableEndpointConfig.put(tableName.trim(), endpoints);
                
                log.debug("Loaded table endpoints for: {}", tableName);
                row++;
            }
            
            log.info("✅ Loaded endpoint configurations for {} tables", tableEndpointConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load table endpoint configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load constraint configuration
     */
    private void loadConstraintConfiguration() {
        log.info("Loading constraint configuration from 'Constraint_Config' sheet");
        
        try {
            int row = 2;
            String tableName;
            
            while ((tableName = excelUtils.getCellData(configFilePath, "Constraint_Config", row, 1)) != null 
                   && !tableName.trim().isEmpty()) {
                
                String nullConstraints = excelUtils.getCellData(configFilePath, "Constraint_Config", row, 2);
                String uniqueConstraints = excelUtils.getCellData(configFilePath, "Constraint_Config", row, 3);
                String foreignKeys = excelUtils.getCellData(configFilePath, "Constraint_Config", row, 4);
                
                List<String> constraints = new ArrayList<>();
                if (nullConstraints != null && !nullConstraints.trim().isEmpty()) {
                    constraints.addAll(Arrays.asList(nullConstraints.split(",")));
                }
                if (uniqueConstraints != null && !uniqueConstraints.trim().isEmpty()) {
                    constraints.addAll(Arrays.asList(uniqueConstraints.split(",")));
                }
                if (foreignKeys != null && !foreignKeys.trim().isEmpty()) {
                    constraints.addAll(Arrays.asList(foreignKeys.split(",")));
                }
                
                constraintConfig.put(tableName.trim(), constraints);
                
                log.debug("Loaded constraints for table {}: {} constraints", tableName, constraints.size());
                row++;
            }
            
            log.info("✅ Loaded constraint configurations for {} tables", constraintConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load constraint configuration: {}", e.getMessage());
        }
    }

    /**
     * Load test execution control configuration
     */
    private void loadTestExecutionControlConfiguration() {
        log.info("Loading test execution control configuration from 'Test_Execution_Control' sheet");

        try {
            int row = 2;
            String serviceName;

            while ((serviceName = excelUtils.getCellData(configFilePath, "Test_Execution_Control", row, 1)) != null
                   && !serviceName.trim().isEmpty()) {

                String tableName = excelUtils.getCellData(configFilePath, "Test_Execution_Control", row, 2);
                String enableService = excelUtils.getCellData(configFilePath, "Test_Execution_Control", row, 3);
                String enableTable = excelUtils.getCellData(configFilePath, "Test_Execution_Control", row, 4);
                String operationsToTest = excelUtils.getCellData(configFilePath, "Test_Execution_Control", row, 5);
                String testPriority = excelUtils.getCellData(configFilePath, "Test_Execution_Control", row, 6);
                String testTypes = excelUtils.getCellData(configFilePath, "Test_Execution_Control", row, 7);
                String executionOrder = excelUtils.getCellData(configFilePath, "Test_Execution_Control", row, 8);
                String comments = excelUtils.getCellData(configFilePath, "Test_Execution_Control", row, 9);

                TestExecutionControl control = TestExecutionControl.fromExcelRow(
                    serviceName, tableName, enableService, enableTable,
                    operationsToTest, testPriority, testTypes, executionOrder, comments
                );

                String key = control.getKey();
                testExecutionControls.put(key, control);

                log.debug("Loaded test execution control: {}", control);
                row++;
            }

            log.info("✅ Loaded {} test execution control configurations", testExecutionControls.size());

        } catch (Exception e) {
            log.warn("Could not load test execution control configuration: {}", e.getMessage());
            // Load default controls if Excel loading fails
            loadDefaultTestExecutionControls();
        }
    }

    /**
     * Load default test execution controls
     */
    private void loadDefaultTestExecutionControls() {
        log.info("Loading default test execution controls");

        // Enable all services and tables by default
        for (String service : serviceTablesConfig.keySet()) {
            List<String> tables = serviceTablesConfig.get(service);
            for (String table : tables) {
                TestExecutionControl control = TestExecutionControl.builder()
                        .serviceName(service)
                        .tableName(table)
                        .enableService(true)
                        .enableTable(true)
                        .operationsToTest(Arrays.asList("post", "get", "getall"))
                        .testPriority("medium")
                        .testTypes(Arrays.asList("normal"))
                        .executionOrder(0)
                        .comments("Default configuration")
                        .build();

                testExecutionControls.put(control.getKey(), control);
            }
        }

        log.info("✅ Loaded {} default test execution controls", testExecutionControls.size());
    }

    /**
     * Load field mapping configuration from service-specific Excel sheets
     */
    private void loadFieldMappingConfiguration() {
        log.info("Loading field mapping configuration from service-specific sheets");

        // Load field mappings from each service-specific sheet
        loadFieldMappingFromSheet("FieldMapping_Contact");
        loadFieldMappingFromSheet("FieldMapping_Auth");
        loadFieldMappingFromSheet("FieldMapping_Core");
        loadFieldMappingFromSheet("FieldMapping_Order");
        loadFieldMappingFromSheet("FieldMapping_Product");
    }

    /**
     * Load field mapping configuration from a specific sheet
     */
    private void loadFieldMappingFromSheet(String sheetName) {
        log.info("Loading field mapping configuration from '{}' sheet", sheetName);

        try {
            int row = 2; // Start from row 2 (assuming headers in row 1)
            String tableName;

            while ((tableName = excelUtils.getCellData(configFilePath, sheetName, row, 1)) != null
                   && !tableName.trim().isEmpty()) {

                String databaseField = excelUtils.getCellData(configFilePath, sheetName, row, 2);
                String apiRequestField = excelUtils.getCellData(configFilePath, sheetName, row, 3);
                String apiResponseField = excelUtils.getCellData(configFilePath, sheetName, row, 4);
                String fieldType = excelUtils.getCellData(configFilePath, sheetName, row, 5);
                String requestOperations = excelUtils.getCellData(configFilePath, sheetName, row, 6);
                String responseOperations = excelUtils.getCellData(configFilePath, sheetName, row, 7);
                String fkSameService = excelUtils.getCellData(configFilePath, sheetName, row, 8);
                String fkDifferentService = excelUtils.getCellData(configFilePath, sheetName, row, 9);
                String description = excelUtils.getCellData(configFilePath, sheetName, row, 10);

                FieldMapping fieldMapping = new FieldMapping(
                    databaseField != null ? databaseField.trim() : "",
                    apiRequestField != null ? apiRequestField.trim() : "",
                    apiResponseField != null ? apiResponseField.trim() : "",
                    fieldType != null ? fieldType.trim() : "",
                    description != null ? description.trim() : "",
                    requestOperations != null ? requestOperations.trim() : "ALL",
                    responseOperations != null ? responseOperations.trim() : "ALL",
                    fkSameService != null ? fkSameService.trim() : "",
                    fkDifferentService != null ? fkDifferentService.trim() : ""
                );

                // Store field mapping by table and database field name
                fieldMappingConfig.computeIfAbsent(tableName.trim(), k -> new HashMap<>())
                    .put(databaseField != null ? databaseField.trim() : "", fieldMapping);

                log.debug("Loaded field mapping from {} for table {}: {}", sheetName, tableName, fieldMapping);

                row++;
            }

        } catch (Exception e) {
            log.warn("Could not load field mapping configuration from sheet {}: {}", sheetName, e.getMessage());
        }
    }

    /**
     * Load default configurations if Excel loading fails
     */
    private void loadDefaultConfigurations() {
        log.info("Loading default configurations as fallback");
        
        // Default general config
        generalConfig.put("framework.name", "Automated CRUD Testing Framework");
        generalConfig.put("framework.version", "1.0.0");
        generalConfig.put("auto.test.operations", "post,put,patch,get,getall,delete");
        generalConfig.put("auto.test.types", "normal,null_constraint,unique_constraint,foreign_key_invalid");
        
        // Default URL config
        urlConfig.put("direct.pattern.base_url", "http://localhost:8071");
        urlConfig.put("proxy.pattern.base_url", "http://localhost:9762");
        urlConfig.put("proxy.pattern.endpoint", "/decrypt");
        
        // Default database config
        databaseConfig.put("JDBC_URL", "***************************************");
        databaseConfig.put("JDBC_USER", "testuser");
        databaseConfig.put("JDBC_PASSWORD", "testpass");
        
        // Default validation config
        validationConfig.put("validation.status_code.post", "201,200");
        validationConfig.put("validation.status_code.put", "200");
        validationConfig.put("validation.status_code.patch", "200");
        validationConfig.put("validation.status_code.get", "200");
        validationConfig.put("validation.status_code.getall", "200");
        validationConfig.put("validation.status_code.delete", "200,204");
        validationConfig.put("validation.constraint_violation.expected_status", "400,422");

        // Specific constraint violation status codes
        validationConfig.put("validation.unique_constraint.expected_status", "701");
        validationConfig.put("validation.null_constraint.expected_status", "700");
        validationConfig.put("validation.foreign_key_same_service.expected_status", "404");
        validationConfig.put("validation.foreign_key_other_service.expected_status", "702");
        
        log.info("✅ Default configurations loaded");
    }
    
    // Getter methods for accessing configurations
    
    public String getGeneralConfig(String key) {
        return generalConfig.getOrDefault(key, "");
    }
    
    public List<String> getTablesForService(String service) {
        return serviceTablesConfig.getOrDefault(service, new ArrayList<>());
    }
    
    public String getServicePattern(String service) {
        return servicePatternConfig.getOrDefault(service, "direct");
    }
    
    public String getUrlConfig(String key) {
        return urlConfig.getOrDefault(key, "");
    }
    
    public String getDatabaseConfig(String key) {
        return databaseConfig.getOrDefault(key, "");
    }
    
    public String getValidationConfig(String key) {
        return validationConfig.getOrDefault(key, "");
    }
    
    public String getTableEndpoint(String table, String operation) {
        Map<String, String> endpoints = tableEndpointConfig.get(table);
        if (endpoints != null) {
            String endpoint = endpoints.get(operation.toLowerCase());
            if (endpoint != null && !endpoint.trim().isEmpty()) {
                return endpoint;
            }
        }

        // Return default endpoint if not configured
        return "/api/" + table.toLowerCase() + "/" + getDefaultOperationEndpoint(operation);
    }

    /**
     * Check if operation should be tested for a table
     * Returns false if endpoint is null or empty in Excel configuration
     */
    public boolean shouldTestOperation(String table, String operation) {
        Map<String, String> endpoints = tableEndpointConfig.get(table);
        if (endpoints != null) {
            String endpoint = endpoints.get(operation.toLowerCase());
            // If endpoint is explicitly set to null, empty, or "null" string, skip testing
            if (endpoint == null || endpoint.trim().isEmpty() || "null".equalsIgnoreCase(endpoint.trim())) {
                log.debug("Skipping {} operation for table {} - endpoint is null/empty", operation, table);
                return false;
            }
        }

        // If no specific endpoint configuration exists, test the operation (use defaults)
        return true;
    }

    /**
     * Get list of operations to test for a specific table
     */
    public List<String> getOperationsToTestForTable(String table) {
        String[] allOperations = getGeneralConfig("auto.test.operations").split(",");
        List<String> operationsToTest = new ArrayList<>();

        for (String operation : allOperations) {
            String trimmedOperation = operation.trim();
            if (shouldTestOperation(table, trimmedOperation)) {
                operationsToTest.add(trimmedOperation);
            }
        }

        log.debug("Operations to test for table {}: {}", table, operationsToTest);
        return operationsToTest;
    }
    
    public List<String> getConstraintsForTable(String table) {
        return constraintConfig.getOrDefault(table, new ArrayList<>());
    }
    
    public Set<String> getConfiguredServices() {
        return serviceTablesConfig.keySet();
    }

    public List<String> getServiceNames() {
        return new ArrayList<>(serviceTablesConfig.keySet());
    }

    public Set<String> getAllConfiguredTables() {
        Set<String> allTables = new HashSet<>();
        for (List<String> tables : serviceTablesConfig.values()) {
            allTables.addAll(tables);
        }
        return allTables;
    }
    
    private String getDefaultOperationEndpoint(String operation) {
        switch (operation.toLowerCase()) {
            case "post": return "save";
            case "put": return "update";
            case "patch": return "patch";
            case "get": return "getById";
            case "getall": return "getAll";
            case "delete": return "delete";
            default: return operation.toLowerCase();
        }
    }
    
    /**
     * Reload configurations from Excel
     */
    public void reloadConfigurations() {
        log.info("Reloading configurations from Excel file");
        
        // Clear existing configurations
        generalConfig.clear();
        serviceTablesConfig.clear();
        servicePatternConfig.clear();
        urlConfig.clear();
        databaseConfig.clear();
        validationConfig.clear();
        tableEndpointConfig.clear();
        constraintConfig.clear();
        fieldMappingConfig.clear();
        
        // Reload all configurations
        loadAllConfigurations();
        
        log.info("✅ Configurations reloaded successfully");
    }
    
    /**
     * Get expected status code for specific constraint violation type
     */
    public String getConstraintViolationStatusCode(String constraintType, String currentService, String foreignKeyService) {
        switch (constraintType.toLowerCase()) {
            case "unique_constraint":
                return getValidationConfig("validation.unique_constraint.expected_status");
            case "null_constraint":
                return getValidationConfig("validation.null_constraint.expected_status");
            case "foreign_key_invalid":
                // Check if foreign key is from same service or different service
                if (currentService != null && foreignKeyService != null && currentService.equals(foreignKeyService)) {
                    return getValidationConfig("validation.foreign_key_same_service.expected_status");
                } else {
                    return getValidationConfig("validation.foreign_key_other_service.expected_status");
                }
            default:
                return getValidationConfig("validation.constraint_violation.expected_status");
        }
    }

    /**
     * Get service for a specific table
     */
    public String getServiceForTable(String tableName) {
        for (Map.Entry<String, List<String>> entry : serviceTablesConfig.entrySet()) {
            if (entry.getValue().contains(tableName)) {
                return entry.getKey();
            }
        }
        return null; // Table not found in any service
    }

    /**
     * Determine foreign key service based on foreign key table name
     */
    public String getForeignKeyService(String foreignKeyTable) {
        return getServiceForTable(foreignKeyTable);
    }

    /**
     * Get expected status code for constraint violation with automatic service detection
     */
    public String getConstraintViolationStatusCodeForTable(String constraintType, String currentTable, String foreignKeyTable) {
        String currentService = getServiceForTable(currentTable);
        String foreignKeyService = null;

        if (foreignKeyTable != null && !foreignKeyTable.isEmpty()) {
            foreignKeyService = getForeignKeyService(foreignKeyTable);
        }

        return getConstraintViolationStatusCode(constraintType, currentService, foreignKeyService);
    }

    /**
     * Check if constraint violation should expect specific status code
     */
    public boolean isConstraintViolationExpected(String constraintType, int actualStatusCode, String currentTable, String foreignKeyTable) {
        String expectedStatusCodes = getConstraintViolationStatusCodeForTable(constraintType, currentTable, foreignKeyTable);

        if (expectedStatusCodes == null || expectedStatusCodes.trim().isEmpty()) {
            return false;
        }

        String[] statusCodes = expectedStatusCodes.split(",");
        for (String statusCode : statusCodes) {
            try {
                if (Integer.parseInt(statusCode.trim()) == actualStatusCode) {
                    return true;
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid status code format: {}", statusCode);
            }
        }

        return false;
    }

    /**
     * Get field mapping for a specific table and database field
     */
    public FieldMapping getFieldMapping(String tableName, String databaseField) {
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            return tableMapping.get(databaseField);
        }
        return null;
    }

    /**
     * Get all field mappings for a specific table
     */
    public Map<String, FieldMapping> getFieldMappingsForTable(String tableName) {
        return fieldMappingConfig.getOrDefault(tableName, new HashMap<>());
    }

    /**
     * Get API request field name for a database field
     */
    public String getApiRequestFieldName(String tableName, String databaseField) {
        FieldMapping mapping = getFieldMapping(tableName, databaseField);
        if (mapping != null) {
            return mapping.getRequestFieldName();
        }
        return databaseField; // Return database field name as fallback
    }

    /**
     * Get API response field name for a database field
     */
    public String getApiResponseFieldName(String tableName, String databaseField) {
        FieldMapping mapping = getFieldMapping(tableName, databaseField);
        if (mapping != null) {
            return mapping.getResponseFieldName();
        }
        return databaseField; // Return database field name as fallback
    }

    /**
     * Get database field name for an API request field
     */
    public String getDatabaseFieldNameFromRequest(String tableName, String apiRequestField) {
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            for (FieldMapping mapping : tableMapping.values()) {
                if (apiRequestField.equals(mapping.getApiRequestField()) ||
                    apiRequestField.equals(mapping.getDatabaseField())) {
                    return mapping.getDatabaseField();
                }
            }
        }
        return apiRequestField; // Return API field name as fallback
    }

    /**
     * Get database field name for an API response field
     */
    public String getDatabaseFieldNameFromResponse(String tableName, String apiResponseField) {
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            for (FieldMapping mapping : tableMapping.values()) {
                if (apiResponseField.equals(mapping.getApiResponseField()) ||
                    apiResponseField.equals(mapping.getDatabaseField())) {
                    return mapping.getDatabaseField();
                }
            }
        }
        return apiResponseField; // Return API field name as fallback
    }

    /**
     * Check if a field is a primary key
     */
    public boolean isPrimaryKeyField(String tableName, String databaseField) {
        FieldMapping mapping = getFieldMapping(tableName, databaseField);
        return mapping != null && mapping.isPrimaryKey();
    }

    /**
     * Check if a field is a foreign key
     */
    public boolean isForeignKeyField(String tableName, String databaseField) {
        FieldMapping mapping = getFieldMapping(tableName, databaseField);
        return mapping != null && mapping.isForeignKey();
    }

    /**
     * Get all primary key fields for a table
     */
    public List<String> getPrimaryKeyFields(String tableName) {
        List<String> primaryKeys = new ArrayList<>();
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            for (FieldMapping mapping : tableMapping.values()) {
                if (mapping.isPrimaryKey()) {
                    primaryKeys.add(mapping.getDatabaseField());
                }
            }
        }
        return primaryKeys;
    }

    /**
     * Get all foreign key fields for a table
     */
    public List<String> getForeignKeyFields(String tableName) {
        List<String> foreignKeys = new ArrayList<>();
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            for (FieldMapping mapping : tableMapping.values()) {
                if (mapping.isForeignKey()) {
                    foreignKeys.add(mapping.getDatabaseField());
                }
            }
        }
        return foreignKeys;
    }

    /**
     * Get fields that should be present in request for a specific operation
     */
    public List<String> getFieldsPresentInRequest(String tableName, String operation) {
        List<String> fieldsInRequest = new ArrayList<>();
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            for (FieldMapping mapping : tableMapping.values()) {
                if (mapping.isPresentInRequest(operation)) {
                    fieldsInRequest.add(mapping.getDatabaseField());
                }
            }
        }
        return fieldsInRequest;
    }

    /**
     * Get fields that should be present in response for a specific operation
     */
    public List<String> getFieldsPresentInResponse(String tableName, String operation) {
        List<String> fieldsInResponse = new ArrayList<>();
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            for (FieldMapping mapping : tableMapping.values()) {
                if (mapping.isPresentInResponse(operation)) {
                    fieldsInResponse.add(mapping.getDatabaseField());
                }
            }
        }
        return fieldsInResponse;
    }

    /**
     * Check if a field should be present in request for a specific operation
     */
    public boolean isFieldPresentInRequest(String tableName, String databaseField, String operation) {
        FieldMapping mapping = getFieldMapping(tableName, databaseField);
        return mapping != null && mapping.isPresentInRequest(operation);
    }

    /**
     * Check if a field should be present in response for a specific operation
     */
    public boolean isFieldPresentInResponse(String tableName, String databaseField, String operation) {
        FieldMapping mapping = getFieldMapping(tableName, databaseField);
        return mapping != null && mapping.isPresentInResponse(operation);
    }

    /**
     * Get audit fields for a table
     */
    public List<String> getAuditFields(String tableName) {
        List<String> auditFields = new ArrayList<>();
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            for (FieldMapping mapping : tableMapping.values()) {
                if (mapping.isAuditField()) {
                    auditFields.add(mapping.getDatabaseField());
                }
            }
        }
        return auditFields;
    }

    /**
     * Get creation audit fields (createdBy, createdAt)
     */
    public List<String> getCreationAuditFields(String tableName) {
        List<String> creationFields = new ArrayList<>();
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            for (FieldMapping mapping : tableMapping.values()) {
                if (mapping.isCreationAuditField()) {
                    creationFields.add(mapping.getDatabaseField());
                }
            }
        }
        return creationFields;
    }

    /**
     * Get modification audit fields (modifiedBy, lastModifiedAt)
     */
    public List<String> getModificationAuditFields(String tableName) {
        List<String> modificationFields = new ArrayList<>();
        Map<String, FieldMapping> tableMapping = fieldMappingConfig.get(tableName);
        if (tableMapping != null) {
            for (FieldMapping mapping : tableMapping.values()) {
                if (mapping.isModificationAuditField()) {
                    modificationFields.add(mapping.getDatabaseField());
                }
            }
        }
        return modificationFields;
    }

    /**
     * Get test execution control for a specific service and table
     */
    public TestExecutionControl getTestExecutionControl(String service, String table) {
        String key = service + "." + table;
        return testExecutionControls.get(key);
    }

    /**
     * Check if service is enabled for testing
     */
    public boolean isServiceEnabledForTesting(String service) {
        // Check if any table in the service is enabled
        for (TestExecutionControl control : testExecutionControls.values()) {
            if (control.getServiceName().equals(service) && control.isServiceEnabled()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if table is enabled for testing
     */
    public boolean isTableEnabledForTesting(String service, String table) {
        TestExecutionControl control = getTestExecutionControl(service, table);
        return control != null && control.isTableEnabled();
    }

    /**
     * Get all enabled tables for testing (filtered by test execution control)
     */
    public Set<String> getEnabledTablesForTesting() {
        Set<String> enabledTables = new HashSet<>();

        for (TestExecutionControl control : testExecutionControls.values()) {
            if (control.isTableEnabled()) {
                enabledTables.add(control.getTableName());
            }
        }

        return enabledTables;
    }

    /**
     * Get enabled tables for a specific service
     */
    public List<String> getEnabledTablesForService(String service) {
        List<String> enabledTables = new ArrayList<>();

        for (TestExecutionControl control : testExecutionControls.values()) {
            if (control.getServiceName().equals(service) && control.isTableEnabled()) {
                enabledTables.add(control.getTableName());
            }
        }

        return enabledTables;
    }

    /**
     * Get all enabled services for testing
     */
    public Set<String> getEnabledServicesForTesting() {
        Set<String> enabledServices = new HashSet<>();

        for (TestExecutionControl control : testExecutionControls.values()) {
            if (control.isServiceEnabled()) {
                enabledServices.add(control.getServiceName());
            }
        }

        return enabledServices;
    }

    /**
     * Get configuration summary
     */
    public void printConfigurationSummary() {
        log.info("=== EXCEL CONFIGURATION SUMMARY ===");
        log.info("General Config Items: {}", generalConfig.size());
        log.info("Configured Services: {}", serviceTablesConfig.size());
        log.info("Total Tables: {}", getAllConfiguredTables().size());
        log.info("URL Configurations: {}", urlConfig.size());
        log.info("Database Configurations: {}", databaseConfig.size());
        log.info("Validation Configurations: {}", validationConfig.size());
        log.info("Table Endpoint Configurations: {}", tableEndpointConfig.size());
        log.info("Constraint Configurations: {}", constraintConfig.size());
        log.info("Field Mapping Configurations: {}", fieldMappingConfig.size());
        log.info("=====================================");

        // Print constraint validation status codes
        log.info("=== CONSTRAINT VALIDATION STATUS CODES ===");
        log.info("Unique Constraint: {}", getValidationConfig("validation.unique_constraint.expected_status"));
        log.info("Null Constraint: {}", getValidationConfig("validation.null_constraint.expected_status"));
        log.info("Foreign Key (Same Service): {}", getValidationConfig("validation.foreign_key_same_service.expected_status"));
        log.info("Foreign Key (Other Service): {}", getValidationConfig("validation.foreign_key_other_service.expected_status"));
        log.info("===============================================");
    }
}
