package com.rbts.config;

import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * Excel-Based Configuration Manager
 * Reads all configuration from Excel sheets for maximum flexibility
 */
@Slf4j
public class ExcelConfigManager {
    
    private static ExcelConfigManager instance;
    private final ExcelUtils excelUtils;
    private final String configFilePath;
    
    // Configuration caches
    private Map<String, String> generalConfig = new HashMap<>();
    private Map<String, List<String>> serviceTablesConfig = new HashMap<>();
    private Map<String, String> servicePatternConfig = new HashMap<>();
    private Map<String, String> urlConfig = new HashMap<>();
    private Map<String, String> databaseConfig = new HashMap<>();
    private Map<String, String> validationConfig = new HashMap<>();
    private Map<String, Map<String, String>> tableEndpointConfig = new HashMap<>();
    private Map<String, List<String>> constraintConfig = new HashMap<>();
    
    private ExcelConfigManager() {
        this.excelUtils = new ExcelUtils();
        this.configFilePath = "config/Framework_Configuration.xlsx";
        loadAllConfigurations();
    }
    
    public static synchronized ExcelConfigManager getInstance() {
        if (instance == null) {
            instance = new ExcelConfigManager();
        }
        return instance;
    }
    
    /**
     * Load all configurations from Excel file
     */
    public void loadAllConfigurations() {
        log.info("Loading configurations from Excel file: {}", configFilePath);
        
        try {
            loadGeneralConfiguration();
            loadServiceConfiguration();
            loadUrlConfiguration();
            loadDatabaseConfiguration();
            loadValidationConfiguration();
            loadTableEndpointConfiguration();
            loadConstraintConfiguration();
            
            log.info("✅ All configurations loaded successfully from Excel");
            
        } catch (Exception e) {
            log.error("❌ Error loading configurations from Excel: {}", e.getMessage());
            loadDefaultConfigurations();
        }
    }
    
    /**
     * Load general framework configuration
     */
    private void loadGeneralConfiguration() {
        log.info("Loading general configuration from 'General_Config' sheet");
        
        try {
            int row = 2; // Start from row 2 (assuming headers in row 1)
            String configKey;
            
            while ((configKey = excelUtils.getCellData(configFilePath, "General_Config", row, 1)) != null 
                   && !configKey.trim().isEmpty()) {
                
                String configValue = excelUtils.getCellData(configFilePath, "General_Config", row, 2);
                String description = excelUtils.getCellData(configFilePath, "General_Config", row, 3);
                
                generalConfig.put(configKey.trim(), configValue != null ? configValue.trim() : "");
                
                log.debug("Loaded general config: {} = {} ({})", configKey, configValue, description);
                row++;
            }
            
            log.info("✅ Loaded {} general configuration items", generalConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load general configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load service and table configuration
     */
    private void loadServiceConfiguration() {
        log.info("Loading service configuration from 'Service_Config' sheet");
        
        try {
            int row = 2;
            String serviceName;
            
            while ((serviceName = excelUtils.getCellData(configFilePath, "Service_Config", row, 1)) != null 
                   && !serviceName.trim().isEmpty()) {
                
                String tablesStr = excelUtils.getCellData(configFilePath, "Service_Config", row, 2);
                String pattern = excelUtils.getCellData(configFilePath, "Service_Config", row, 3);
                String description = excelUtils.getCellData(configFilePath, "Service_Config", row, 4);
                
                // Parse tables
                List<String> tables = new ArrayList<>();
                if (tablesStr != null && !tablesStr.trim().isEmpty()) {
                    String[] tableArray = tablesStr.split(",");
                    for (String table : tableArray) {
                        tables.add(table.trim());
                    }
                }
                
                serviceTablesConfig.put(serviceName.trim(), tables);
                servicePatternConfig.put(serviceName.trim(), pattern != null ? pattern.trim() : "direct");
                
                log.debug("Loaded service config: {} = {} tables, pattern: {} ({})", 
                         serviceName, tables.size(), pattern, description);
                row++;
            }
            
            log.info("✅ Loaded {} service configurations", serviceTablesConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load service configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load URL configuration
     */
    private void loadUrlConfiguration() {
        log.info("Loading URL configuration from 'URL_Config' sheet");
        
        try {
            int row = 2;
            String urlKey;
            
            while ((urlKey = excelUtils.getCellData(configFilePath, "URL_Config", row, 1)) != null 
                   && !urlKey.trim().isEmpty()) {
                
                String urlValue = excelUtils.getCellData(configFilePath, "URL_Config", row, 2);
                String description = excelUtils.getCellData(configFilePath, "URL_Config", row, 3);
                
                urlConfig.put(urlKey.trim(), urlValue != null ? urlValue.trim() : "");
                
                log.debug("Loaded URL config: {} = {} ({})", urlKey, urlValue, description);
                row++;
            }
            
            log.info("✅ Loaded {} URL configurations", urlConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load URL configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load database configuration
     */
    private void loadDatabaseConfiguration() {
        log.info("Loading database configuration from 'Database_Config' sheet");
        
        try {
            int row = 2;
            String dbKey;
            
            while ((dbKey = excelUtils.getCellData(configFilePath, "Database_Config", row, 1)) != null 
                   && !dbKey.trim().isEmpty()) {
                
                String dbValue = excelUtils.getCellData(configFilePath, "Database_Config", row, 2);
                String description = excelUtils.getCellData(configFilePath, "Database_Config", row, 3);
                
                databaseConfig.put(dbKey.trim(), dbValue != null ? dbValue.trim() : "");
                
                log.debug("Loaded database config: {} = {} ({})", dbKey, dbValue, description);
                row++;
            }
            
            log.info("✅ Loaded {} database configurations", databaseConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load database configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load validation configuration
     */
    private void loadValidationConfiguration() {
        log.info("Loading validation configuration from 'Validation_Config' sheet");
        
        try {
            int row = 2;
            String validationKey;
            
            while ((validationKey = excelUtils.getCellData(configFilePath, "Validation_Config", row, 1)) != null 
                   && !validationKey.trim().isEmpty()) {
                
                String validationValue = excelUtils.getCellData(configFilePath, "Validation_Config", row, 2);
                String description = excelUtils.getCellData(configFilePath, "Validation_Config", row, 3);
                
                validationConfig.put(validationKey.trim(), validationValue != null ? validationValue.trim() : "");
                
                log.debug("Loaded validation config: {} = {} ({})", validationKey, validationValue, description);
                row++;
            }
            
            log.info("✅ Loaded {} validation configurations", validationConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load validation configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load table endpoint configuration
     */
    private void loadTableEndpointConfiguration() {
        log.info("Loading table endpoint configuration from 'Table_Endpoints' sheet");
        
        try {
            int row = 2;
            String tableName;
            
            while ((tableName = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 1)) != null 
                   && !tableName.trim().isEmpty()) {
                
                String postEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 2);
                String putEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 3);
                String patchEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 4);
                String getEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 5);
                String deleteEndpoint = excelUtils.getCellData(configFilePath, "Table_Endpoints", row, 6);
                
                Map<String, String> endpoints = new HashMap<>();
                endpoints.put("post", postEndpoint != null ? postEndpoint.trim() : "");
                endpoints.put("put", putEndpoint != null ? putEndpoint.trim() : "");
                endpoints.put("patch", patchEndpoint != null ? patchEndpoint.trim() : "");
                endpoints.put("get", getEndpoint != null ? getEndpoint.trim() : "");
                endpoints.put("delete", deleteEndpoint != null ? deleteEndpoint.trim() : "");
                
                tableEndpointConfig.put(tableName.trim(), endpoints);
                
                log.debug("Loaded table endpoints for: {}", tableName);
                row++;
            }
            
            log.info("✅ Loaded endpoint configurations for {} tables", tableEndpointConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load table endpoint configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load constraint configuration
     */
    private void loadConstraintConfiguration() {
        log.info("Loading constraint configuration from 'Constraint_Config' sheet");
        
        try {
            int row = 2;
            String tableName;
            
            while ((tableName = excelUtils.getCellData(configFilePath, "Constraint_Config", row, 1)) != null 
                   && !tableName.trim().isEmpty()) {
                
                String nullConstraints = excelUtils.getCellData(configFilePath, "Constraint_Config", row, 2);
                String uniqueConstraints = excelUtils.getCellData(configFilePath, "Constraint_Config", row, 3);
                String foreignKeys = excelUtils.getCellData(configFilePath, "Constraint_Config", row, 4);
                
                List<String> constraints = new ArrayList<>();
                if (nullConstraints != null && !nullConstraints.trim().isEmpty()) {
                    constraints.addAll(Arrays.asList(nullConstraints.split(",")));
                }
                if (uniqueConstraints != null && !uniqueConstraints.trim().isEmpty()) {
                    constraints.addAll(Arrays.asList(uniqueConstraints.split(",")));
                }
                if (foreignKeys != null && !foreignKeys.trim().isEmpty()) {
                    constraints.addAll(Arrays.asList(foreignKeys.split(",")));
                }
                
                constraintConfig.put(tableName.trim(), constraints);
                
                log.debug("Loaded constraints for table {}: {} constraints", tableName, constraints.size());
                row++;
            }
            
            log.info("✅ Loaded constraint configurations for {} tables", constraintConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load constraint configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load default configurations if Excel loading fails
     */
    private void loadDefaultConfigurations() {
        log.info("Loading default configurations as fallback");
        
        // Default general config
        generalConfig.put("framework.name", "Automated CRUD Testing Framework");
        generalConfig.put("framework.version", "1.0.0");
        generalConfig.put("auto.test.operations", "post,put,patch,get,delete");
        generalConfig.put("auto.test.types", "normal,null_constraint,unique_constraint,foreign_key_invalid");
        
        // Default URL config
        urlConfig.put("direct.pattern.base_url", "http://localhost:8071");
        urlConfig.put("proxy.pattern.base_url", "http://localhost:9762");
        urlConfig.put("proxy.pattern.endpoint", "/decrypt");
        
        // Default database config
        databaseConfig.put("JDBC_URL", "***************************************");
        databaseConfig.put("JDBC_USER", "testuser");
        databaseConfig.put("JDBC_PASSWORD", "testpass");
        
        // Default validation config
        validationConfig.put("validation.status_code.post", "201,200");
        validationConfig.put("validation.status_code.put", "200");
        validationConfig.put("validation.status_code.patch", "200");
        validationConfig.put("validation.status_code.get", "200");
        validationConfig.put("validation.status_code.delete", "200,204");
        validationConfig.put("validation.constraint_violation.expected_status", "400,422");
        
        log.info("✅ Default configurations loaded");
    }
    
    // Getter methods for accessing configurations
    
    public String getGeneralConfig(String key) {
        return generalConfig.getOrDefault(key, "");
    }
    
    public List<String> getTablesForService(String service) {
        return serviceTablesConfig.getOrDefault(service, new ArrayList<>());
    }
    
    public String getServicePattern(String service) {
        return servicePatternConfig.getOrDefault(service, "direct");
    }
    
    public String getUrlConfig(String key) {
        return urlConfig.getOrDefault(key, "");
    }
    
    public String getDatabaseConfig(String key) {
        return databaseConfig.getOrDefault(key, "");
    }
    
    public String getValidationConfig(String key) {
        return validationConfig.getOrDefault(key, "");
    }
    
    public String getTableEndpoint(String table, String operation) {
        Map<String, String> endpoints = tableEndpointConfig.get(table);
        if (endpoints != null) {
            String endpoint = endpoints.get(operation.toLowerCase());
            if (endpoint != null && !endpoint.trim().isEmpty()) {
                return endpoint;
            }
        }
        
        // Return default endpoint if not configured
        return "/api/" + table.toLowerCase() + "/" + getDefaultOperationEndpoint(operation);
    }
    
    public List<String> getConstraintsForTable(String table) {
        return constraintConfig.getOrDefault(table, new ArrayList<>());
    }
    
    public Set<String> getConfiguredServices() {
        return serviceTablesConfig.keySet();
    }
    
    public Set<String> getAllConfiguredTables() {
        Set<String> allTables = new HashSet<>();
        for (List<String> tables : serviceTablesConfig.values()) {
            allTables.addAll(tables);
        }
        return allTables;
    }
    
    private String getDefaultOperationEndpoint(String operation) {
        switch (operation.toLowerCase()) {
            case "post": return "save";
            case "put": return "update";
            case "patch": return "patch";
            case "get": return "getById";
            case "delete": return "delete";
            default: return operation.toLowerCase();
        }
    }
    
    /**
     * Reload configurations from Excel
     */
    public void reloadConfigurations() {
        log.info("Reloading configurations from Excel file");
        
        // Clear existing configurations
        generalConfig.clear();
        serviceTablesConfig.clear();
        servicePatternConfig.clear();
        urlConfig.clear();
        databaseConfig.clear();
        validationConfig.clear();
        tableEndpointConfig.clear();
        constraintConfig.clear();
        
        // Reload all configurations
        loadAllConfigurations();
        
        log.info("✅ Configurations reloaded successfully");
    }
    
    /**
     * Get configuration summary
     */
    public void printConfigurationSummary() {
        log.info("=== EXCEL CONFIGURATION SUMMARY ===");
        log.info("General Config Items: {}", generalConfig.size());
        log.info("Configured Services: {}", serviceTablesConfig.size());
        log.info("Total Tables: {}", getAllConfiguredTables().size());
        log.info("URL Configurations: {}", urlConfig.size());
        log.info("Database Configurations: {}", databaseConfig.size());
        log.info("Validation Configurations: {}", validationConfig.size());
        log.info("Table Endpoint Configurations: {}", tableEndpointConfig.size());
        log.info("Constraint Configurations: {}", constraintConfig.size());
        log.info("=====================================");
    }
}
