package com.rbts.tests;

import com.rbts.reporting.TestCaseResult;
import com.rbts.reporting.TestExecutionReporter;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;

/**
 * Test class to verify Excel reporting functionality
 * This test ensures that test results are properly written to Excel
 */
@Slf4j
public class ExcelReportingTest {
    
    private TestExecutionReporter testReporter;
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up Excel Reporting Test");
        
        // Create reports directory if it doesn't exist
        File reportsDir = new File("reports");
        if (!reportsDir.exists()) {
            reportsDir.mkdirs();
            log.info("📁 Created reports directory");
        }
        
        // Initialize test reporter
        testReporter = new TestExecutionReporter();
        
        log.info("✅ Excel Reporting Test setup completed");
        log.info("📊 Report will be generated at: {}", testReporter.getReportFilePath());
    }
    
    /**
     * Test successful POST operation reporting
     */
    @Test(description = "Test successful POST operation reporting to Excel")
    public void testSuccessfulPostReporting() {
        log.info("🧪 Testing successful POST operation reporting");
        
        // Create a successful test case result
        TestCaseResult successResult = TestCaseResult.builder()
                .tableName("Order.Order")
                .operation("POST")
                .testCase("Create Order - Valid Data")
                .expectedResult("Status: 201, Order created successfully")
                .actualResult("Status: 201, Order created with ID: 123")
                .status("PASS")
                .requestBody("{\"customerId\": 1, \"orderDate\": \"2024-01-01T10:00:00Z\", \"orderItems\": [{\"productId\": 1, \"quantity\": 2}]}")
                .responseBody("{\"id\": 123, \"customerId\": 1, \"orderDate\": \"2024-01-01T10:00:00Z\", \"status\": \"PENDING\", \"total\": 59.98}")
                .statusCode(201)
                .build();
        
        // Report the test case with color coding
        testReporter.reportTestCaseWithColorCoding(successResult);
        
        log.info("✅ Successful POST test case reported to Excel");
    }
    
    /**
     * Test failed POST operation reporting
     */
    @Test(description = "Test failed POST operation reporting to Excel")
    public void testFailedPostReporting() {
        log.info("🧪 Testing failed POST operation reporting");
        
        // Create a failed test case result
        TestCaseResult failResult = TestCaseResult.builder()
                .tableName("User.User")
                .operation("POST")
                .testCase("Create User - Invalid Email")
                .expectedResult("Status: 400, Invalid email format")
                .actualResult("Status: 500, Internal server error")
                .status("FAIL")
                .requestBody("{\"email\": \"invalid-email\", \"firstName\": \"John\", \"lastName\": \"Doe\"}")
                .responseBody("{\"error\": \"Internal server error\", \"message\": \"Database connection failed\"}")
                .statusCode(500)
                .errorMessage("Expected 400 but got 500")
                .build();
        
        // Report the test case with color coding
        testReporter.reportTestCaseWithColorCoding(failResult);
        
        log.info("❌ Failed POST test case reported to Excel");
    }
    
    /**
     * Test constraint violation reporting
     */
    @Test(description = "Test constraint violation reporting to Excel")
    public void testConstraintViolationReporting() {
        log.info("🧪 Testing constraint violation reporting");
        
        // Test null constraint violation
        TestCaseResult nullConstraintResult = TestCaseResult.builder()
                .tableName("State.State")
                .operation("POST")
                .testCase("Constraint Violation - null_constraint")
                .expectedResult("Status: 400, State short name cannot be null")
                .actualResult("Status: 400, State short name is required")
                .status("PASS")
                .requestBody("{\"stateShortName\": null, \"stateName\": \"California\", \"countryId\": 1}")
                .responseBody("{\"error\": \"Validation failed\", \"message\": \"State short name is required\"}")
                .statusCode(400)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(nullConstraintResult);
        
        // Test unique constraint violation
        TestCaseResult uniqueConstraintResult = TestCaseResult.builder()
                .tableName("State.State")
                .operation("POST")
                .testCase("Constraint Violation - unique_constraint")
                .expectedResult("Status: 409, State short name 'CA' already exists")
                .actualResult("Status: 409, Duplicate entry 'CA' for key 'state_short_name'")
                .status("FAIL")
                .requestBody("{\"stateShortName\": \"CA\", \"stateName\": \"California\", \"countryId\": 1}")
                .responseBody("{\"error\": \"Duplicate entry\", \"message\": \"Duplicate entry 'CA' for key 'state_short_name'\"}")
                .statusCode(409)
                .errorMessage("Error message format doesn't match expected")
                .build();
        
        testReporter.reportTestCaseWithColorCoding(uniqueConstraintResult);
        
        log.info("🔍 Constraint violation test cases reported to Excel");
    }
    
    /**
     * Test GET operation reporting
     */
    @Test(description = "Test GET operation reporting to Excel")
    public void testGetOperationReporting() {
        log.info("🧪 Testing GET operation reporting");
        
        // Test successful GET
        TestCaseResult getSuccessResult = TestCaseResult.builder()
                .tableName("Product.Product")
                .operation("GET")
                .testCase("Get Product by ID - Valid ID")
                .expectedResult("Status: 200, Product details returned")
                .actualResult("Status: 200, Product found with ID: 1")
                .status("PASS")
                .requestBody("")
                .responseBody("{\"id\": 1, \"name\": \"Test Product\", \"price\": 29.99, \"categoryId\": 1}")
                .statusCode(200)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(getSuccessResult);
        
        // Test GET not found
        TestCaseResult getNotFoundResult = TestCaseResult.builder()
                .tableName("Product.Product")
                .operation("GET")
                .testCase("Get Product by ID - Invalid ID")
                .expectedResult("Status: 404, Product not found")
                .actualResult("Status: 404, Product with ID 999 not found")
                .status("PASS")
                .requestBody("")
                .responseBody("{\"error\": \"Not found\", \"message\": \"Product with ID 999 not found\"}")
                .statusCode(404)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(getNotFoundResult);
        
        log.info("📖 GET operation test cases reported to Excel");
    }
    
    /**
     * Test PUT operation reporting
     */
    @Test(description = "Test PUT operation reporting to Excel")
    public void testPutOperationReporting() {
        log.info("🧪 Testing PUT operation reporting");
        
        TestCaseResult putResult = TestCaseResult.builder()
                .tableName("Contact.AddressType")
                .operation("PUT")
                .testCase("Update Address Type - Valid Data")
                .expectedResult("Status: 200, Address type updated successfully")
                .actualResult("Status: 200, Address type updated with ID: 1")
                .status("PASS")
                .requestBody("{\"id\": 1, \"typeName\": \"Updated Home Address\", \"description\": \"Updated description\"}")
                .responseBody("{\"id\": 1, \"typeName\": \"Updated Home Address\", \"description\": \"Updated description\", \"lastModifiedAt\": \"2024-01-01T11:00:00Z\"}")
                .statusCode(200)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(putResult);
        
        log.info("🔄 PUT operation test case reported to Excel");
    }
    
    /**
     * Test foreign key constraint reporting
     */
    @Test(description = "Test foreign key constraint reporting to Excel")
    public void testForeignKeyConstraintReporting() {
        log.info("🧪 Testing foreign key constraint reporting");
        
        // Foreign key same service violation
        TestCaseResult fkSameServiceResult = TestCaseResult.builder()
                .tableName("Authentication.UserProfile")
                .operation("POST")
                .testCase("Constraint Violation - foreign_key_same_service")
                .expectedResult("Status: 404, User with ID 999 not found")
                .actualResult("Status: 404, Referenced user does not exist")
                .status("PASS")
                .requestBody("{\"userId\": 999, \"firstName\": \"John\", \"lastName\": \"Doe\"}")
                .responseBody("{\"error\": \"Foreign key violation\", \"message\": \"Referenced user does not exist\"}")
                .statusCode(404)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(fkSameServiceResult);
        
        // Foreign key different service violation
        TestCaseResult fkDifferentServiceResult = TestCaseResult.builder()
                .tableName("Authentication.UserProfile")
                .operation("POST")
                .testCase("Constraint Violation - foreign_key_different_service")
                .expectedResult("Status: 502, Country service unavailable or country ID 999 not found")
                .actualResult("Status: 502, External service error")
                .status("PASS")
                .requestBody("{\"userId\": 1, \"countryId\": 999, \"firstName\": \"John\"}")
                .responseBody("{\"error\": \"External service error\", \"message\": \"Country service is unavailable\"}")
                .statusCode(502)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(fkDifferentServiceResult);
        
        log.info("🔗 Foreign key constraint test cases reported to Excel");
    }
    
    /**
     * Test success message validation reporting
     */
    @Test(description = "Test success message validation reporting to Excel")
    public void testSuccessMessageReporting() {
        log.info("🧪 Testing success message validation reporting");
        
        TestCaseResult successMessageResult = TestCaseResult.builder()
                .tableName("Order.Order")
                .operation("POST")
                .testCase("Success Message Validation")
                .expectedResult("Order created successfully with ID {orderId}")
                .actualResult("Order created successfully with ID 123")
                .status("PASS")
                .requestBody("{\"customerId\": 1, \"orderDate\": \"2024-01-01T10:00:00Z\"}")
                .responseBody("{\"id\": 123, \"message\": \"Order created successfully with ID 123\"}")
                .statusCode(201)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(successMessageResult);
        
        log.info("✅ Success message validation test case reported to Excel");
    }
    
    /**
     * Excel reporting test summary
     */
    @Test(description = "Excel reporting test summary", 
          dependsOnMethods = {"testSuccessfulPostReporting", "testFailedPostReporting", 
                             "testConstraintViolationReporting", "testGetOperationReporting",
                             "testPutOperationReporting", "testForeignKeyConstraintReporting",
                             "testSuccessMessageReporting"})
    public void excelReportingTestSummary() {
        log.info("📊 EXCEL REPORTING TEST SUMMARY");
        log.info("===============================");
        
        int totalTestCases = testReporter.getTotalTestCases();
        int totalDefects = testReporter.getTotalDefects();
        String reportFile = testReporter.getReportFilePath();
        
        log.info("📋 Total Test Cases Reported: {}", totalTestCases);
        log.info("🐛 Total Defects Generated: {}", totalDefects);
        log.info("📁 Excel Report File: {}", reportFile);
        
        // Check if report file exists
        File reportFileObj = new File(reportFile);
        if (reportFileObj.exists()) {
            log.info("✅ Excel report file created successfully");
            log.info("📊 File size: {} bytes", reportFileObj.length());
        } else {
            log.error("❌ Excel report file not found!");
        }
        
        log.info("");
        log.info("🎯 EXCEL REPORTING FEATURES DEMONSTRATED:");
        log.info("✅ Test case ID generation (TC_TableName_Operation_001)");
        log.info("✅ Defect ID generation for failed tests (D_TableName_Operation_001)");
        log.info("✅ Color coding (Green for PASS, Red for FAIL)");
        log.info("✅ Comprehensive test data recording:");
        log.info("   • Table Name and Operation");
        log.info("   • Test Case Description");
        log.info("   • Expected vs Actual Results");
        log.info("   • Request and Response Bodies");
        log.info("   • Status Codes and Error Messages");
        log.info("   • Execution Timestamps");
        log.info("✅ Multiple operation types (POST, GET, PUT)");
        log.info("✅ Constraint violation testing");
        log.info("✅ Foreign key constraint testing");
        log.info("✅ Success and error message validation");
        
        log.info("");
        log.info("📋 TEST CASES REPORTED TO EXCEL:");
        log.info("1. ✅ Successful POST operation (Order creation)");
        log.info("2. ❌ Failed POST operation (User creation with invalid email)");
        log.info("3. ✅ Null constraint violation (State short name)");
        log.info("4. ❌ Unique constraint violation (Duplicate state short name)");
        log.info("5. ✅ Successful GET operation (Product retrieval)");
        log.info("6. ✅ GET not found (Invalid product ID)");
        log.info("7. ✅ Successful PUT operation (Address type update)");
        log.info("8. ✅ Foreign key same service violation (Invalid user ID)");
        log.info("9. ✅ Foreign key different service violation (Invalid country ID)");
        log.info("10. ✅ Success message validation (Order creation message)");
        
        log.info("");
        log.info("🚀 NEXT STEPS:");
        log.info("1. Open Excel file: {}", reportFile);
        log.info("2. Review test results with color coding");
        log.info("3. Check defect IDs for failed test cases");
        log.info("4. Use this reporting in your actual API tests");
        
        log.info("");
        log.info("✅ EXCEL REPORTING TEST COMPLETED SUCCESSFULLY!");
        log.info("📊 All test results have been written to Excel with proper formatting!");
    }
}
