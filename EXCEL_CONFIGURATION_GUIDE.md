# 📊 Excel-Based Dynamic Configuration System

## 🎯 **Why Excel Configuration?**

✅ **More Dynamic** - Change configuration without code changes  
✅ **User-Friendly** - Non-technical users can update settings  
✅ **Visual** - See all configurations in organized sheets  
✅ **Flexible** - Add/remove services and tables easily  
✅ **Collaborative** - Share and review configurations with team  
✅ **Version Control** - Track configuration changes over time  

## 🚀 **How to Use Excel Configuration**

### **Step 1: Generate Excel Configuration Template**

```bash
mvn test -Dtest=ExcelConfigurationTest#generateExcelConfigurationTemplate
```

This creates: `config/Framework_Configuration.xlsx`

### **Step 2: Open and Configure Excel File**

The Excel file contains **8 sheets**:

#### 📋 **Sheet 1: General_Config**
Framework-wide settings

| Configuration Key | Configuration Value | Description |
|------------------|-------------------|-------------|
| framework.name | Automated CRUD Testing Framework | Name of the framework |
| framework.version | 1.0.0 | Version of the framework |
| auto.test.operations | post,put,patch,get,delete | CRUD operations to test |
| auto.test.types | normal,null_constraint,unique_constraint,foreign_key_invalid | Types of tests |

#### 🏢 **Sheet 2: Service_Config**
Your services and tables

| Service Name | Tables (comma-separated) | API Pattern | Description |
|-------------|-------------------------|-------------|-------------|
| contact | AddressType,ContactType,Address,Contact | proxy | Contact management service |
| authentication | User,Role,Permission,UserRole | proxy | Authentication service |
| core | Country,State,City,Currency | direct | Core master data service |
| **YOUR_SERVICE** | **YOUR_TABLES** | **direct/proxy** | **Your service description** |

#### 🌐 **Sheet 3: URL_Config**
API URLs and patterns

| URL Key | URL Value | Description |
|---------|-----------|-------------|
| direct.pattern.base_url | http://localhost:8071 | Base URL for direct API |
| proxy.pattern.base_url | http://localhost:9762 | Base URL for proxy API |
| proxy.pattern.endpoint | /decrypt | Proxy endpoint |
| proxy.pattern.tenant_id | your_tenant_id | Tenant ID for proxy |

#### 🗄️ **Sheet 4: Database_Config**
Database connection settings

| Database Key | Database Value | Description |
|-------------|---------------|-------------|
| JDBC_URL | **************************************** | Database URL |
| JDBC_USER | your_username | Database username |
| JDBC_PASSWORD | your_password | Database password |
| JDBC_DRIVER | org.postgresql.Driver | JDBC driver |

#### ✅ **Sheet 5: Validation_Config**
Test validation rules

| Validation Key | Expected Values | Description |
|---------------|----------------|-------------|
| validation.status_code.post | 201,200 | Expected POST status codes |
| validation.status_code.put | 200 | Expected PUT status codes |
| validation.constraint_violation.expected_status | 400,422 | Constraint violation codes |

#### 🔗 **Sheet 6: Table_Endpoints**
Table-specific endpoints

| Table Name | POST Endpoint | PUT Endpoint | PATCH Endpoint | GET Endpoint | DELETE Endpoint |
|-----------|---------------|--------------|----------------|--------------|-----------------|
| AddressType | /contact/api/AddressType/save | /contact/api/AddressType/update | /contact/api/AddressType/patch | /contact/api/AddressType/getById | /contact/api/AddressType/delete |
| **YOUR_TABLE** | **YOUR_POST_ENDPOINT** | **YOUR_PUT_ENDPOINT** | **YOUR_PATCH_ENDPOINT** | **YOUR_GET_ENDPOINT** | **YOUR_DELETE_ENDPOINT** |

#### 🔒 **Sheet 7: Constraint_Config**
Database constraints for testing

| Table Name | Null Constraints | Unique Constraints | Foreign Keys |
|-----------|-----------------|-------------------|--------------|
| AddressType | type,isActive | type | |
| User | username,email,firstName | username,email | |
| Address | addressLine1,city | | addressTypeId,countryId |

#### 📖 **Sheet 8: Instructions**
How to use each sheet

## 🎯 **Step 3: Update Configuration for Your Application**

### **Update Service_Config Sheet:**
```
Service Name: your_service_name
Tables: Table1,Table2,Table3,Table4
API Pattern: direct (or proxy)
Description: Your service description
```

### **Update URL_Config Sheet:**
```
direct.pattern.base_url: http://your-api-server:port
proxy.pattern.base_url: http://your-proxy-server:port
proxy.pattern.tenant_id: your_actual_tenant_id
```

### **Update Database_Config Sheet:**
```
JDBC_URL: ***************************************************
JDBC_USER: your_actual_username
JDBC_PASSWORD: your_actual_password
```

### **Update Table_Endpoints Sheet:**
Add rows for each of your tables with their specific endpoints.

### **Update Constraint_Config Sheet:**
Add constraint information for each table to enable constraint testing.

## 🚀 **Step 4: Test Excel Configuration**

```bash
mvn test -Dtest=ExcelConfigurationTest
```

**Expected Output:**
```
=== EXCEL CONFIGURATION TEST SUMMARY ===
✅ Excel template generation - PASSED
✅ Configuration loading - PASSED
✅ Service and table configuration - PASSED
✅ Table endpoint configuration - PASSED
✅ Validation configuration - PASSED
✅ Configuration reload - PASSED
=== ALL EXCEL CONFIGURATION TESTS PASSED ===
```

## 🎯 **Step 5: Run Automated Tests with Excel Config**

```bash
mvn test -Dtest=AutomatedCrudTest
```

The framework will automatically:
- ✅ Load configuration from Excel
- ✅ Test all configured services and tables
- ✅ Use configured endpoints and patterns
- ✅ Apply configured validation rules
- ✅ Generate comprehensive documentation

## 📊 **Benefits of Excel Configuration**

### **🔄 Dynamic Updates**
- Change configuration without recompiling code
- Add new services and tables instantly
- Update URLs and endpoints on the fly

### **👥 Team Collaboration**
- Non-technical team members can update configuration
- Easy to review and approve configuration changes
- Share configuration files across teams

### **📋 Visual Organization**
- See all configuration in organized sheets
- Easy to understand relationships between services and tables
- Clear documentation of what's being tested

### **🔍 Easy Maintenance**
- No need to edit multiple property files
- All configuration in one place
- Easy to backup and version control

## 🎯 **Example: Adding a New Service**

### **1. Open Excel File**
Open `config/Framework_Configuration.xlsx`

### **2. Add to Service_Config Sheet**
```
Service Name: order_management
Tables: Order,OrderItem,OrderStatus,Payment,Shipment
API Pattern: proxy
Description: Order management and processing service
```

### **3. Add to Table_Endpoints Sheet**
```
Order | /order/api/Order/save | /order/api/Order/update | ... | ... | ...
OrderItem | /order/api/OrderItem/save | /order/api/OrderItem/update | ... | ... | ...
```

### **4. Add to Constraint_Config Sheet**
```
Order | orderNumber,customerId,orderDate | orderNumber | customerId
OrderItem | orderId,productId,quantity | | orderId,productId
```

### **5. Save and Run Tests**
```bash
mvn test -Dtest=AutomatedCrudTest
```

**Result:** Framework automatically tests all Order service tables! 🎉

## 🔧 **Advanced Configuration**

### **Custom Validation Rules**
Add custom validation rules in Validation_Config sheet:
```
validation.custom.timeout: 60
validation.custom.retry_count: 3
validation.custom.expected_fields: id,createdDate,updatedDate
```

### **Environment-Specific Configuration**
Create multiple Excel files:
- `Framework_Configuration_DEV.xlsx`
- `Framework_Configuration_QA.xlsx`
- `Framework_Configuration_PROD.xlsx`

### **Pattern-Specific Settings**
Configure different settings for direct vs proxy patterns:
```
direct.pattern.timeout: 30
proxy.pattern.timeout: 60
direct.pattern.auth_header: Authorization
proxy.pattern.auth_header: X-Auth-Token
```

## 🎉 **The Result**

With Excel configuration, you get:

✅ **Zero Code Changes** - Update configuration without touching code  
✅ **Maximum Flexibility** - Add/remove services and tables instantly  
✅ **Team Friendly** - Anyone can update configuration  
✅ **Visual Clarity** - See all settings in organized sheets  
✅ **Easy Maintenance** - All configuration in one place  
✅ **Version Control** - Track configuration changes over time  

**Just update Excel → Run tests → Get comprehensive CRUD testing!** 🚀

## 📋 **Quick Start Checklist**

- [ ] Generate Excel template: `mvn test -Dtest=ExcelConfigurationTest#generateExcelConfigurationTemplate`
- [ ] Open `config/Framework_Configuration.xlsx`
- [ ] Update Service_Config with your services and tables
- [ ] Update URL_Config with your API URLs
- [ ] Update Database_Config with your database details
- [ ] Update Table_Endpoints with your specific endpoints
- [ ] Update Constraint_Config with your table constraints
- [ ] Test configuration: `mvn test -Dtest=ExcelConfigurationTest`
- [ ] Run automated tests: `mvn test -Dtest=AutomatedCrudTest`
- [ ] Review generated documentation in `data/` directory

**Configuration is now 100% Excel-driven and dynamic!** 📊🎯
