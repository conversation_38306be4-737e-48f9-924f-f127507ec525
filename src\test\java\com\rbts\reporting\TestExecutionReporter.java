package com.rbts.reporting;

import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Test execution reporter for generating test results in Excel
 */
@Slf4j
public class TestExecutionReporter {
    
    private final ExcelUtils excelUtils;
    private final String reportFilePath;
    private final AtomicInteger testCaseCounter;
    private final AtomicInteger defectCounter;
    private int currentRow;
    
    public TestExecutionReporter() {
        this.excelUtils = new ExcelUtils();
        this.reportFilePath = "reports/Test_Execution_Report.xlsx";
        this.testCaseCounter = new AtomicInteger(1);
        this.defectCounter = new AtomicInteger(1);
        this.currentRow = 2; // Start after header row
        initializeReportSheet();
    }
    
    /**
     * Initialize the test execution report sheet with headers
     */
    private void initializeReportSheet() {
        log.info("Initializing test execution report sheet: {}", reportFilePath);
        
        try {
            String sheetName = "Test_Execution_Report";
            
            // Create headers
            excelUtils.setCellData(reportFilePath, sheetName, 1, 1, "TestCaseId");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 2, "TableName");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 3, "TestCase");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 4, "ExpectedResult");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 5, "ActualResult");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 6, "Status");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 7, "DefectId");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 8, "ExecutionTime");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 9, "Operation");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 10, "RequestBody");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 11, "ResponseBody");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 12, "StatusCode");
            
            log.info("✅ Test execution report sheet initialized successfully");
            
        } catch (Exception e) {
            log.error("❌ Error initializing test execution report sheet: {}", e.getMessage());
        }
    }
    
    /**
     * Report a test case execution result
     */
    public void reportTestCase(TestCaseResult testResult) {
        try {
            String sheetName = "Test_Execution_Report";

            // Generate test case ID
            String testCaseId = generateTestCaseId(testResult.getTableName(), testResult.getOperation());

            // Generate defect ID if test failed
            String defectId = "";
            if ("FAIL".equals(testResult.getStatus())) {
                defectId = generateDefectId(testResult.getTableName(), testResult.getOperation());
            }

            // Write test case data with retry mechanism for file locking
            writeTestCaseDataWithRetry(sheetName, testCaseId, testResult, defectId);

            currentRow++;

            log.info("📝 Test case reported: {} - {} - {}", testCaseId, testResult.getTableName(), testResult.getStatus());

        } catch (Exception e) {
            log.error("❌ Error reporting test case: {}", e.getMessage());
            // Log the test case to console as fallback
            logTestCaseToConsole(testResult);
        }
    }

    /**
     * Write test case data with retry mechanism for file locking issues
     */
    private void writeTestCaseDataWithRetry(String sheetName, String testCaseId, TestCaseResult testResult, String defectId) {
        int maxRetries = 3;
        int retryDelay = 1000; // 1 second

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // Write test case data
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 1, testCaseId);
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 2, testResult.getTableName());
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 3, testResult.getTestCase());
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 4, testResult.getExpectedResult());
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 5, testResult.getActualResult());
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 6, testResult.getStatus());
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 7, defectId);
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 8, getCurrentTimestamp());
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 9, testResult.getOperation());
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 10, testResult.getRequestBody());
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 11, testResult.getResponseBody());
                excelUtils.setCellData(reportFilePath, sheetName, currentRow, 12, String.valueOf(testResult.getStatusCode()));

                // If we reach here, writing was successful
                return;

            } catch (Exception e) {
                if (attempt < maxRetries) {
                    log.warn("⚠️ Attempt {} failed to write to Excel (file may be locked), retrying in {}ms: {}",
                            attempt, retryDelay, e.getMessage());
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.error("❌ Failed to write to Excel after {} attempts: {}", maxRetries, e.getMessage());
                    throw e;
                }
            }
        }
    }

    /**
     * Log test case to console as fallback when Excel writing fails
     */
    private void logTestCaseToConsole(TestCaseResult testResult) {
        log.info("📋 FALLBACK CONSOLE REPORT:");
        log.info("   Table: {}", testResult.getTableName());
        log.info("   Operation: {}", testResult.getOperation());
        log.info("   Test Case: {}", testResult.getTestCase());
        log.info("   Expected: {}", testResult.getExpectedResult());
        log.info("   Actual: {}", testResult.getActualResult());
        log.info("   Status: {}", testResult.getStatus());
        log.info("   Status Code: {}", testResult.getStatusCode());
        log.info("   Request: {}", testResult.getRequestBody());
        log.info("   Response: {}", testResult.getResponseBody());
    }
    
    /**
     * Generate unique test case ID
     */
    private String generateTestCaseId(String tableName, String operation) {
        int caseNumber = testCaseCounter.getAndIncrement();
        return String.format("TC_%s_%s_%03d", tableName, operation.toUpperCase(), caseNumber);
    }
    
    /**
     * Generate defect ID for failed test cases
     */
    private String generateDefectId(String tableName, String operation) {
        int defectNumber = defectCounter.getAndIncrement();
        return String.format("D_%s_%s_%03d", tableName, operation.toUpperCase(), defectNumber);
    }
    
    /**
     * Get current timestamp
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * Report POST operation test case
     */
    public void reportPostTestCase(String tableName, String testCase, String expectedResult, 
                                  String actualResult, String status, String requestBody, 
                                  String responseBody, int statusCode) {
        TestCaseResult result = TestCaseResult.builder()
                .tableName(tableName)
                .operation("POST")
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody)
                .responseBody(responseBody)
                .statusCode(statusCode)
                .build();
        
        reportTestCase(result);
    }
    
    /**
     * Report PUT operation test case
     */
    public void reportPutTestCase(String tableName, String testCase, String expectedResult, 
                                 String actualResult, String status, String requestBody, 
                                 String responseBody, int statusCode) {
        TestCaseResult result = TestCaseResult.builder()
                .tableName(tableName)
                .operation("PUT")
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody)
                .responseBody(responseBody)
                .statusCode(statusCode)
                .build();
        
        reportTestCase(result);
    }
    
    /**
     * Report GET operation test case
     */
    public void reportGetTestCase(String tableName, String testCase, String expectedResult, 
                                 String actualResult, String status, String responseBody, int statusCode) {
        TestCaseResult result = TestCaseResult.builder()
                .tableName(tableName)
                .operation("GET")
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody("")
                .responseBody(responseBody)
                .statusCode(statusCode)
                .build();
        
        reportTestCase(result);
    }
    
    /**
     * Report constraint violation test case
     */
    public void reportConstraintViolationTestCase(String tableName, String constraintType, 
                                                 String expectedStatus, String actualStatus, 
                                                 String requestBody, String responseBody, int statusCode) {
        String testCase = String.format("Constraint Violation - %s", constraintType);
        String expectedResult = String.format("Status Code: %s", expectedStatus);
        String actualResult = String.format("Status Code: %d", statusCode);
        String status = expectedStatus.equals(String.valueOf(statusCode)) ? "PASS" : "FAIL";
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName(tableName)
                .operation("POST")
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody)
                .responseBody(responseBody)
                .statusCode(statusCode)
                .build();
        
        reportTestCase(result);
    }
    
    /**
     * Get report file path
     */
    public String getReportFilePath() {
        return reportFilePath;
    }
    
    /**
     * Get total test cases reported
     */
    public int getTotalTestCases() {
        return testCaseCounter.get() - 1;
    }
    
    /**
     * Get total defects reported
     */
    public int getTotalDefects() {
        return defectCounter.get() - 1;
    }

    /**
     * Report test case with color coding (Green for PASS, Red for FAIL)
     */
    public void reportTestCaseWithColorCoding(TestCaseResult testResult) {
        try {
            // First report the test case normally
            reportTestCase(testResult);

            // Then apply color coding
            applyColorCoding(currentRow - 1, testResult.getStatus());

        } catch (Exception e) {
            log.error("❌ Error reporting test case with color coding: {}", e.getMessage());
        }
    }

    /**
     * Apply color coding to Excel row based on test status
     */
    private void applyColorCoding(int row, String status) {
        try {
            String sheetName = "Test_Execution_Report";

            // Use ExcelUtils to apply color coding with retry mechanism
            applyColorCodingWithRetry(sheetName, row, status);

        } catch (Exception e) {
            log.warn("⚠️ Could not apply color coding (file may be locked): {}", e.getMessage());
        }
    }

    /**
     * Apply color coding with retry mechanism for file locking issues
     */
    private void applyColorCodingWithRetry(String sheetName, int row, String status) {
        int maxRetries = 2;
        int retryDelay = 500; // 0.5 seconds

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if ("PASS".equals(status)) {
                    excelUtils.setCellBackgroundColor(reportFilePath, sheetName, row, 6, "GREEN");
                } else if ("FAIL".equals(status)) {
                    excelUtils.setCellBackgroundColor(reportFilePath, sheetName, row, 6, "RED");
                }
                return; // Success

            } catch (Exception e) {
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    throw e;
                }
            }
        }
    }

    /**
     * Report constraint violation test with color coding
     */
    public void reportConstraintViolationWithColorCoding(String tableName, String constraintType,
                                                        String expectedStatus, String actualStatus,
                                                        String requestBody, String responseBody, int statusCode) {
        String testCase = String.format("Constraint Violation - %s", constraintType);
        String expectedResult = String.format("Status Code: %s", expectedStatus);
        String actualResult = String.format("Status Code: %d", statusCode);
        String status = expectedStatus.equals(String.valueOf(statusCode)) ? "PASS" : "FAIL";

        TestCaseResult result = TestCaseResult.builder()
                .tableName(tableName)
                .operation("POST")
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody)
                .responseBody(responseBody)
                .statusCode(statusCode)
                .build();

        reportTestCaseWithColorCoding(result);
    }

    /**
     * Report message validation test with color coding
     */
    public void reportMessageValidationWithColorCoding(String tableName, String operation,
                                                      String messageType, String expectedMessage,
                                                      String actualMessage, String responseBody, int statusCode) {
        String testCase = String.format("Message Validation - %s", messageType);
        String status = expectedMessage.equals(actualMessage) ? "PASS" : "FAIL";

        TestCaseResult result = TestCaseResult.builder()
                .tableName(tableName)
                .operation(operation)
                .testCase(testCase)
                .expectedResult(expectedMessage)
                .actualResult(actualMessage)
                .status(status)
                .responseBody(responseBody)
                .statusCode(statusCode)
                .build();

        reportTestCaseWithColorCoding(result);
    }
}
