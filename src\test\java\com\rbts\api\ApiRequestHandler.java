package com.rbts.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.rbts.config.ConfigManager;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static io.restassured.RestAssured.given;

/**
 * API Request Handler for CRUD Testing Framework
 * Handles both direct and proxy API request patterns
 */
@Slf4j
public class ApiRequestHandler {
    
    private final ConfigManager configManager;
    private final ObjectMapper objectMapper;
    
    public ApiRequestHandler() {
        this.configManager = ConfigManager.getInstance();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Execute API request based on entity pattern (direct or proxy)
     */
    public Response executeRequest(String entity, String operation, String requestBody) {
        if (configManager.isDirectPattern(entity)) {
            return executeDirectRequest(entity, operation, requestBody);
        } else {
            return executeProxyRequest(entity, operation, requestBody);
        }
    }
    
    /**
     * Execute direct API request (Pattern 1)
     * URL: http://localhost:8071/api/field-configs
     * Headers: Custom headers + Bearer token
     * Body: Direct JSON payload
     */
    private Response executeDirectRequest(String entity, String operation, String requestBody) {
        try {
            String baseUrl = configManager.getDirectBaseUrl();
            String endpoint = configManager.getDirectEndpoint(operation, entity);
            String fullUrl = baseUrl + endpoint;
            
            log.info("Executing direct {} request for entity: {} at URL: {}", operation.toUpperCase(), entity, fullUrl);
            
            // Create request specification
            RequestSpecification requestSpec = given()
                    .contentType("application/json");
            
            // Add headers
            Map<String, String> headers = configManager.getDirectHeaders();
            for (Map.Entry<String, String> header : headers.entrySet()) {
                requestSpec.header(header.getKey(), header.getValue());
            }
            
            // Add authentication
            String authType = configManager.getAuthType();
            if ("bearer".equalsIgnoreCase(authType)) {
                String bearerToken = configManager.getBearerToken();
                if (bearerToken != null && !bearerToken.trim().isEmpty()) {
                    requestSpec.header("Authorization", "Bearer " + bearerToken);
                }
            }
            
            // Add request body for non-GET requests
            if (!operation.equalsIgnoreCase("get")) {
                requestSpec.body(requestBody);
            }
            
            // Log request details
            requestSpec.log().headers().log().body();
            
            // Execute request based on operation
            Response response = executeHttpRequest(requestSpec, operation, fullUrl);
            
            log.info("Direct {} request completed. Status: {}", operation.toUpperCase(), response.getStatusCode());
            return response;
            
        } catch (Exception e) {
            log.error("Error executing direct request for entity {}: {}", entity, e.getMessage());
            throw new RuntimeException("Direct API request failed", e);
        }
    }
    
    /**
     * Execute proxy API request (Pattern 2)
     * URL: http://localhost:9762/decrypt (same for all operations)
     * Body: Wrapper containing endpoint, payload, type, tenantId, auth
     */
    private Response executeProxyRequest(String entity, String operation, String requestBody) {
        try {
            String baseUrl = configManager.getProxyBaseUrl();
            String endpoint = configManager.getProxyEndpoint();
            String fullUrl = baseUrl + endpoint;
            String internalEndpoint = configManager.getProxyInternalEndpoint(operation, entity);
            String tenantId = configManager.getTenantId();
            
            log.info("Executing proxy {} request for entity: {} at URL: {} with internal endpoint: {}", 
                    operation.toUpperCase(), entity, fullUrl, internalEndpoint);
            
            // Create proxy request body
            ObjectNode proxyRequestBody = createProxyRequestBody(internalEndpoint, requestBody, operation, tenantId);
            String proxyRequestBodyString = objectMapper.writeValueAsString(proxyRequestBody);
            
            log.debug("Proxy request body: {}", proxyRequestBodyString);
            
            // Create request specification
            RequestSpecification requestSpec = given()
                    .contentType("application/json")
                    .body(proxyRequestBodyString)
                    .log().headers()
                    .log().body();
            
            // Execute POST request (proxy always uses POST)
            Response response = requestSpec.post(fullUrl);
            
            log.info("Proxy {} request completed. Status: {}", operation.toUpperCase(), response.getStatusCode());
            return response;
            
        } catch (Exception e) {
            log.error("Error executing proxy request for entity {}: {}", entity, e.getMessage());
            throw new RuntimeException("Proxy API request failed", e);
        }
    }
    
    /**
     * Create proxy request body wrapper
     */
    private ObjectNode createProxyRequestBody(String endpoint, String payload, String operation, String tenantId) {
        try {
            ObjectNode proxyBody = objectMapper.createObjectNode();
            proxyBody.put("endpoint", endpoint);
            proxyBody.put("type", operation.toLowerCase());
            proxyBody.put("tenantId", tenantId);
            proxyBody.putNull("auth"); // Set to null as per example
            
            // Parse and set payload
            if (payload != null && !payload.trim().isEmpty()) {
                Object payloadObject = objectMapper.readValue(payload, Object.class);
                proxyBody.set("payload", objectMapper.valueToTree(payloadObject));
            } else {
                proxyBody.putNull("payload");
            }
            
            return proxyBody;
            
        } catch (Exception e) {
            log.error("Error creating proxy request body: {}", e.getMessage());
            throw new RuntimeException("Failed to create proxy request body", e);
        }
    }
    
    /**
     * Execute HTTP request based on operation type
     */
    private Response executeHttpRequest(RequestSpecification requestSpec, String operation, String url) {
        switch (operation.toLowerCase()) {
            case "get":
                return requestSpec.get(url);
            case "post":
                return requestSpec.post(url);
            case "put":
                return requestSpec.put(url);
            case "patch":
                return requestSpec.patch(url);
            case "delete":
                return requestSpec.delete(url);
            default:
                throw new IllegalArgumentException("Unsupported operation: " + operation);
        }
    }
    
    /**
     * Execute GET request with ID parameter
     */
    public Response executeGetRequest(String entity, Object id) {
        if (configManager.isDirectPattern(entity)) {
            return executeDirectGetRequest(entity, id);
        } else {
            return executeProxyGetRequest(entity, id);
        }
    }
    
    /**
     * Execute direct GET request with ID
     */
    private Response executeDirectGetRequest(String entity, Object id) {
        String baseUrl = configManager.getDirectBaseUrl();
        String endpoint = configManager.getDirectEndpoint("get", entity);
        String fullUrl = baseUrl + endpoint + "/" + id;
        
        RequestSpecification requestSpec = given()
                .contentType("application/json");
        
        // Add headers and authentication
        Map<String, String> headers = configManager.getDirectHeaders();
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestSpec.header(header.getKey(), header.getValue());
        }
        
        String bearerToken = configManager.getBearerToken();
        if (bearerToken != null && !bearerToken.trim().isEmpty()) {
            requestSpec.header("Authorization", "Bearer " + bearerToken);
        }
        
        return requestSpec.get(fullUrl);
    }
    
    /**
     * Execute proxy GET request with ID
     */
    private Response executeProxyGetRequest(String entity, Object id) {
        try {
            String baseUrl = configManager.getProxyBaseUrl();
            String endpoint = configManager.getProxyEndpoint();
            String fullUrl = baseUrl + endpoint;
            String internalEndpoint = configManager.getProxyInternalEndpoint("get", entity) + "/" + id;
            String tenantId = configManager.getTenantId();
            
            // Create proxy request body for GET with ID
            ObjectNode proxyRequestBody = objectMapper.createObjectNode();
            proxyRequestBody.put("endpoint", internalEndpoint);
            proxyRequestBody.put("type", "get");
            proxyRequestBody.put("tenantId", tenantId);
            proxyRequestBody.putNull("auth");
            proxyRequestBody.putNull("payload");
            
            String proxyRequestBodyString = objectMapper.writeValueAsString(proxyRequestBody);
            
            return given()
                    .contentType("application/json")
                    .body(proxyRequestBodyString)
                    .post(fullUrl);
                    
        } catch (Exception e) {
            log.error("Error executing proxy GET request: {}", e.getMessage());
            throw new RuntimeException("Proxy GET request failed", e);
        }
    }
    
    /**
     * Validate if entity and operation are supported
     */
    public boolean isSupported(String entity, String operation) {
        if (configManager.isDirectPattern(entity)) {
            return configManager.getDirectEndpoint(operation, entity) != null;
        } else {
            return configManager.getProxyInternalEndpoint(operation, entity) != null;
        }
    }
}
