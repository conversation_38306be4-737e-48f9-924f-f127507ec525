package com.rbts.tests;

import com.rbts.config.ExcelConfigManager;
import com.rbts.config.ExcelConfigTemplateGenerator;
import com.rbts.config.FieldMapping;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

/**
 * Test for enhanced field mapping functionality
 * Tests audit fields, operation-specific field presence, and service-specific mapping sheets
 */
@Slf4j
public class EnhancedFieldMappingTest {
    
    private ExcelConfigManager configManager;
    private ExcelConfigTemplateGenerator templateGenerator;
    
    @BeforeClass
    public void setUp() {
        templateGenerator = new ExcelConfigTemplateGenerator();
        log.info("Enhanced Field Mapping Test initialized");
    }
    
    /**
     * Generate Excel template and test enhanced field mapping functionality
     */
    @Test(description = "Test enhanced field mapping with audit fields", priority = 1)
    public void testEnhancedFieldMappingFunctionality() {
        log.info("Testing enhanced field mapping functionality with audit fields");
        
        try {
            // Generate Excel template with service-specific field mapping sheets
            templateGenerator.generateConfigurationTemplate();
            
            // Initialize config manager to load the Excel configuration
            configManager = ExcelConfigManager.getInstance();
            
            // Test Contact service field mappings
            testContactServiceFieldMappings();
            
            // Test Authentication service field mappings with audit fields
            testAuthenticationServiceFieldMappings();
            
            // Test audit field functionality
            testAuditFieldFunctionality();
            
            // Test operation-specific field presence
            testOperationSpecificFieldPresence();
            
            log.info("✅ Enhanced field mapping functionality working correctly");
            
        } catch (Exception e) {
            log.error("❌ Error testing enhanced field mapping functionality: {}", e.getMessage());
            Assert.fail("Enhanced field mapping functionality test failed: " + e.getMessage());
        }
    }
    
    private void testContactServiceFieldMappings() {
        log.info("🔍 Testing Contact service field mappings...");
        
        // Test AddressType field mappings from FieldMapping_Contact sheet
        FieldMapping addressTypeIdMapping = configManager.getFieldMapping("AddressType", "address_type_id");
        Assert.assertNotNull(addressTypeIdMapping, "AddressType.address_type_id field mapping should exist");
        Assert.assertEquals(addressTypeIdMapping.getApiRequestField(), "addressTypeId", "API request field should be addressTypeId");
        Assert.assertEquals(addressTypeIdMapping.getApiResponseField(), "id", "API response field should be id");
        Assert.assertTrue(addressTypeIdMapping.isPrimaryKey(), "address_type_id should be identified as primary key");
        
        // Test audit fields for AddressType
        FieldMapping createdByMapping = configManager.getFieldMapping("AddressType", "created_by");
        Assert.assertNotNull(createdByMapping, "AddressType.created_by field mapping should exist");
        Assert.assertEquals(createdByMapping.getFieldType(), "AUDIT_FIELD", "Field type should be AUDIT_FIELD");
        Assert.assertTrue(createdByMapping.isAuditField(), "created_by should be identified as audit field");
        Assert.assertTrue(createdByMapping.isCreationAuditField(), "created_by should be identified as creation audit field");
        
        // Test that created_by is not present in requests but present in responses
        Assert.assertFalse(createdByMapping.isPresentInRequest("POST"), "created_by should not be present in POST request");
        Assert.assertTrue(createdByMapping.isPresentInResponse("POST"), "created_by should be present in POST response");
        
        log.info("✅ Contact service field mappings working correctly");
    }
    
    private void testAuthenticationServiceFieldMappings() {
        log.info("🔍 Testing Authentication service field mappings...");
        
        // Test User field mappings from FieldMapping_Auth sheet
        FieldMapping userIdMapping = configManager.getFieldMapping("User", "user_id");
        Assert.assertNotNull(userIdMapping, "User.user_id field mapping should exist");
        Assert.assertEquals(userIdMapping.getApiRequestField(), "userId", "API request field should be userId");
        Assert.assertEquals(userIdMapping.getApiResponseField(), "id", "API response field should be id");
        Assert.assertTrue(userIdMapping.isPrimaryKey(), "user_id should be identified as primary key");
        
        // Test email field mapping
        FieldMapping emailMapping = configManager.getFieldMapping("User", "email_address");
        Assert.assertNotNull(emailMapping, "User.email_address field mapping should exist");
        Assert.assertEquals(emailMapping.getApiRequestField(), "email", "API request field should be email");
        Assert.assertEquals(emailMapping.getApiResponseField(), "emailAddress", "API response field should be emailAddress");
        Assert.assertTrue(emailMapping.isEmailField(), "email_address should be identified as email field");
        
        // Test modification audit fields
        FieldMapping modifiedByMapping = configManager.getFieldMapping("User", "modified_by");
        Assert.assertNotNull(modifiedByMapping, "User.modified_by field mapping should exist");
        Assert.assertEquals(modifiedByMapping.getFieldType(), "AUDIT_FIELD", "Field type should be AUDIT_FIELD");
        Assert.assertTrue(modifiedByMapping.isAuditField(), "modified_by should be identified as audit field");
        Assert.assertTrue(modifiedByMapping.isModificationAuditField(), "modified_by should be identified as modification audit field");
        
        // Test that modified_by is present in PUT/PATCH requests and responses
        Assert.assertTrue(modifiedByMapping.isPresentInRequest("PUT"), "modified_by should be present in PUT request");
        Assert.assertTrue(modifiedByMapping.isPresentInRequest("PATCH"), "modified_by should be present in PATCH request");
        Assert.assertFalse(modifiedByMapping.isPresentInRequest("POST"), "modified_by should not be present in POST request");
        Assert.assertTrue(modifiedByMapping.isPresentInResponse("PUT"), "modified_by should be present in PUT response");
        
        log.info("✅ Authentication service field mappings working correctly");
    }
    
    private void testAuditFieldFunctionality() {
        log.info("🔍 Testing audit field functionality...");
        
        // Test audit field identification for AddressType
        List<String> addressTypeAuditFields = configManager.getAuditFields("AddressType");
        Assert.assertTrue(addressTypeAuditFields.contains("created_by"), "AddressType audit fields should contain created_by");
        Assert.assertTrue(addressTypeAuditFields.contains("created_at"), "AddressType audit fields should contain created_at");
        Assert.assertTrue(addressTypeAuditFields.contains("modified_by"), "AddressType audit fields should contain modified_by");
        Assert.assertTrue(addressTypeAuditFields.contains("modified_at"), "AddressType audit fields should contain modified_at");
        
        // Test creation audit fields
        List<String> creationAuditFields = configManager.getCreationAuditFields("AddressType");
        Assert.assertTrue(creationAuditFields.contains("created_by"), "Creation audit fields should contain created_by");
        Assert.assertTrue(creationAuditFields.contains("created_at"), "Creation audit fields should contain created_at");
        Assert.assertFalse(creationAuditFields.contains("modified_by"), "Creation audit fields should not contain modified_by");
        
        // Test modification audit fields
        List<String> modificationAuditFields = configManager.getModificationAuditFields("User");
        Assert.assertTrue(modificationAuditFields.contains("modified_by"), "Modification audit fields should contain modified_by");
        Assert.assertTrue(modificationAuditFields.contains("last_modified_at"), "Modification audit fields should contain last_modified_at");
        Assert.assertFalse(modificationAuditFields.contains("created_by"), "Modification audit fields should not contain created_by");
        
        log.info("✅ Audit field functionality working correctly");
    }
    
    private void testOperationSpecificFieldPresence() {
        log.info("🔍 Testing operation-specific field presence...");
        
        // Test fields present in POST request (should not include audit fields)
        List<String> postRequestFields = configManager.getFieldsPresentInRequest("AddressType", "POST");
        Assert.assertTrue(postRequestFields.contains("type_name"), "POST request should include type_name");
        Assert.assertFalse(postRequestFields.contains("created_by"), "POST request should not include created_by");
        Assert.assertFalse(postRequestFields.contains("created_at"), "POST request should not include created_at");
        Assert.assertFalse(postRequestFields.contains("modified_by"), "POST request should not include modified_by");
        
        // Test fields present in POST response (should include creation audit fields)
        List<String> postResponseFields = configManager.getFieldsPresentInResponse("AddressType", "POST");
        Assert.assertTrue(postResponseFields.contains("type_name"), "POST response should include type_name");
        Assert.assertTrue(postResponseFields.contains("created_by"), "POST response should include created_by");
        Assert.assertTrue(postResponseFields.contains("created_at"), "POST response should include created_at");
        Assert.assertFalse(postResponseFields.contains("modified_by"), "POST response should not include modified_by (not set yet)");
        
        // Test fields present in PUT request (should include modification audit fields)
        List<String> putRequestFields = configManager.getFieldsPresentInRequest("User", "PUT");
        Assert.assertTrue(putRequestFields.contains("email_address"), "PUT request should include email_address");
        Assert.assertTrue(putRequestFields.contains("modified_by"), "PUT request should include modified_by");
        Assert.assertTrue(putRequestFields.contains("last_modified_at"), "PUT request should include last_modified_at");
        Assert.assertFalse(putRequestFields.contains("created_by"), "PUT request should not include created_by");
        
        // Test fields present in PUT response (should include all audit fields)
        List<String> putResponseFields = configManager.getFieldsPresentInResponse("User", "PUT");
        Assert.assertTrue(putResponseFields.contains("email_address"), "PUT response should include email_address");
        Assert.assertTrue(putResponseFields.contains("created_by"), "PUT response should include created_by");
        Assert.assertTrue(putResponseFields.contains("created_at"), "PUT response should include created_at");
        Assert.assertTrue(putResponseFields.contains("modified_by"), "PUT response should include modified_by");
        Assert.assertTrue(putResponseFields.contains("last_modified_at"), "PUT response should include last_modified_at");

        // Test fields present in GET response (should include all audit fields)
        List<String> getResponseFields = configManager.getFieldsPresentInResponse("User", "GET");
        Assert.assertTrue(getResponseFields.contains("email_address"), "GET response should include email_address");
        Assert.assertTrue(getResponseFields.contains("created_by"), "GET response should include created_by");
        Assert.assertTrue(getResponseFields.contains("created_at"), "GET response should include created_at");
        Assert.assertTrue(getResponseFields.contains("modified_by"), "GET response should include modified_by");
        Assert.assertTrue(getResponseFields.contains("last_modified_at"), "GET response should include last_modified_at");
        
        // Test individual field presence checking
        Assert.assertFalse(configManager.isFieldPresentInRequest("AddressType", "created_by", "POST"), 
                "created_by should not be present in POST request");
        Assert.assertTrue(configManager.isFieldPresentInResponse("AddressType", "created_by", "POST"), 
                "created_by should be present in POST response");
        Assert.assertTrue(configManager.isFieldPresentInRequest("User", "modified_by", "PUT"), 
                "modified_by should be present in PUT request");
        
        log.info("✅ Operation-specific field presence working correctly");
    }
    
    /**
     * Test summary
     */
    @Test(description = "Enhanced field mapping test summary", dependsOnMethods = "testEnhancedFieldMappingFunctionality", priority = 2)
    public void testSummary() {
        log.info("=== ENHANCED FIELD MAPPING TEST SUMMARY ===");
        log.info("✅ Contact service field mappings - PASSED");
        log.info("✅ Authentication service field mappings - PASSED");
        log.info("✅ Audit field functionality - PASSED");
        log.info("✅ Operation-specific field presence - PASSED");
        log.info("=== ALL ENHANCED FIELD MAPPING TESTS PASSED ===");
        
        log.info("🎉 Enhanced field mapping functionality is working perfectly!");
        log.info("📋 Key Features:");
        log.info("   ✅ Service-specific field mapping sheets");
        log.info("   ✅ Audit field support (createdBy, createdAt, modifiedBy, lastModifiedAt)");
        log.info("   ✅ Operation-specific field presence (POST request vs response)");
        log.info("   ✅ Automatic audit field identification");
        log.info("   ✅ Smart field filtering based on operation type");
        
        log.info("📊 Real-world Benefits:");
        log.info("   🎯 Accurate audit field handling in API testing");
        log.info("   🎯 Proper validation of creation vs modification scenarios");
        log.info("   🎯 Service-organized field mapping configuration");
        log.info("   🎯 Support for complex audit trail requirements");
        
        // Print service-specific field mapping counts
        Map<String, FieldMapping> contactMappings = configManager.getFieldMappingsForTable("AddressType");
        log.info("📋 Contact service (AddressType) field mappings: {}", contactMappings.size());
        
        Map<String, FieldMapping> authMappings = configManager.getFieldMappingsForTable("User");
        log.info("📋 Authentication service (User) field mappings: {}", authMappings.size());
        
        // Print audit field counts
        List<String> auditFields = configManager.getAuditFields("AddressType");
        log.info("📋 AddressType audit fields: {}", auditFields.size());
        
        List<String> creationFields = configManager.getCreationAuditFields("AddressType");
        log.info("📋 AddressType creation audit fields: {}", creationFields.size());
        
        List<String> modificationFields = configManager.getModificationAuditFields("User");
        log.info("📋 User modification audit fields: {}", modificationFields.size());
        
        Assert.assertTrue(true, "All enhanced field mapping tests passed successfully");
    }
}
