package com.rbts.tests;

import com.rbts.config.ExcelConfigManager;
import com.rbts.config.ExcelConfigTemplateGenerator;
import com.rbts.config.FieldMapping;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

/**
 * Test for field mapping functionality
 * Verifies Excel-based field mapping between database and API fields
 */
@Slf4j
public class FieldMappingTest {
    
    private ExcelConfigManager configManager;
    private ExcelConfigTemplateGenerator templateGenerator;
    
    @BeforeClass
    public void setUp() {
        templateGenerator = new ExcelConfigTemplateGenerator();
        log.info("Field Mapping Test initialized");
    }
    
    /**
     * Generate Excel template and test field mapping functionality
     */
    @Test(description = "Test field mapping functionality", priority = 1)
    public void testFieldMappingFunctionality() {
        log.info("Testing field mapping functionality");
        
        try {
            // Generate Excel template with field mapping examples
            templateGenerator.generateConfigurationTemplate();
            
            // Initialize config manager to load the Excel configuration
            configManager = ExcelConfigManager.getInstance();
            
            // Test User table field mappings
            testUserTableFieldMappings();
            
            // Test Order table field mappings
            testOrderTableFieldMappings();
            
            // Test AddressType table field mappings
            testAddressTypeTableFieldMappings();
            
            log.info("✅ Field mapping functionality working correctly");
            
        } catch (Exception e) {
            log.error("❌ Error testing field mapping functionality: {}", e.getMessage());
            Assert.fail("Field mapping functionality test failed: " + e.getMessage());
        }
    }
    
    private void testUserTableFieldMappings() {
        log.info("🔍 Testing User table field mappings...");
        
        // Test user_id field mapping
        FieldMapping userIdMapping = configManager.getFieldMapping("User", "user_id");
        Assert.assertNotNull(userIdMapping, "user_id field mapping should exist");
        Assert.assertEquals(userIdMapping.getApiRequestField(), "userId", "API request field should be userId");
        Assert.assertEquals(userIdMapping.getApiResponseField(), "id", "API response field should be id");
        Assert.assertEquals(userIdMapping.getFieldType(), "PRIMARY_KEY", "Field type should be PRIMARY_KEY");
        Assert.assertTrue(userIdMapping.isPrimaryKey(), "user_id should be identified as primary key");
        
        // Test first_name field mapping
        FieldMapping firstNameMapping = configManager.getFieldMapping("User", "first_name");
        Assert.assertNotNull(firstNameMapping, "first_name field mapping should exist");
        Assert.assertEquals(firstNameMapping.getApiRequestField(), "firstName", "API request field should be firstName");
        Assert.assertEquals(firstNameMapping.getApiResponseField(), "firstName", "API response field should be firstName");
        Assert.assertEquals(firstNameMapping.getFieldType(), "STRING", "Field type should be STRING");
        
        // Test email_address field mapping
        FieldMapping emailMapping = configManager.getFieldMapping("User", "email_address");
        Assert.assertNotNull(emailMapping, "email_address field mapping should exist");
        Assert.assertEquals(emailMapping.getApiRequestField(), "email", "API request field should be email");
        Assert.assertEquals(emailMapping.getApiResponseField(), "emailAddress", "API response field should be emailAddress");
        Assert.assertEquals(emailMapping.getFieldType(), "EMAIL", "Field type should be EMAIL");
        Assert.assertTrue(emailMapping.isEmailField(), "email_address should be identified as email field");
        
        // Test created_date field mapping
        FieldMapping createdDateMapping = configManager.getFieldMapping("User", "created_date");
        Assert.assertNotNull(createdDateMapping, "created_date field mapping should exist");
        Assert.assertEquals(createdDateMapping.getApiRequestField(), "createdDate", "API request field should be createdDate");
        Assert.assertEquals(createdDateMapping.getApiResponseField(), "createdAt", "API response field should be createdAt");
        Assert.assertEquals(createdDateMapping.getFieldType(), "TIMESTAMP", "Field type should be TIMESTAMP");
        Assert.assertTrue(createdDateMapping.isTimestampField(), "created_date should be identified as timestamp field");
        
        log.info("✅ User table field mappings working correctly");
    }
    
    private void testOrderTableFieldMappings() {
        log.info("🔍 Testing Order table field mappings...");
        
        // Test order_id field mapping
        FieldMapping orderIdMapping = configManager.getFieldMapping("Order", "order_id");
        Assert.assertNotNull(orderIdMapping, "order_id field mapping should exist");
        Assert.assertEquals(orderIdMapping.getApiRequestField(), "orderId", "API request field should be orderId");
        Assert.assertEquals(orderIdMapping.getApiResponseField(), "id", "API response field should be id");
        Assert.assertTrue(orderIdMapping.isPrimaryKey(), "order_id should be identified as primary key");
        
        // Test user_id field mapping (foreign key)
        FieldMapping userIdMapping = configManager.getFieldMapping("Order", "user_id");
        Assert.assertNotNull(userIdMapping, "user_id field mapping should exist");
        Assert.assertEquals(userIdMapping.getApiRequestField(), "customerId", "API request field should be customerId");
        Assert.assertEquals(userIdMapping.getApiResponseField(), "userId", "API response field should be userId");
        Assert.assertEquals(userIdMapping.getFieldType(), "FOREIGN_KEY", "Field type should be FOREIGN_KEY");
        Assert.assertTrue(userIdMapping.isForeignKey(), "user_id should be identified as foreign key");
        
        // Test order_total field mapping
        FieldMapping orderTotalMapping = configManager.getFieldMapping("Order", "order_total");
        Assert.assertNotNull(orderTotalMapping, "order_total field mapping should exist");
        Assert.assertEquals(orderTotalMapping.getApiRequestField(), "totalAmount", "API request field should be totalAmount");
        Assert.assertEquals(orderTotalMapping.getApiResponseField(), "total", "API response field should be total");
        Assert.assertEquals(orderTotalMapping.getFieldType(), "DECIMAL", "Field type should be DECIMAL");
        Assert.assertTrue(orderTotalMapping.isDecimalField(), "order_total should be identified as decimal field");
        
        log.info("✅ Order table field mappings working correctly");
    }
    
    private void testAddressTypeTableFieldMappings() {
        log.info("🔍 Testing AddressType table field mappings...");
        
        // Test address_type_id field mapping
        FieldMapping addressTypeIdMapping = configManager.getFieldMapping("AddressType", "address_type_id");
        Assert.assertNotNull(addressTypeIdMapping, "address_type_id field mapping should exist");
        Assert.assertEquals(addressTypeIdMapping.getApiRequestField(), "addressTypeId", "API request field should be addressTypeId");
        Assert.assertEquals(addressTypeIdMapping.getApiResponseField(), "id", "API response field should be id");
        Assert.assertTrue(addressTypeIdMapping.isPrimaryKey(), "address_type_id should be identified as primary key");
        
        // Test type_name field mapping
        FieldMapping typeNameMapping = configManager.getFieldMapping("AddressType", "type_name");
        Assert.assertNotNull(typeNameMapping, "type_name field mapping should exist");
        Assert.assertEquals(typeNameMapping.getApiRequestField(), "typeName", "API request field should be typeName");
        Assert.assertEquals(typeNameMapping.getApiResponseField(), "name", "API response field should be name");
        Assert.assertEquals(typeNameMapping.getFieldType(), "STRING", "Field type should be STRING");
        
        log.info("✅ AddressType table field mappings working correctly");
    }
    
    /**
     * Test field mapping utility methods
     */
    @Test(description = "Test field mapping utility methods", dependsOnMethods = "testFieldMappingFunctionality", priority = 2)
    public void testFieldMappingUtilityMethods() {
        log.info("Testing field mapping utility methods");
        
        try {
            // Test API request field name retrieval
            String userIdRequestField = configManager.getApiRequestFieldName("User", "user_id");
            Assert.assertEquals(userIdRequestField, "userId", "API request field name should be userId");
            
            String emailRequestField = configManager.getApiRequestFieldName("User", "email_address");
            Assert.assertEquals(emailRequestField, "email", "API request field name should be email");
            
            // Test API response field name retrieval
            String userIdResponseField = configManager.getApiResponseFieldName("User", "user_id");
            Assert.assertEquals(userIdResponseField, "id", "API response field name should be id");
            
            String emailResponseField = configManager.getApiResponseFieldName("User", "email_address");
            Assert.assertEquals(emailResponseField, "emailAddress", "API response field name should be emailAddress");
            
            // Test database field name retrieval from API request field
            String dbFieldFromRequest = configManager.getDatabaseFieldNameFromRequest("User", "userId");
            Assert.assertEquals(dbFieldFromRequest, "user_id", "Database field name should be user_id");
            
            // Test database field name retrieval from API response field
            String dbFieldFromResponse = configManager.getDatabaseFieldNameFromResponse("User", "id");
            Assert.assertEquals(dbFieldFromResponse, "user_id", "Database field name should be user_id");
            
            // Test primary key identification
            boolean isUserIdPrimaryKey = configManager.isPrimaryKeyField("User", "user_id");
            Assert.assertTrue(isUserIdPrimaryKey, "user_id should be identified as primary key");
            
            boolean isFirstNamePrimaryKey = configManager.isPrimaryKeyField("User", "first_name");
            Assert.assertFalse(isFirstNamePrimaryKey, "first_name should not be identified as primary key");
            
            // Test foreign key identification
            boolean isOrderUserIdForeignKey = configManager.isForeignKeyField("Order", "user_id");
            Assert.assertTrue(isOrderUserIdForeignKey, "Order.user_id should be identified as foreign key");
            
            // Test primary key fields retrieval
            List<String> userPrimaryKeys = configManager.getPrimaryKeyFields("User");
            Assert.assertTrue(userPrimaryKeys.contains("user_id"), "User primary keys should contain user_id");
            
            List<String> orderPrimaryKeys = configManager.getPrimaryKeyFields("Order");
            Assert.assertTrue(orderPrimaryKeys.contains("order_id"), "Order primary keys should contain order_id");
            
            // Test foreign key fields retrieval
            List<String> orderForeignKeys = configManager.getForeignKeyFields("Order");
            Assert.assertTrue(orderForeignKeys.contains("user_id"), "Order foreign keys should contain user_id");
            
            log.info("✅ Field mapping utility methods working correctly");
            
        } catch (Exception e) {
            log.error("❌ Error testing field mapping utility methods: {}", e.getMessage());
            Assert.fail("Field mapping utility methods test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test field mapping for non-existent fields (fallback behavior)
     */
    @Test(description = "Test field mapping fallback behavior", dependsOnMethods = "testFieldMappingUtilityMethods", priority = 3)
    public void testFieldMappingFallbackBehavior() {
        log.info("Testing field mapping fallback behavior");
        
        try {
            // Test non-existent table
            FieldMapping nonExistentTableMapping = configManager.getFieldMapping("NonExistentTable", "some_field");
            Assert.assertNull(nonExistentTableMapping, "Non-existent table mapping should return null");
            
            // Test non-existent field in existing table
            FieldMapping nonExistentFieldMapping = configManager.getFieldMapping("User", "non_existent_field");
            Assert.assertNull(nonExistentFieldMapping, "Non-existent field mapping should return null");
            
            // Test fallback behavior for API request field name
            String fallbackRequestField = configManager.getApiRequestFieldName("User", "non_existent_field");
            Assert.assertEquals(fallbackRequestField, "non_existent_field", "Should return database field name as fallback");
            
            // Test fallback behavior for API response field name
            String fallbackResponseField = configManager.getApiResponseFieldName("User", "non_existent_field");
            Assert.assertEquals(fallbackResponseField, "non_existent_field", "Should return database field name as fallback");
            
            // Test fallback behavior for database field name from request
            String fallbackDbFromRequest = configManager.getDatabaseFieldNameFromRequest("User", "unknownRequestField");
            Assert.assertEquals(fallbackDbFromRequest, "unknownRequestField", "Should return API field name as fallback");
            
            // Test fallback behavior for database field name from response
            String fallbackDbFromResponse = configManager.getDatabaseFieldNameFromResponse("User", "unknownResponseField");
            Assert.assertEquals(fallbackDbFromResponse, "unknownResponseField", "Should return API field name as fallback");
            
            log.info("✅ Field mapping fallback behavior working correctly");
            
        } catch (Exception e) {
            log.error("❌ Error testing field mapping fallback behavior: {}", e.getMessage());
            Assert.fail("Field mapping fallback behavior test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test summary
     */
    @Test(description = "Field mapping test summary", dependsOnMethods = {"testFieldMappingFunctionality", "testFieldMappingUtilityMethods", "testFieldMappingFallbackBehavior"}, priority = 4)
    public void testSummary() {
        log.info("=== FIELD MAPPING TEST SUMMARY ===");
        log.info("✅ Field mapping functionality - PASSED");
        log.info("✅ Field mapping utility methods - PASSED");
        log.info("✅ Field mapping fallback behavior - PASSED");
        log.info("=== ALL FIELD MAPPING TESTS PASSED ===");
        
        log.info("🎉 Field mapping functionality is working perfectly!");
        log.info("📋 How it works:");
        log.info("   ✅ Database fields mapped to API request fields");
        log.info("   ✅ Database fields mapped to API response fields");
        log.info("   ✅ Field types identified (PRIMARY_KEY, FOREIGN_KEY, EMAIL, etc.)");
        log.info("   ✅ Automatic fallback to original field names when mapping not found");
        
        log.info("📊 Benefits:");
        log.info("   🎯 Accurate field matching between database and API");
        log.info("   🎯 Support for different naming conventions");
        log.info("   🎯 Automatic field type identification");
        log.info("   🎯 Easy Excel-based configuration");
        
        // Print field mapping configuration summary
        Map<String, FieldMapping> userMappings = configManager.getFieldMappingsForTable("User");
        log.info("📋 User table field mappings: {}", userMappings.size());
        
        Map<String, FieldMapping> orderMappings = configManager.getFieldMappingsForTable("Order");
        log.info("📋 Order table field mappings: {}", orderMappings.size());
        
        Map<String, FieldMapping> addressTypeMappings = configManager.getFieldMappingsForTable("AddressType");
        log.info("📋 AddressType table field mappings: {}", addressTypeMappings.size());
        
        Assert.assertTrue(true, "All field mapping tests passed successfully");
    }
}
