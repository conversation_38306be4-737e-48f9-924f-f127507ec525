package com.rbts.database;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;

/**
 * Database schema reader that connects to the actual database
 * and reads table structures to generate correct payloads
 */
@Slf4j
public class DatabaseSchemaReader {
    
    private final String databaseUrl;
    private final String username;
    private final String password;
    
    public DatabaseSchemaReader(String databaseUrl, String username, String password) {
        this.databaseUrl = databaseUrl;
        this.username = username;
        this.password = password;
    }
    
    /**
     * Get table schema information from database
     */
    public TableSchema getTableSchema(String tableName) {
        TableSchema schema = new TableSchema(tableName);
        
        try (Connection connection = DriverManager.getConnection(databaseUrl, username, password)) {
            log.info("🔍 Reading schema for table: {}", tableName);
            
            // Get table columns
            DatabaseMetaData metaData = connection.getMetaData();
            
            // Get columns
            try (ResultSet columns = metaData.getColumns(null, null, tableName, null)) {
                while (columns.next()) {
                    String columnName = columns.getString("COLUMN_NAME");
                    String dataType = columns.getString("TYPE_NAME");
                    int columnSize = columns.getInt("COLUMN_SIZE");
                    boolean isNullable = "YES".equals(columns.getString("IS_NULLABLE"));
                    boolean isAutoIncrement = "YES".equals(columns.getString("IS_AUTOINCREMENT"));
                    String defaultValue = columns.getString("COLUMN_DEF");
                    
                    ColumnInfo columnInfo = new ColumnInfo(
                            columnName, dataType, columnSize, isNullable, isAutoIncrement, defaultValue
                    );
                    
                    schema.addColumn(columnInfo);
                    log.debug("📋 Column: {} - Type: {} - Nullable: {} - AutoIncrement: {}", 
                            columnName, dataType, isNullable, isAutoIncrement);
                }
            }
            
            // Get primary keys
            try (ResultSet primaryKeys = metaData.getPrimaryKeys(null, null, tableName)) {
                while (primaryKeys.next()) {
                    String pkColumn = primaryKeys.getString("COLUMN_NAME");
                    schema.addPrimaryKey(pkColumn);
                    log.debug("🔑 Primary Key: {}", pkColumn);
                }
            }
            
            // Get foreign keys
            try (ResultSet foreignKeys = metaData.getImportedKeys(null, null, tableName)) {
                while (foreignKeys.next()) {
                    String fkColumn = foreignKeys.getString("FKCOLUMN_NAME");
                    String pkTable = foreignKeys.getString("PKTABLE_NAME");
                    String pkColumn = foreignKeys.getString("PKCOLUMN_NAME");
                    
                    ForeignKeyInfo fkInfo = new ForeignKeyInfo(fkColumn, pkTable, pkColumn);
                    schema.addForeignKey(fkInfo);
                    log.debug("🔗 Foreign Key: {} -> {}.{}", fkColumn, pkTable, pkColumn);
                }
            }
            
            // Get unique constraints
            try (ResultSet indexes = metaData.getIndexInfo(null, null, tableName, true, false)) {
                while (indexes.next()) {
                    String indexName = indexes.getString("INDEX_NAME");
                    String columnName = indexes.getString("COLUMN_NAME");
                    boolean nonUnique = indexes.getBoolean("NON_UNIQUE");
                    
                    if (!nonUnique && !"PRIMARY".equals(indexName)) {
                        schema.addUniqueConstraint(columnName);
                        log.debug("🎯 Unique Constraint: {}", columnName);
                    }
                }
            }
            
            log.info("✅ Schema read successfully for table: {} - {} columns", tableName, schema.getColumns().size());
            
        } catch (SQLException e) {
            log.error("❌ Error reading schema for table {}: {}", tableName, e.getMessage());
        }
        
        return schema;
    }
    
    /**
     * Get all table names from database
     */
    public List<String> getAllTableNames() {
        List<String> tableNames = new ArrayList<>();
        
        try (Connection connection = DriverManager.getConnection(databaseUrl, username, password)) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            try (ResultSet tables = metaData.getTables(null, null, "%", new String[]{"TABLE"})) {
                while (tables.next()) {
                    String tableName = tables.getString("TABLE_NAME");
                    tableNames.add(tableName);
                }
            }
            
            log.info("📋 Found {} tables in database", tableNames.size());
            
        } catch (SQLException e) {
            log.error("❌ Error getting table names: {}", e.getMessage());
        }
        
        return tableNames;
    }
    
    /**
     * Check if table exists in database
     */
    public boolean tableExists(String tableName) {
        try (Connection connection = DriverManager.getConnection(databaseUrl, username, password)) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            try (ResultSet tables = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                return tables.next();
            }
            
        } catch (SQLException e) {
            log.error("❌ Error checking if table {} exists: {}", tableName, e.getMessage());
            return false;
        }
    }
    
    /**
     * Table schema information
     */
    public static class TableSchema {
        private final String tableName;
        private final List<ColumnInfo> columns;
        private final List<String> primaryKeys;
        private final List<ForeignKeyInfo> foreignKeys;
        private final List<String> uniqueConstraints;
        
        public TableSchema(String tableName) {
            this.tableName = tableName;
            this.columns = new ArrayList<>();
            this.primaryKeys = new ArrayList<>();
            this.foreignKeys = new ArrayList<>();
            this.uniqueConstraints = new ArrayList<>();
        }
        
        public void addColumn(ColumnInfo column) {
            columns.add(column);
        }
        
        public void addPrimaryKey(String columnName) {
            primaryKeys.add(columnName);
        }
        
        public void addForeignKey(ForeignKeyInfo foreignKey) {
            foreignKeys.add(foreignKey);
        }
        
        public void addUniqueConstraint(String columnName) {
            uniqueConstraints.add(columnName);
        }
        
        // Getters
        public String getTableName() { return tableName; }
        public List<ColumnInfo> getColumns() { return columns; }
        public List<String> getPrimaryKeys() { return primaryKeys; }
        public List<ForeignKeyInfo> getForeignKeys() { return foreignKeys; }
        public List<String> getUniqueConstraints() { return uniqueConstraints; }
        
        public ColumnInfo getColumn(String columnName) {
            return columns.stream()
                    .filter(col -> col.getColumnName().equalsIgnoreCase(columnName))
                    .findFirst()
                    .orElse(null);
        }
        
        public boolean isPrimaryKey(String columnName) {
            return primaryKeys.stream().anyMatch(pk -> pk.equalsIgnoreCase(columnName));
        }
        
        public boolean isForeignKey(String columnName) {
            return foreignKeys.stream().anyMatch(fk -> fk.getColumnName().equalsIgnoreCase(columnName));
        }
        
        public boolean isUniqueConstraint(String columnName) {
            return uniqueConstraints.stream().anyMatch(uc -> uc.equalsIgnoreCase(columnName));
        }
    }
    
    /**
     * Column information
     */
    public static class ColumnInfo {
        private final String columnName;
        private final String dataType;
        private final int columnSize;
        private final boolean isNullable;
        private final boolean isAutoIncrement;
        private final String defaultValue;
        
        public ColumnInfo(String columnName, String dataType, int columnSize, 
                         boolean isNullable, boolean isAutoIncrement, String defaultValue) {
            this.columnName = columnName;
            this.dataType = dataType;
            this.columnSize = columnSize;
            this.isNullable = isNullable;
            this.isAutoIncrement = isAutoIncrement;
            this.defaultValue = defaultValue;
        }
        
        // Getters
        public String getColumnName() { return columnName; }
        public String getDataType() { return dataType; }
        public int getColumnSize() { return columnSize; }
        public boolean isNullable() { return isNullable; }
        public boolean isAutoIncrement() { return isAutoIncrement; }
        public String getDefaultValue() { return defaultValue; }
    }
    
    /**
     * Foreign key information
     */
    public static class ForeignKeyInfo {
        private final String columnName;
        private final String referencedTable;
        private final String referencedColumn;
        
        public ForeignKeyInfo(String columnName, String referencedTable, String referencedColumn) {
            this.columnName = columnName;
            this.referencedTable = referencedTable;
            this.referencedColumn = referencedColumn;
        }
        
        // Getters
        public String getColumnName() { return columnName; }
        public String getReferencedTable() { return referencedTable; }
        public String getReferencedColumn() { return referencedColumn; }
    }
}
