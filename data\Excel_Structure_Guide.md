# Excel Test Data Structure Guide

## 📊 Excel File Structure for CRUD Testing Framework

### File Location
- **Path**: `data/api_test_data.xlsx`
- **Sheet Name**: `API_Tests`

### Column Structure

| Column | Name | Description | Example |
|--------|------|-------------|---------|
| A (1) | URL | API endpoint URL | `/api/user/save` |
| B (2) | Request Body | JSON request payload | `{"username":"test","email":"<EMAIL>"}` |
| C (3) | Expected Result | Expected JSON response | `{"user_id":1,"username":"test","email":"<EMAIL>"}` |
| D (4) | Actual Result | Actual response from database (auto-filled) | `{"user_id":123,"username":"test","email":"<EMAIL>"}` |
| E (5) | Status | Test result status (auto-filled) | `PASSED` / `FAILED` / `ERROR` |
| F (6) | Entity | Entity name for the test | `user` |

### Sample Data

#### Row 1 (Header)
| URL | Request Body | Expected Result | Actual Result | Status | Entity |
|-----|-------------|----------------|---------------|--------|--------|

#### Row 2 (User Test)
| URL | Request Body | Expected Result | Actual Result | Status | Entity |
|-----|-------------|----------------|---------------|--------|--------|
| `/api/user/save` | `{"username":"testuser","email":"<EMAIL>","firstName":"Test","lastName":"User"}` | `{"user_id":1,"username":"testuser","email":"<EMAIL>","firstName":"Test","lastName":"User","created_date":"2024-01-01","status":"ACTIVE"}` | [Auto-filled by framework] | [Auto-filled by framework] | `user` |

#### Row 3 (Product Test)
| URL | Request Body | Expected Result | Actual Result | Status | Entity |
|-----|-------------|----------------|---------------|--------|--------|
| `/api/product/save` | `{"product_name":"Test Product","product_code":"TP001","price":99.99,"description":"Test product","country_id":1}` | `{"product_id":1,"product_name":"Test Product","product_code":"TP001","price":99.99,"description":"Test product","country_id":1,"created_date":"2024-01-01","status":"ACTIVE"}` | [Auto-filled by framework] | [Auto-filled by framework] | `product` |

### Color Coding

The framework automatically applies color coding to the Status column:

- **🟢 Green**: PASSED - Test completed successfully
- **🔴 Red**: FAILED - Test failed (JSON mismatch or API error)
- **🟡 Yellow**: ERROR - Test execution error

### Request Body Examples

#### User Entity
```json
{
  "username": "testuser123",
  "email": "<EMAIL>",
  "firstName": "Test",
  "lastName": "User",
  "phone": "+1234567890",
  "address": "123 Test Street, Test City"
}
```

#### Product Entity
```json
{
  "product_name": "Test Product",
  "product_code": "TP001",
  "price": 99.99,
  "description": "Test product description",
  "category": "Electronics",
  "country_id": 1
}
```

#### Country Entity
```json
{
  "country_name": "Test Country",
  "country_code": "TC",
  "currency": "USD",
  "continent": "North America"
}
```

#### Order Entity
```json
{
  "order_number": "ORD_12345",
  "user_id": 1,
  "product_id": 1,
  "quantity": 2,
  "total_amount": 199.98,
  "order_date": "2024-01-01",
  "status": "PENDING"
}
```

### Expected Result Examples

#### User Entity Expected Result
```json
{
  "user_id": 1,
  "username": "testuser123",
  "email": "<EMAIL>",
  "firstName": "Test",
  "lastName": "User",
  "phone": "+1234567890",
  "address": "123 Test Street, Test City",
  "created_date": "2024-01-01T10:00:00",
  "updated_date": "2024-01-01T10:00:00",
  "status": "ACTIVE"
}
```

#### Product Entity Expected Result (with Foreign Key Resolution)
```json
{
  "product_id": 1,
  "product_name": "Test Product",
  "product_code": "TP001",
  "price": 99.99,
  "description": "Test product description",
  "category": "Electronics",
  "country": {
    "country_id": 1,
    "country_name": "United States",
    "country_code": "US",
    "currency": "USD"
  },
  "created_date": "2024-01-01T10:00:00",
  "updated_date": "2024-01-01T10:00:00",
  "status": "ACTIVE"
}
```

### Special Test Cases

#### Constraint Violation Tests
For constraint violation tests, you don't need to provide request bodies in Excel. The framework will automatically generate them based on configuration.

#### Rows for Constraint Tests
- **Row 5**: Null constraint violation test
- **Row 6**: Unique constraint violation test
- **Rows 7+**: Additional entity tests

### Framework Behavior

1. **URL Column**: If empty, framework uses configured endpoint from `config.properties`
2. **Request Body**: Must be valid JSON format
3. **Expected Result**: Can be empty - framework will pass test based on successful API response
4. **Actual Result**: Automatically filled by framework after database query
5. **Status**: Automatically filled based on test result
6. **Entity**: Used to determine which configuration to use

### Tips for Test Data Creation

1. **Use TestDataManager**: The framework includes `TestDataManager` class to generate sample data
2. **Validate JSON**: Ensure all JSON in Request Body and Expected Result columns is valid
3. **Foreign Keys**: Use valid foreign key values that exist in referenced tables
4. **Unique Values**: Ensure unique constraint fields have unique values across test data
5. **Required Fields**: Include all required fields based on null constraint configuration

### Automatic Data Generation

You can use the framework's automatic data generation:

```java
TestDataManager testDataManager = new TestDataManager();
testDataManager.createTestDataForExcel("data/api_test_data.xlsx", "API_Tests");
```

This will create a complete Excel file with sample test data for all configured entities.
