package com.rbts.constraint;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.javafaker.Faker;
import com.rbts.config.ConfigManager;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.Random;

/**
 * Constraint Test Generator for CRUD Testing Framework
 * Generates test data for null constraint and unique constraint violation testing
 */
@Slf4j
public class ConstraintTestGenerator {
    
    private final ConfigManager configManager;
    private final ObjectMapper objectMapper;
    private final Faker faker;
    private final Random random;
    
    public ConstraintTestGenerator() {
        this.configManager = ConfigManager.getInstance();
        this.objectMapper = new ObjectMapper();
        this.faker = new Faker();
        this.random = new Random();
    }
    
    /**
     * Generate request body for null constraint violation test
     */
    public String generateNullConstraintViolationRequest(String entity, String baseRequestBody) {
        try {
            JsonNode baseNode = objectMapper.readTree(baseRequestBody);
            ObjectNode testNode = baseNode.deepCopy();
            
            List<String> nullConstraintFields = configManager.getNullConstraintFields(entity);
            
            if (nullConstraintFields.isEmpty()) {
                log.warn("No null constraint fields configured for entity: {}", entity);
                return baseRequestBody;
            }
            
            // Randomly select one null constraint field to violate
            String fieldToViolate = nullConstraintFields.get(random.nextInt(nullConstraintFields.size()));
            
            // Set the field to null
            testNode.putNull(fieldToViolate);
            
            log.info("Generated null constraint violation request for entity {} by setting field '{}' to null", 
                    entity, fieldToViolate);
            
            return objectMapper.writeValueAsString(testNode);
            
        } catch (Exception e) {
            log.error("Error generating null constraint violation request for entity {}: {}", entity, e.getMessage());
            return baseRequestBody;
        }
    }
    
    /**
     * Generate request body for unique constraint violation test
     */
    public String generateUniqueConstraintViolationRequest(String entity, String baseRequestBody) {
        try {
            JsonNode baseNode = objectMapper.readTree(baseRequestBody);
            ObjectNode testNode = baseNode.deepCopy();
            
            List<String> uniqueConstraintFields = configManager.getUniqueConstraintFields(entity);
            
            if (uniqueConstraintFields.isEmpty()) {
                log.warn("No unique constraint fields configured for entity: {}", entity);
                return baseRequestBody;
            }
            
            // Randomly select one unique constraint field to violate
            String fieldToViolate = uniqueConstraintFields.get(random.nextInt(uniqueConstraintFields.size()));
            
            // Get an existing value for this field from database
            String existingValue = getExistingUniqueValue(entity, fieldToViolate);
            
            if (existingValue != null) {
                testNode.put(fieldToViolate, existingValue);
                log.info("Generated unique constraint violation request for entity {} by setting field '{}' to existing value: {}", 
                        entity, fieldToViolate, existingValue);
            } else {
                // If no existing value found, generate a duplicate within the same request
                String duplicateValue = generateDuplicateValue(fieldToViolate);
                testNode.put(fieldToViolate, duplicateValue);
                log.info("Generated unique constraint violation request for entity {} by setting field '{}' to duplicate value: {}", 
                        entity, fieldToViolate, duplicateValue);
            }
            
            return objectMapper.writeValueAsString(testNode);
            
        } catch (Exception e) {
            log.error("Error generating unique constraint violation request for entity {}: {}", entity, e.getMessage());
            return baseRequestBody;
        }
    }
    
    /**
     * Generate valid request body with all constraints satisfied
     */
    public String generateValidRequest(String entity, String baseRequestBody) {
        try {
            JsonNode baseNode = objectMapper.readTree(baseRequestBody);
            ObjectNode testNode = baseNode.deepCopy();
            
            // Ensure all null constraint fields have values
            List<String> nullConstraintFields = configManager.getNullConstraintFields(entity);
            for (String field : nullConstraintFields) {
                if (testNode.get(field) == null || testNode.get(field).isNull()) {
                    String generatedValue = generateValidValueForField(field);
                    testNode.put(field, generatedValue);
                }
            }
            
            // Ensure all unique constraint fields have unique values
            List<String> uniqueConstraintFields = configManager.getUniqueConstraintFields(entity);
            for (String field : uniqueConstraintFields) {
                String uniqueValue = generateUniqueValueForField(entity, field);
                testNode.put(field, uniqueValue);
            }
            
            log.info("Generated valid request for entity {} with all constraints satisfied", entity);
            
            return objectMapper.writeValueAsString(testNode);
            
        } catch (Exception e) {
            log.error("Error generating valid request for entity {}: {}", entity, e.getMessage());
            return baseRequestBody;
        }
    }
    
    /**
     * Get existing unique value from database
     */
    private String getExistingUniqueValue(String entity, String fieldName) {
        String tableName = configManager.getTableName(entity);
        String query = String.format("SELECT %s FROM %s WHERE %s IS NOT NULL LIMIT 1", fieldName, tableName, fieldName);
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(query);
             ResultSet resultSet = statement.executeQuery()) {
            
            if (resultSet.next()) {
                return resultSet.getString(1);
            }
            
        } catch (Exception e) {
            log.warn("Could not get existing unique value for field {} in entity {}: {}", fieldName, entity, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Generate unique value for field
     */
    private String generateUniqueValueForField(String entity, String fieldName) {
        String baseValue = generateValidValueForField(fieldName);
        String uniqueValue = baseValue;
        int counter = 1;
        
        // Keep generating until we find a unique value
        while (valueExistsInDatabase(entity, fieldName, uniqueValue)) {
            uniqueValue = baseValue + "_" + counter;
            counter++;
            
            // Prevent infinite loop
            if (counter > 1000) {
                uniqueValue = baseValue + "_" + System.currentTimeMillis();
                break;
            }
        }
        
        return uniqueValue;
    }
    
    /**
     * Check if value exists in database
     */
    private boolean valueExistsInDatabase(String entity, String fieldName, String value) {
        String tableName = configManager.getTableName(entity);
        String query = String.format("SELECT 1 FROM %s WHERE %s = ?", tableName, fieldName);
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            
            statement.setString(1, value);
            try (ResultSet resultSet = statement.executeQuery()) {
                return resultSet.next();
            }
            
        } catch (Exception e) {
            log.warn("Could not check if value exists for field {} in entity {}: {}", fieldName, entity, e.getMessage());
            return false;
        }
    }
    
    /**
     * Generate valid value for field based on field name
     */
    private String generateValidValueForField(String fieldName) {
        String lowerFieldName = fieldName.toLowerCase();
        
        if (lowerFieldName.contains("email")) {
            return faker.internet().emailAddress();
        } else if (lowerFieldName.contains("name")) {
            return faker.name().fullName();
        } else if (lowerFieldName.contains("username")) {
            return faker.name().username();
        } else if (lowerFieldName.contains("phone")) {
            return faker.phoneNumber().phoneNumber();
        } else if (lowerFieldName.contains("address")) {
            return faker.address().fullAddress();
        } else if (lowerFieldName.contains("code")) {
            return faker.code().asin();
        } else if (lowerFieldName.contains("price")) {
            return String.valueOf(faker.number().randomDouble(2, 1, 1000));
        } else if (lowerFieldName.contains("quantity")) {
            return String.valueOf(faker.number().numberBetween(1, 100));
        } else if (lowerFieldName.contains("description")) {
            return faker.lorem().sentence();
        } else {
            return faker.lorem().word() + "_" + faker.number().numberBetween(1000, 9999);
        }
    }
    
    /**
     * Generate duplicate value for testing
     */
    private String generateDuplicateValue(String fieldName) {
        return "DUPLICATE_" + generateValidValueForField(fieldName);
    }
    
    /**
     * Get database connection
     */
    private Connection getConnection() throws Exception {
        String url = configManager.getJdbcUrl();
        String user = configManager.getJdbcUser();
        String password = configManager.getJdbcPassword();
        
        return DriverManager.getConnection(url, user, password);
    }
}
