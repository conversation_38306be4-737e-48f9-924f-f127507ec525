package com.rbts.base.post;

import io.restassured.response.Response;
import lombok.*;
import org.slf4j.Logger;
import com.rbts.utils.ExcelUtils;

import static io.restassured.RestAssured.given;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class PostBasic implements ApiMethod {
    private Logger logger;
    private String filePath;
    private String sheetName;
    private int url;
    private int body;


    @Override
    public Response post(int rowNum) {

        ExcelUtils excelUtils = new ExcelUtils();
        String uri = excelUtils.getCellData(filePath, sheetName, rowNum, url);
        String body1 = excelUtils.getCellData(filePath, sheetName, rowNum, body);

        Response response = given()
                .contentType("application/json")
                .body(body1)
                .when()
                .log().headers()
                .post(uri);
        String responseBody = response.getBody().asString();
        logger.info("Request: {}", response.getHeaders());
        logger.info("ResponseBody: {}", responseBody);
        logger.info("Response: {}", response.getDetailedCookies());
        logger.info("Request: {}", response.getStatusCode());
        logger.info(response.getContentType());
        return response;
    }

}

