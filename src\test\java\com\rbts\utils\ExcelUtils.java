package com.rbts.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.io.*;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

public class ExcelUtils implements Excel {

	private static Workbook workbook;
	private static Sheet sheet;
	private static String userDirectory = System.getProperty("user.dir");

	private static void openWorkbook(String filePath)  {
		String filePath1 = userDirectory + "/" + filePath;
		File file = new File(filePath1);

		// Create parent directories if they don't exist
		File parentDir = file.getParentFile();
		if (parentDir != null && !parentDir.exists()) {
			parentDir.mkdirs();
		}

		FileInputStream inputStream = null;
		try {
			if (file.exists()) {
				inputStream = new FileInputStream(filePath1);
				workbook = new XSSFWorkbook(inputStream);
				inputStream.close();
			} else {
				// Create new workbook if file doesn't exist
				workbook = new XSSFWorkbook();
			}
		} catch (FileNotFoundException e) {
			// Create new workbook if file not found
			workbook = new XSSFWorkbook();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	public int getRowCount(String filePath, String sheetName) {
		try {
			openWorkbook(filePath);
			sheet = workbook.getSheet(sheetName);
			return sheet.getLastRowNum();
		} finally {
			try {
				workbook.close();
			} catch (IOException e) {
				throw new RuntimeException(e.getMessage());
			}
		}
	}

	public String getCellData(String filePath, String sheetName, int rowNum, int colNum) {
		try {
			openWorkbook(filePath);
			sheet = workbook.getSheet(sheetName);
			if (sheet == null) {
				throw new RuntimeException("Sheet " + sheetName + " does not exist in the workbook.");
			}
			DataFormatter formatter = new DataFormatter();
			String cellData = formatter.formatCellValue(sheet.getRow(rowNum-1).getCell(colNum-1));
			return cellData;
		} finally {
			try {
				workbook.close();
			} catch (IOException e) {
				throw new RuntimeException(e.getMessage());
			}
		}
	}

	/**
	 * Set cell data with color formatting based on test result
	 * Green for Passed, Red for Failed, Yellow for Error
	 */
	public void setCellData(String filePath, String sheetName, int rowNum, int colNum, String value, boolean testPassed) {
		try {
			openWorkbook(filePath);
			sheet = workbook.getSheet(sheetName);

			// Create or get the cell
			Row row = sheet.getRow(rowNum-1);
			if (row == null) {
				row = sheet.createRow(rowNum-1);
			}
			Cell cell = row.createCell(colNum-1);
			cell.setCellValue(value);

			// Create cell style with color
			CellStyle style = workbook.createCellStyle();
			Font font = workbook.createFont();
			font.setBold(true);
			font.setColor(IndexedColors.WHITE.getIndex());
			style.setFont(font);

			if (testPassed) {
				// Green background for PASSED
				style.setFillForegroundColor(IndexedColors.BRIGHT_GREEN.getIndex());
				style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			} else {
				// Red background for FAILED
				style.setFillForegroundColor(IndexedColors.RED.getIndex());
				style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			}

			// Add border
			style.setBorderTop(BorderStyle.THIN);
			style.setBorderBottom(BorderStyle.THIN);
			style.setBorderLeft(BorderStyle.THIN);
			style.setBorderRight(BorderStyle.THIN);

			cell.setCellStyle(style);

			// Save the file
			String fullPath = userDirectory + "/" + filePath;
			FileOutputStream outputStream = new FileOutputStream(fullPath);
			workbook.write(outputStream);
			outputStream.close();
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage());
		} finally {
			try {
				workbook.close();
			} catch (IOException e) {
				throw new RuntimeException(e.getMessage());
			}
		}
	}

	/**
	 * Set cell data with specific status color formatting
	 * @param status - "Passed", "Failed", "Error", or any other status
	 */
	public void setCellDataWithStatusColor(String filePath, String sheetName, int rowNum, int colNum, String status) {
		try {
			openWorkbook(filePath);
			sheet = workbook.getSheet(sheetName);

			// Create or get the cell
			Row row = sheet.getRow(rowNum-1);
			if (row == null) {
				row = sheet.createRow(rowNum-1);
			}
			Cell cell = row.createCell(colNum-1);
			cell.setCellValue(status);

			// Create cell style with color based on status
			CellStyle style = workbook.createCellStyle();
			Font font = workbook.createFont();
			font.setBold(true);
			font.setColor(IndexedColors.WHITE.getIndex());
			style.setFont(font);

			// Set color based on status
			switch (status.toLowerCase()) {
				case "passed":
					style.setFillForegroundColor(IndexedColors.BRIGHT_GREEN.getIndex());
					break;
				case "failed":
					style.setFillForegroundColor(IndexedColors.RED.getIndex());
					break;
				case "error":
					style.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
					break;
				case "skipped":
					style.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
					font.setColor(IndexedColors.BLACK.getIndex());
					break;
				default:
					style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
					font.setColor(IndexedColors.BLACK.getIndex());
					break;
			}

			style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			style.setFont(font);

			// Add border
			style.setBorderTop(BorderStyle.THIN);
			style.setBorderBottom(BorderStyle.THIN);
			style.setBorderLeft(BorderStyle.THIN);
			style.setBorderRight(BorderStyle.THIN);

			cell.setCellStyle(style);

			// Save the file
			String fullPath = userDirectory + "/" + filePath;
			FileOutputStream outputStream = new FileOutputStream(fullPath);
			workbook.write(outputStream);
			outputStream.close();
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage());
		} finally {
			try {
				if (workbook != null) {
					workbook.close();
				}
			} catch (IOException e) {
				throw new RuntimeException(e.getMessage());
			}
		}
	}

	public void setCellData(String filePath, String sheetName, int rowNum, int colNum, String value) {
		try {
			openWorkbook(filePath);
			sheet = workbook.getSheet(sheetName);

			// Create sheet if it doesn't exist
			if (sheet == null) {
				sheet = workbook.createSheet(sheetName);
			}

			// Create or get the row
			Row row = sheet.getRow(rowNum-1);
			if (row == null) {
				row = sheet.createRow(rowNum-1);
			}
			Cell cell = row.createCell(colNum-1);
			cell.setCellValue(value);

			// Save the file with consistent path handling
			String fullPath = userDirectory + "/" + filePath;
			FileOutputStream outputStream = new FileOutputStream(fullPath);
			workbook.write(outputStream);
			outputStream.close();
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage());
		} finally {
			try {
				if (workbook != null) {
					workbook.close();
				}
			} catch (IOException e) {
				throw new RuntimeException(e.getMessage());
			}
		}
	}

	/**
	 * Get all sheet names from Excel file
	 */
	public String[] getAllSheetNames(String filePath) {
		try {
			openWorkbook(filePath);
			int numberOfSheets = workbook.getNumberOfSheets();
			String[] sheetNames = new String[numberOfSheets];

			for (int i = 0; i < numberOfSheets; i++) {
				sheetNames[i] = workbook.getSheetName(i);
			}

			return sheetNames;
		} finally {
			try {
				if (workbook != null) {
					workbook.close();
				}
			} catch (IOException e) {
				throw new RuntimeException(e.getMessage());
			}
		}
	}

	/**
	 * Check if sheet exists in Excel file
	 */
	public boolean sheetExists(String filePath, String sheetName) {
		try {
			openWorkbook(filePath);
			Sheet targetSheet = workbook.getSheet(sheetName);
			return targetSheet != null;
		} finally {
			try {
				if (workbook != null) {
					workbook.close();
				}
			} catch (IOException e) {
				throw new RuntimeException(e.getMessage());
			}
		}
	}

	/**
	 * Set cell background color
	 */
	public void setCellBackgroundColor(String filePath, String sheetName, int rowNum, int colNum, String color) {
		try {
			openWorkbook(filePath);
			sheet = workbook.getSheet(sheetName);

			if (sheet == null) {
				return;
			}

			// Get or create the row
			Row row = sheet.getRow(rowNum);
			if (row == null) {
				row = sheet.createRow(rowNum);
			}

			// Get or create the cell
			Cell cell = row.getCell(colNum);
			if (cell == null) {
				cell = row.createCell(colNum);
			}

			// Create cell style with background color
			CellStyle cellStyle = workbook.createCellStyle();

			// Set background color based on string parameter
			switch (color.toUpperCase()) {
				case "GREEN":
					cellStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
					break;
				case "RED":
					cellStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
					break;
				case "YELLOW":
					cellStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
					break;
				default:
					cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
					break;
			}

			cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

			// Apply style to cell
			cell.setCellStyle(cellStyle);

			// Save the file
			String fullPath = userDirectory + "/" + filePath;
			FileOutputStream outputStream = new FileOutputStream(fullPath);
			workbook.write(outputStream);
			outputStream.close();

		} catch (IOException e) {
			throw new RuntimeException(e.getMessage());
		} finally {
			try {
				if (workbook != null) {
					workbook.close();
				}
			} catch (IOException e) {
				throw new RuntimeException(e.getMessage());
			}
		}
	}

}
