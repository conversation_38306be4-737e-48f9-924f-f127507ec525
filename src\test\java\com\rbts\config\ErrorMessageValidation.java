package com.rbts.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Configuration class for error message validation
 * Defines expected error messages for specific constraint violations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrorMessageValidation {
    
    private String serviceName;
    private String tableName;
    private String fieldName;
    private String constraintType;
    private int expectedStatusCode;
    private String expectedErrorMessage;
    private String testValue;
    private String messageType;
    private String description;
    
    /**
     * Check if this validation matches the given criteria
     */
    public boolean matches(String service, String table, String field, String constraint) {
        return serviceName.equals(service) && 
               tableName.equals(table) && 
               (fieldName == null || fieldName.isEmpty() || fieldName.equals(field)) &&
               constraintType.equals(constraint);
    }
    
    /**
     * Check if this is an error message validation
     */
    public boolean isErrorMessage() {
        return "error".equalsIgnoreCase(messageType);
    }
    
    /**
     * Check if this is a success message validation
     */
    public boolean isSuccessMessage() {
        return "success".equalsIgnoreCase(messageType);
    }
    
    /**
     * Get the test value for constraint violation
     */
    public Object getTestValueAsObject() {
        if (testValue == null || testValue.trim().isEmpty()) {
            return null;
        }
        
        String trimmed = testValue.trim();
        
        // Handle special values
        if ("null".equalsIgnoreCase(trimmed)) {
            return null;
        }
        
        if ("valid_data".equalsIgnoreCase(trimmed)) {
            return "valid_test_data";
        }
        
        // Try to parse as number
        try {
            if (trimmed.contains(".")) {
                return Double.parseDouble(trimmed);
            } else {
                return Integer.parseInt(trimmed);
            }
        } catch (NumberFormatException e) {
            // Return as string
            return trimmed;
        }
    }
    
    /**
     * Validate actual error message against expected
     */
    public boolean validateMessage(String actualMessage) {
        if (expectedErrorMessage == null || expectedErrorMessage.trim().isEmpty()) {
            return true; // No specific message expected
        }
        
        if (actualMessage == null) {
            return false;
        }
        
        // Handle dynamic placeholders in expected message
        String expectedWithPlaceholders = expectedErrorMessage;
        
        // Replace common placeholders
        expectedWithPlaceholders = expectedWithPlaceholders.replace("{fieldName}", fieldName != null ? fieldName : "field");
        expectedWithPlaceholders = expectedWithPlaceholders.replace("{tableName}", tableName);
        expectedWithPlaceholders = expectedWithPlaceholders.replace("{serviceName}", serviceName);
        expectedWithPlaceholders = expectedWithPlaceholders.replace("{testValue}", testValue != null ? testValue : "");
        
        // Check for exact match or contains
        return actualMessage.equals(expectedWithPlaceholders) || 
               actualMessage.contains(expectedWithPlaceholders) ||
               expectedWithPlaceholders.contains(actualMessage);
    }
    
    /**
     * Validate actual status code against expected
     */
    public boolean validateStatusCode(int actualStatusCode) {
        return expectedStatusCode == actualStatusCode;
    }
    
    /**
     * Get unique key for this validation
     */
    public String getKey() {
        return String.format("%s.%s.%s.%s", serviceName, tableName, 
                           fieldName != null ? fieldName : "", constraintType);
    }
    
    /**
     * Create ErrorMessageValidation from Excel row data
     */
    public static ErrorMessageValidation fromExcelRow(String serviceName, String tableName, 
                                                     String fieldName, String constraintType,
                                                     String expectedStatusCode, String expectedErrorMessage,
                                                     String testValue, String messageType, String description) {
        return ErrorMessageValidation.builder()
                .serviceName(serviceName != null ? serviceName.trim() : "")
                .tableName(tableName != null ? tableName.trim() : "")
                .fieldName(fieldName != null ? fieldName.trim() : "")
                .constraintType(constraintType != null ? constraintType.trim() : "")
                .expectedStatusCode(parseInteger(expectedStatusCode))
                .expectedErrorMessage(expectedErrorMessage != null ? expectedErrorMessage.trim() : "")
                .testValue(testValue != null ? testValue.trim() : "")
                .messageType(messageType != null ? messageType.trim() : "error")
                .description(description != null ? description.trim() : "")
                .build();
    }
    
    /**
     * Parse integer from string with default value
     */
    private static int parseInteger(String value) {
        if (value == null || value.trim().isEmpty()) {
            return 400; // Default error status code
        }
        
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return 400;
        }
    }
    
    /**
     * Check if this validation is for a specific constraint type
     */
    public boolean isConstraintType(String constraint) {
        return constraintType.equalsIgnoreCase(constraint);
    }
    
    /**
     * Check if this validation is for null constraint
     */
    public boolean isNullConstraint() {
        return isConstraintType("null_constraint");
    }
    
    /**
     * Check if this validation is for unique constraint
     */
    public boolean isUniqueConstraint() {
        return isConstraintType("unique_constraint");
    }
    
    /**
     * Check if this validation is for foreign key constraint (same service)
     */
    public boolean isForeignKeySameService() {
        return isConstraintType("foreign_key_same_service");
    }
    
    /**
     * Check if this validation is for foreign key constraint (different service)
     */
    public boolean isForeignKeyDifferentService() {
        return isConstraintType("foreign_key_different_service");
    }
    
    /**
     * Check if this validation is for success scenario
     */
    public boolean isSuccessValidation() {
        return isConstraintType("success");
    }
    
    /**
     * Check if this validation is for validation error
     */
    public boolean isValidationError() {
        return isConstraintType("validation_error");
    }
    
    /**
     * Get formatted error message with dynamic values
     */
    public String getFormattedErrorMessage(Object... dynamicValues) {
        String formatted = expectedErrorMessage;
        
        // Replace numbered placeholders {0}, {1}, etc.
        for (int i = 0; i < dynamicValues.length; i++) {
            formatted = formatted.replace("{" + i + "}", String.valueOf(dynamicValues[i]));
        }
        
        // Replace named placeholders
        formatted = formatted.replace("{fieldName}", fieldName != null ? fieldName : "field");
        formatted = formatted.replace("{tableName}", tableName);
        formatted = formatted.replace("{serviceName}", serviceName);
        formatted = formatted.replace("{testValue}", testValue != null ? testValue : "");
        
        return formatted;
    }
    
    @Override
    public String toString() {
        return String.format("ErrorMessageValidation{service='%s', table='%s', field='%s', constraint='%s', statusCode=%d, message='%s'}",
                serviceName, tableName, fieldName, constraintType, expectedStatusCode, expectedErrorMessage);
    }
}
