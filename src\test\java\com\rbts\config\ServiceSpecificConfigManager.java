package com.rbts.config;

import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * Manager for service-specific comprehensive configurations
 * Loads all configurations from service-specific sheets
 */
@Slf4j
public class ServiceSpecificConfigManager {
    
    private static ServiceSpecificConfigManager instance;
    private final ExcelUtils excelUtils;
    private final String configFilePath;
    
    // Configuration storage
    private Map<String, String> generalConfig = new HashMap<>();
    private Map<String, Map<String, Integer>> statusCodeConfig = new HashMap<>();
    private Map<String, List<ServiceTableConfig>> serviceConfigurations = new HashMap<>();
    
    private ServiceSpecificConfigManager() {
        this.excelUtils = new ExcelUtils();
        this.configFilePath = "config/Service_Specific_Configuration.xlsx";
    }
    
    public static ServiceSpecificConfigManager getInstance() {
        if (instance == null) {
            instance = new ServiceSpecificConfigManager();
        }
        return instance;
    }
    
    /**
     * Load all configurations from service-specific Excel file
     */
    public void loadConfigurations() {
        log.info("🚀 Loading service-specific configurations from: {}", configFilePath);
        
        try {
            loadGeneralConfiguration();
            loadStatusCodeConfiguration();
            loadServiceSpecificConfigurations();
            
            log.info("✅ All service-specific configurations loaded successfully");
            
        } catch (Exception e) {
            log.error("❌ Error loading service-specific configurations: {}", e.getMessage());
            throw new RuntimeException("Failed to load service-specific configurations", e);
        }
    }
    
    /**
     * Load general configuration
     */
    private void loadGeneralConfiguration() {
        log.info("Loading general configuration from 'General_Config' sheet");
        
        try {
            int row = 2;
            String configKey;
            
            while ((configKey = excelUtils.getCellData(configFilePath, "General_Config", row, 1)) != null 
                   && !configKey.trim().isEmpty()) {
                
                String configValue = excelUtils.getCellData(configFilePath, "General_Config", row, 2);
                String description = excelUtils.getCellData(configFilePath, "General_Config", row, 3);
                
                generalConfig.put(configKey.trim(), configValue != null ? configValue.trim() : "");
                
                log.debug("Loaded general config: {} = {} ({})", configKey, configValue, description);
                row++;
            }
            
            log.info("✅ Loaded {} general configuration entries", generalConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load general configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load status code configuration
     */
    private void loadStatusCodeConfiguration() {
        log.info("Loading status code configuration from 'Status_Code_Config' sheet");
        
        try {
            int row = 2;
            String operation;
            
            while ((operation = excelUtils.getCellData(configFilePath, "Status_Code_Config", row, 1)) != null 
                   && !operation.trim().isEmpty()) {
                
                Map<String, Integer> operationStatusCodes = new HashMap<>();
                
                // Parse status codes for different constraint types
                String successCodes = excelUtils.getCellData(configFilePath, "Status_Code_Config", row, 2);
                String nullConstraintCode = excelUtils.getCellData(configFilePath, "Status_Code_Config", row, 3);
                String uniqueConstraintCode = excelUtils.getCellData(configFilePath, "Status_Code_Config", row, 4);
                String fkSameServiceCode = excelUtils.getCellData(configFilePath, "Status_Code_Config", row, 5);
                String fkDifferentServiceCode = excelUtils.getCellData(configFilePath, "Status_Code_Config", row, 6);
                String validationErrorCode = excelUtils.getCellData(configFilePath, "Status_Code_Config", row, 7);
                
                // Store first success code as default
                if (successCodes != null && !successCodes.trim().isEmpty()) {
                    String[] codes = successCodes.split(",");
                    operationStatusCodes.put("success", Integer.parseInt(codes[0].trim()));
                }
                
                operationStatusCodes.put("null_constraint", parseStatusCode(nullConstraintCode, 700));
                operationStatusCodes.put("unique_constraint", parseStatusCode(uniqueConstraintCode, 701));
                operationStatusCodes.put("foreign_key_same_service", parseStatusCode(fkSameServiceCode, 404));
                operationStatusCodes.put("foreign_key_different_service", parseStatusCode(fkDifferentServiceCode, 702));
                operationStatusCodes.put("validation_error", parseStatusCode(validationErrorCode, 400));
                
                statusCodeConfig.put(operation.trim().toUpperCase(), operationStatusCodes);
                
                log.debug("Loaded status codes for operation: {}", operation);
                row++;
            }
            
            log.info("✅ Loaded status code configuration for {} operations", statusCodeConfig.size());
            
        } catch (Exception e) {
            log.warn("Could not load status code configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load service-specific configurations
     */
    private void loadServiceSpecificConfigurations() {
        log.info("Loading service-specific configurations");
        
        // List of services to load
        String[] services = {"Contact", "Authentication", "Core", "Order", "Product"};
        
        for (String service : services) {
            loadServiceConfiguration(service);
        }
        
        // Log summary of loaded services
        for (String service : serviceConfigurations.keySet()) {
            List<ServiceTableConfig> configs = serviceConfigurations.get(service);
            log.info("   📋 Service '{}': {} configurations", service, configs.size());
        }

        log.info("✅ Loaded configurations for {} services", serviceConfigurations.size());
    }
    
    /**
     * Load configuration for a specific service
     */
    private void loadServiceConfiguration(String serviceName) {
        String sheetName = serviceName + "_Service_Config";
        log.info("Loading configuration from '{}' sheet", sheetName);
        
        try {
            List<ServiceTableConfig> serviceTableConfigs = new ArrayList<>();
            int row = 2;
            String tableName;
            
            while ((tableName = excelUtils.getCellData(configFilePath, sheetName, row, 1)) != null 
                   && !tableName.trim().isEmpty()) {
                
                ServiceTableConfig config = ServiceTableConfig.builder()
                        .serviceName(serviceName.toLowerCase())
                        .tableName(tableName.trim())
                        .databaseFieldName(excelUtils.getCellData(configFilePath, sheetName, row, 2))
                        .apiRequestFieldName(excelUtils.getCellData(configFilePath, sheetName, row, 3))
                        .apiResponseFieldName(excelUtils.getCellData(configFilePath, sheetName, row, 4))
                        .fieldType(excelUtils.getCellData(configFilePath, sheetName, row, 5))
                        .requestOperations(excelUtils.getCellData(configFilePath, sheetName, row, 6))
                        .responseOperations(excelUtils.getCellData(configFilePath, sheetName, row, 7))
                        .fkSameService(excelUtils.getCellData(configFilePath, sheetName, row, 8))
                        .fkDifferentService(excelUtils.getCellData(configFilePath, sheetName, row, 9))
                        .apiEndpoints(excelUtils.getCellData(configFilePath, sheetName, row, 10))
                        .enableTesting(parseBoolean(excelUtils.getCellData(configFilePath, sheetName, row, 11)))
                        .operationsToTest(excelUtils.getCellData(configFilePath, sheetName, row, 12))
                        .constraintType(excelUtils.getCellData(configFilePath, sheetName, row, 13))
                        .expectedErrorMessage(excelUtils.getCellData(configFilePath, sheetName, row, 14))
                        .testValue(excelUtils.getCellData(configFilePath, sheetName, row, 15))
                        .comments(excelUtils.getCellData(configFilePath, sheetName, row, 16))
                        .build();
                
                serviceTableConfigs.add(config);
                
                log.debug("Loaded config for {}.{}.{}: {}", serviceName, tableName, 
                         config.getDatabaseFieldName(), config.getConstraintType());
                row++;
            }
            
            if (!serviceTableConfigs.isEmpty()) {
                serviceConfigurations.put(serviceName.toLowerCase(), serviceTableConfigs);
                log.info("✅ Loaded {} configurations for {} service", serviceTableConfigs.size(), serviceName);
            } else {
                log.warn("⚠️ No configurations loaded for {} service", serviceName);
            }
            
        } catch (Exception e) {
            log.warn("Could not load configuration for service {}: {}", serviceName, e.getMessage());
        }
    }
    
    /**
     * Get general configuration value
     */
    public String getGeneralConfig(String key) {
        return generalConfig.get(key);
    }
    
    /**
     * Get status code for operation and constraint type
     */
    public int getStatusCode(String operation, String constraintType) {
        Map<String, Integer> operationCodes = statusCodeConfig.get(operation.toUpperCase());
        if (operationCodes != null) {
            Integer statusCode = operationCodes.get(constraintType);
            if (statusCode != null) {
                return statusCode;
            }
        }
        return 400; // Default error status code
    }
    
    /**
     * Get all configurations for a service
     */
    public List<ServiceTableConfig> getServiceConfigurations(String serviceName) {
        return serviceConfigurations.getOrDefault(serviceName.toLowerCase(), new ArrayList<>());
    }
    
    /**
     * Get configurations for a specific table
     */
    public List<ServiceTableConfig> getTableConfigurations(String serviceName, String tableName) {
        List<ServiceTableConfig> serviceConfigs = getServiceConfigurations(serviceName);
        List<ServiceTableConfig> tableConfigs = new ArrayList<>();
        
        for (ServiceTableConfig config : serviceConfigs) {
            if (config.getTableName().equals(tableName)) {
                tableConfigs.add(config);
            }
        }
        
        return tableConfigs;
    }
    
    /**
     * Get API endpoint for table and operation
     */
    public String getApiEndpoint(String serviceName, String tableName, String operation) {
        List<ServiceTableConfig> tableConfigs = getTableConfigurations(serviceName, tableName);
        
        for (ServiceTableConfig config : tableConfigs) {
            String endpoints = config.getApiEndpoints();
            if (endpoints != null && !endpoints.trim().isEmpty()) {
                // Parse endpoints: POST:/service/api/Table/save,GET:/service/api/Table/{id}
                String[] endpointArray = endpoints.split(",");
                for (String endpoint : endpointArray) {
                    if (endpoint.trim().toUpperCase().startsWith(operation.toUpperCase() + ":")) {
                        return endpoint.substring(endpoint.indexOf(":") + 1).trim();
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * Check if table is enabled for testing
     */
    public boolean isTableEnabledForTesting(String serviceName, String tableName) {
        List<ServiceTableConfig> tableConfigs = getTableConfigurations(serviceName, tableName);
        
        for (ServiceTableConfig config : tableConfigs) {
            if (config.isEnableTesting()) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get operations to test for a table
     */
    public List<String> getOperationsToTest(String serviceName, String tableName) {
        List<ServiceTableConfig> tableConfigs = getTableConfigurations(serviceName, tableName);
        Set<String> operations = new HashSet<>();
        
        for (ServiceTableConfig config : tableConfigs) {
            String operationsToTest = config.getOperationsToTest();
            if (operationsToTest != null && !operationsToTest.trim().isEmpty()) {
                String[] ops = operationsToTest.split(",");
                for (String op : ops) {
                    operations.add(op.trim().toLowerCase());
                }
            }
        }
        
        return new ArrayList<>(operations);
    }
    
    /**
     * Get error message validation for constraint
     */
    public ServiceTableConfig getErrorMessageValidation(String serviceName, String tableName, 
                                                       String fieldName, String constraintType) {
        List<ServiceTableConfig> tableConfigs = getTableConfigurations(serviceName, tableName);
        
        for (ServiceTableConfig config : tableConfigs) {
            if (constraintType.equals(config.getConstraintType())) {
                // Check if field name matches (or is empty for table-level validations)
                String configFieldName = config.getDatabaseFieldName();
                if (fieldName == null || fieldName.isEmpty() || 
                    configFieldName == null || configFieldName.isEmpty() ||
                    configFieldName.equals(fieldName)) {
                    return config;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Get all configured services
     */
    public Set<String> getConfiguredServices() {
        return serviceConfigurations.keySet();
    }
    
    /**
     * Get all tables for a service
     */
    public Set<String> getTablesForService(String serviceName) {
        List<ServiceTableConfig> serviceConfigs = getServiceConfigurations(serviceName);
        Set<String> tables = new HashSet<>();
        
        for (ServiceTableConfig config : serviceConfigs) {
            tables.add(config.getTableName());
        }
        
        return tables;
    }
    
    /**
     * Parse status code with default value
     */
    private int parseStatusCode(String statusCode, int defaultValue) {
        if (statusCode == null || statusCode.trim().isEmpty()) {
            return defaultValue;
        }
        
        try {
            return Integer.parseInt(statusCode.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * Parse boolean with default false
     */
    private boolean parseBoolean(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        return "true".equalsIgnoreCase(value.trim()) || "yes".equalsIgnoreCase(value.trim()) || "1".equals(value.trim());
    }
    
    /**
     * Print configuration summary
     */
    public void printConfigurationSummary() {
        log.info("📊 SERVICE-SPECIFIC CONFIGURATION SUMMARY");
        log.info("==========================================");
        
        log.info("🔧 General Configuration: {} entries", generalConfig.size());
        log.info("📊 Status Code Configuration: {} operations", statusCodeConfig.size());
        log.info("🏢 Service Configurations: {} services", serviceConfigurations.size());
        
        for (String service : serviceConfigurations.keySet()) {
            List<ServiceTableConfig> configs = serviceConfigurations.get(service);
            Set<String> tables = getTablesForService(service);
            log.info("   📋 {}: {} tables, {} configurations", service, tables.size(), configs.size());
        }
        
        log.info("");
        log.info("🎯 BENEFITS OF SERVICE-SPECIFIC CONFIGURATION:");
        log.info("✅ One comprehensive sheet per service");
        log.info("✅ All configurations in one place per service");
        log.info("✅ Easy to maintain and scale");
        log.info("✅ Service-specific field mappings");
        log.info("✅ Service-specific error messages");
        log.info("✅ Service-specific test controls");
        log.info("✅ Service-specific API endpoints");
    }
}
