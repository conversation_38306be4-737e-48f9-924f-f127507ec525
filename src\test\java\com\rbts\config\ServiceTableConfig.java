package com.rbts.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Configuration class for service-specific table configurations
 * Contains all configuration data for a table field in a service
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceTableConfig {
    
    private String serviceName;
    private String tableName;
    private String databaseFieldName;
    private String apiRequestFieldName;
    private String apiResponseFieldName;
    private String fieldType;
    private String requestOperations;
    private String responseOperations;
    private String fkSameService;
    private String fkDifferentService;
    private String apiEndpoints;
    private boolean enableTesting;
    private String operationsToTest;
    private String constraintType;
    private String expectedErrorMessage;
    private String testValue;
    private String comments;
    
    /**
     * Check if this configuration is for a specific constraint type
     */
    public boolean isConstraintType(String constraint) {
        return constraintType != null && constraintType.equalsIgnoreCase(constraint);
    }
    
    /**
     * Check if this is a null constraint configuration
     */
    public boolean isNullConstraint() {
        return isConstraintType("null_constraint");
    }
    
    /**
     * Check if this is a unique constraint configuration
     */
    public boolean isUniqueConstraint() {
        return isConstraintType("unique_constraint");
    }
    
    /**
     * Check if this is a foreign key same service constraint
     */
    public boolean isForeignKeySameService() {
        return isConstraintType("foreign_key_same_service");
    }
    
    /**
     * Check if this is a foreign key different service constraint
     */
    public boolean isForeignKeyDifferentService() {
        return isConstraintType("foreign_key_different_service");
    }
    
    /**
     * Check if this is a validation error configuration
     */
    public boolean isValidationError() {
        return isConstraintType("validation_error");
    }
    
    /**
     * Check if this is a success message configuration
     */
    public boolean isSuccessMessage() {
        return isConstraintType("success");
    }
    
    /**
     * Check if this field is present in request for given operation
     */
    public boolean isPresentInRequest(String operation) {
        if (requestOperations == null || requestOperations.trim().isEmpty()) {
            return false;
        }
        
        if ("ALL".equalsIgnoreCase(requestOperations.trim())) {
            return true;
        }
        
        if ("NONE".equalsIgnoreCase(requestOperations.trim())) {
            return false;
        }
        
        String[] operations = requestOperations.split(",");
        for (String op : operations) {
            if (op.trim().equalsIgnoreCase(operation)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if this field is present in response for given operation
     */
    public boolean isPresentInResponse(String operation) {
        if (responseOperations == null || responseOperations.trim().isEmpty()) {
            return false;
        }
        
        if ("ALL".equalsIgnoreCase(responseOperations.trim())) {
            return true;
        }
        
        if ("NONE".equalsIgnoreCase(responseOperations.trim())) {
            return false;
        }
        
        String[] operations = responseOperations.split(",");
        for (String op : operations) {
            if (op.trim().equalsIgnoreCase(operation)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if this is a primary key field
     */
    public boolean isPrimaryKey() {
        return "PRIMARY_KEY".equalsIgnoreCase(fieldType);
    }
    
    /**
     * Check if this is a foreign key field
     */
    public boolean isForeignKey() {
        return "FOREIGN_KEY".equalsIgnoreCase(fieldType);
    }
    
    /**
     * Check if this is an audit field
     */
    public boolean isAuditField() {
        return fieldType != null && (fieldType.contains("AUDIT") || fieldType.contains("TIMESTAMP"));
    }
    
    /**
     * Check if this is an email field
     */
    public boolean isEmailField() {
        return "EMAIL".equalsIgnoreCase(fieldType);
    }
    
    /**
     * Get test value as object
     */
    public Object getTestValueAsObject() {
        if (testValue == null || testValue.trim().isEmpty()) {
            return null;
        }
        
        String trimmed = testValue.trim();
        
        // Handle special values
        if ("null".equalsIgnoreCase(trimmed)) {
            return null;
        }
        
        if ("valid_data".equalsIgnoreCase(trimmed)) {
            return generateValidValue();
        }
        
        // Try to parse as number
        try {
            if (trimmed.contains(".")) {
                return Double.parseDouble(trimmed);
            } else {
                return Integer.parseInt(trimmed);
            }
        } catch (NumberFormatException e) {
            // Return as string
            return trimmed;
        }
    }
    
    /**
     * Generate valid value based on field type
     */
    private Object generateValidValue() {
        if (fieldType == null) {
            return "Valid Value";
        }
        
        switch (fieldType.toUpperCase()) {
            case "EMAIL":
                return "<EMAIL>";
            case "STRING":
                return "Valid " + (databaseFieldName != null ? databaseFieldName : "Value");
            case "INTEGER":
            case "PRIMARY_KEY":
                return 1;
            case "DECIMAL":
                return 10.50;
            case "BOOLEAN":
                return true;
            case "TIMESTAMP":
            case "AUDIT_TIMESTAMP":
                return "2024-01-01T10:00:00Z";
            case "FOREIGN_KEY":
                return 1; // Valid FK ID
            default:
                return "Valid Value";
        }
    }
    
    /**
     * Get API endpoint for specific operation
     */
    public String getApiEndpointForOperation(String operation) {
        if (apiEndpoints == null || apiEndpoints.trim().isEmpty()) {
            return null;
        }
        
        // Parse endpoints: POST:/service/api/Table/save,GET:/service/api/Table/{id}
        String[] endpointArray = apiEndpoints.split(",");
        for (String endpoint : endpointArray) {
            if (endpoint.trim().toUpperCase().startsWith(operation.toUpperCase() + ":")) {
                return endpoint.substring(endpoint.indexOf(":") + 1).trim();
            }
        }
        
        return null;
    }
    
    /**
     * Check if operation should be tested
     */
    public boolean shouldTestOperation(String operation) {
        if (!enableTesting) {
            return false;
        }
        
        if (operationsToTest == null || operationsToTest.trim().isEmpty()) {
            return false;
        }
        
        String[] operations = operationsToTest.split(",");
        for (String op : operations) {
            if (op.trim().equalsIgnoreCase(operation)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Validate error message against expected
     */
    public boolean validateErrorMessage(String actualMessage) {
        if (expectedErrorMessage == null || expectedErrorMessage.trim().isEmpty()) {
            return true; // No specific message expected
        }
        
        if (actualMessage == null) {
            return false;
        }
        
        // Handle dynamic placeholders in expected message
        String expectedWithPlaceholders = expectedErrorMessage;
        
        // Replace common placeholders
        expectedWithPlaceholders = expectedWithPlaceholders.replace("{fieldName}", 
                databaseFieldName != null ? databaseFieldName : "field");
        expectedWithPlaceholders = expectedWithPlaceholders.replace("{tableName}", tableName);
        expectedWithPlaceholders = expectedWithPlaceholders.replace("{serviceName}", serviceName);
        expectedWithPlaceholders = expectedWithPlaceholders.replace("{testValue}", 
                testValue != null ? testValue : "");
        
        // Check for exact match or contains
        return actualMessage.equals(expectedWithPlaceholders) || 
               actualMessage.contains(expectedWithPlaceholders) ||
               expectedWithPlaceholders.contains(actualMessage);
    }
    
    /**
     * Get formatted error message with dynamic values
     */
    public String getFormattedErrorMessage(Object... dynamicValues) {
        if (expectedErrorMessage == null) {
            return "";
        }
        
        String formatted = expectedErrorMessage;
        
        // Replace numbered placeholders {0}, {1}, etc.
        for (int i = 0; i < dynamicValues.length; i++) {
            formatted = formatted.replace("{" + i + "}", String.valueOf(dynamicValues[i]));
        }
        
        // Replace named placeholders
        formatted = formatted.replace("{fieldName}", databaseFieldName != null ? databaseFieldName : "field");
        formatted = formatted.replace("{tableName}", tableName);
        formatted = formatted.replace("{serviceName}", serviceName);
        formatted = formatted.replace("{testValue}", testValue != null ? testValue : "");
        
        return formatted;
    }
    
    /**
     * Get unique key for this configuration
     */
    public String getKey() {
        return String.format("%s.%s.%s.%s", serviceName, tableName, 
                           databaseFieldName != null ? databaseFieldName : "", 
                           constraintType != null ? constraintType : "");
    }
    
    /**
     * Check if this configuration has constraint validation
     */
    public boolean hasConstraintValidation() {
        return constraintType != null && !constraintType.trim().isEmpty() && 
               !constraintType.trim().equalsIgnoreCase("success");
    }
    
    /**
     * Check if this configuration has success validation
     */
    public boolean hasSuccessValidation() {
        return isSuccessMessage();
    }
    
    /**
     * Check if this is a table-level configuration (no specific field)
     */
    public boolean isTableLevelConfig() {
        return databaseFieldName == null || databaseFieldName.trim().isEmpty();
    }
    
    /**
     * Check if this is a field-level configuration
     */
    public boolean isFieldLevelConfig() {
        return !isTableLevelConfig();
    }
    
    @Override
    public String toString() {
        return String.format("ServiceTableConfig{service='%s', table='%s', field='%s', constraint='%s', enabled=%s}",
                serviceName, tableName, databaseFieldName, constraintType, enableTesting);
    }
}
