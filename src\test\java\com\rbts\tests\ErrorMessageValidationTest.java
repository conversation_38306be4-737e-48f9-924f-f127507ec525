package com.rbts.tests;

import com.rbts.config.ErrorMessageValidation;
import com.rbts.config.ExcelConfigManager;
import com.rbts.config.ExcelConfigTemplateGenerator;
import com.rbts.core.ErrorMessageTester;
import com.rbts.reporting.TestCaseResult;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * TestNG test class for comprehensive error message validation
 * Tests specific error messages for constraint violations based on Excel configuration
 */
@Slf4j
public class ErrorMessageValidationTest {
    
    private ExcelConfigManager configManager;
    private ExcelConfigTemplateGenerator templateGenerator;
    private ErrorMessageTester errorMessageTester;
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up Error Message Validation Test");
        
        // Generate Excel configuration template with error message validation
        templateGenerator = new ExcelConfigTemplateGenerator();
        templateGenerator.generateConfigurationTemplate();
        
        // Initialize config manager and error message tester
        configManager = ExcelConfigManager.getInstance();
        errorMessageTester = new ErrorMessageTester();
        
        log.info("✅ Error Message Validation Test setup completed");
    }
    
    /**
     * Data provider for error message validations
     */
    @DataProvider(name = "errorMessageValidations")
    public Object[][] getErrorMessageValidationData() {
        List<Object[]> validationData = new ArrayList<>();
        
        // Get all configured error message validations
        List<ErrorMessageValidation> allValidations = getAllErrorMessageValidations();
        
        for (ErrorMessageValidation validation : allValidations) {
            // Only include error message validations (not success messages)
            if (validation.isErrorMessage()) {
                validationData.add(new Object[]{
                    validation.getServiceName(),
                    validation.getTableName(),
                    validation.getFieldName(),
                    validation.getConstraintType(),
                    validation
                });
                
                log.info("📋 Added error message validation: {}.{}.{} - {}", 
                        validation.getServiceName(), validation.getTableName(), 
                        validation.getFieldName(), validation.getConstraintType());
            }
        }
        
        log.info("🎯 Total error message validations to test: {}", validationData.size());
        return validationData.toArray(new Object[0][]);
    }
    
    /**
     * Data provider for success message validations
     */
    @DataProvider(name = "successMessageValidations")
    public Object[][] getSuccessMessageValidationData() {
        List<Object[]> validationData = new ArrayList<>();
        
        // Get all configured success message validations
        List<ErrorMessageValidation> allValidations = getAllErrorMessageValidations();
        
        for (ErrorMessageValidation validation : allValidations) {
            // Only include success message validations
            if (validation.isSuccessMessage()) {
                validationData.add(new Object[]{
                    validation.getServiceName(),
                    validation.getTableName(),
                    validation
                });
                
                log.info("📋 Added success message validation: {}.{}", 
                        validation.getServiceName(), validation.getTableName());
            }
        }
        
        log.info("🎯 Total success message validations to test: {}", validationData.size());
        return validationData.toArray(new Object[0][]);
    }
    
    /**
     * Get all error message validations from configuration
     */
    private List<ErrorMessageValidation> getAllErrorMessageValidations() {
        List<ErrorMessageValidation> allValidations = new ArrayList<>();
        
        // Get all configured services and tables
        for (String service : configManager.getConfiguredServices()) {
            List<String> tables = configManager.getTablesForService(service);
            for (String table : tables) {
                List<ErrorMessageValidation> tableValidations = 
                        configManager.getErrorMessageValidationsForTable(service, table);
                allValidations.addAll(tableValidations);
            }
        }
        
        return allValidations;
    }
    
    /**
     * Test null constraint error messages
     */
    @Test(dataProvider = "errorMessageValidations", description = "Test null constraint error messages")
    public void testNullConstraintErrorMessage(String serviceName, String tableName, String fieldName, 
                                             String constraintType, ErrorMessageValidation validation) {
        
        // Only test null constraint validations
        if (!validation.isNullConstraint()) {
            return;
        }
        
        log.info("🔍 Testing NULL constraint error message for {}.{}.{}", serviceName, tableName, fieldName);
        
        try {
            TestCaseResult result = errorMessageTester.testConstraintErrorMessage(
                    serviceName, tableName, fieldName, constraintType);
            
            // Log test result
            log.info("📊 NULL Constraint Test Result: {}", result.getStatus());
            log.info("   Expected: {}", result.getExpectedResult());
            log.info("   Actual: {}", result.getActualResult());
            
            // Assert based on test result
            if (result.isPassed()) {
                log.info("✅ NULL constraint error message test PASSED for {}.{}.{}", 
                        serviceName, tableName, fieldName);
                Assert.assertTrue(true, "NULL constraint error message test passed");
            } else {
                log.error("❌ NULL constraint error message test FAILED for {}.{}.{}: {}", 
                        serviceName, tableName, fieldName, result.getErrorMessage());
                Assert.fail("NULL constraint error message test failed: " + result.getActualResult());
            }
            
        } catch (Exception e) {
            log.error("💥 Exception during NULL constraint test: {}", e.getMessage());
            Assert.fail("Exception during NULL constraint test: " + e.getMessage());
        }
    }
    
    /**
     * Test unique constraint error messages
     */
    @Test(dataProvider = "errorMessageValidations", description = "Test unique constraint error messages")
    public void testUniqueConstraintErrorMessage(String serviceName, String tableName, String fieldName, 
                                               String constraintType, ErrorMessageValidation validation) {
        
        // Only test unique constraint validations
        if (!validation.isUniqueConstraint()) {
            return;
        }
        
        log.info("🔍 Testing UNIQUE constraint error message for {}.{}.{}", serviceName, tableName, fieldName);
        
        try {
            TestCaseResult result = errorMessageTester.testConstraintErrorMessage(
                    serviceName, tableName, fieldName, constraintType);
            
            // Log test result
            log.info("📊 UNIQUE Constraint Test Result: {}", result.getStatus());
            log.info("   Expected: {}", result.getExpectedResult());
            log.info("   Actual: {}", result.getActualResult());
            
            // Assert based on test result
            if (result.isPassed()) {
                log.info("✅ UNIQUE constraint error message test PASSED for {}.{}.{}", 
                        serviceName, tableName, fieldName);
                Assert.assertTrue(true, "UNIQUE constraint error message test passed");
            } else {
                log.error("❌ UNIQUE constraint error message test FAILED for {}.{}.{}: {}", 
                        serviceName, tableName, fieldName, result.getErrorMessage());
                Assert.fail("UNIQUE constraint error message test failed: " + result.getActualResult());
            }
            
        } catch (Exception e) {
            log.error("💥 Exception during UNIQUE constraint test: {}", e.getMessage());
            Assert.fail("Exception during UNIQUE constraint test: " + e.getMessage());
        }
    }
    
    /**
     * Test foreign key constraint error messages (same service)
     */
    @Test(dataProvider = "errorMessageValidations", description = "Test foreign key constraint error messages (same service)")
    public void testForeignKeySameServiceErrorMessage(String serviceName, String tableName, String fieldName, 
                                                    String constraintType, ErrorMessageValidation validation) {
        
        // Only test foreign key same service validations
        if (!validation.isForeignKeySameService()) {
            return;
        }
        
        log.info("🔍 Testing FOREIGN KEY (same service) error message for {}.{}.{}", serviceName, tableName, fieldName);
        
        try {
            TestCaseResult result = errorMessageTester.testConstraintErrorMessage(
                    serviceName, tableName, fieldName, constraintType);
            
            // Log test result
            log.info("📊 FK Same Service Test Result: {}", result.getStatus());
            log.info("   Expected: {}", result.getExpectedResult());
            log.info("   Actual: {}", result.getActualResult());
            
            // Assert based on test result
            if (result.isPassed()) {
                log.info("✅ FK same service error message test PASSED for {}.{}.{}", 
                        serviceName, tableName, fieldName);
                Assert.assertTrue(true, "FK same service error message test passed");
            } else {
                log.error("❌ FK same service error message test FAILED for {}.{}.{}: {}", 
                        serviceName, tableName, fieldName, result.getErrorMessage());
                Assert.fail("FK same service error message test failed: " + result.getActualResult());
            }
            
        } catch (Exception e) {
            log.error("💥 Exception during FK same service test: {}", e.getMessage());
            Assert.fail("Exception during FK same service test: " + e.getMessage());
        }
    }
    
    /**
     * Test foreign key constraint error messages (different service)
     */
    @Test(dataProvider = "errorMessageValidations", description = "Test foreign key constraint error messages (different service)")
    public void testForeignKeyDifferentServiceErrorMessage(String serviceName, String tableName, String fieldName, 
                                                         String constraintType, ErrorMessageValidation validation) {
        
        // Only test foreign key different service validations
        if (!validation.isForeignKeyDifferentService()) {
            return;
        }
        
        log.info("🔍 Testing FOREIGN KEY (different service) error message for {}.{}.{}", serviceName, tableName, fieldName);
        
        try {
            TestCaseResult result = errorMessageTester.testConstraintErrorMessage(
                    serviceName, tableName, fieldName, constraintType);
            
            // Log test result
            log.info("📊 FK Different Service Test Result: {}", result.getStatus());
            log.info("   Expected: {}", result.getExpectedResult());
            log.info("   Actual: {}", result.getActualResult());
            
            // Assert based on test result
            if (result.isPassed()) {
                log.info("✅ FK different service error message test PASSED for {}.{}.{}", 
                        serviceName, tableName, fieldName);
                Assert.assertTrue(true, "FK different service error message test passed");
            } else {
                log.error("❌ FK different service error message test FAILED for {}.{}.{}: {}", 
                        serviceName, tableName, fieldName, result.getErrorMessage());
                Assert.fail("FK different service error message test failed: " + result.getActualResult());
            }
            
        } catch (Exception e) {
            log.error("💥 Exception during FK different service test: {}", e.getMessage());
            Assert.fail("Exception during FK different service test: " + e.getMessage());
        }
    }
    
    /**
     * Test validation error messages
     */
    @Test(dataProvider = "errorMessageValidations", description = "Test validation error messages")
    public void testValidationErrorMessage(String serviceName, String tableName, String fieldName, 
                                         String constraintType, ErrorMessageValidation validation) {
        
        // Only test validation error validations
        if (!validation.isValidationError()) {
            return;
        }
        
        log.info("🔍 Testing VALIDATION error message for {}.{}.{}", serviceName, tableName, fieldName);
        
        try {
            TestCaseResult result = errorMessageTester.testConstraintErrorMessage(
                    serviceName, tableName, fieldName, constraintType);
            
            // Log test result
            log.info("📊 Validation Error Test Result: {}", result.getStatus());
            log.info("   Expected: {}", result.getExpectedResult());
            log.info("   Actual: {}", result.getActualResult());
            
            // Assert based on test result
            if (result.isPassed()) {
                log.info("✅ Validation error message test PASSED for {}.{}.{}", 
                        serviceName, tableName, fieldName);
                Assert.assertTrue(true, "Validation error message test passed");
            } else {
                log.error("❌ Validation error message test FAILED for {}.{}.{}: {}", 
                        serviceName, tableName, fieldName, result.getErrorMessage());
                Assert.fail("Validation error message test failed: " + result.getActualResult());
            }
            
        } catch (Exception e) {
            log.error("💥 Exception during validation error test: {}", e.getMessage());
            Assert.fail("Exception during validation error test: " + e.getMessage());
        }
    }
    
    /**
     * Test success messages
     */
    @Test(dataProvider = "successMessageValidations", description = "Test success messages")
    public void testSuccessMessage(String serviceName, String tableName, ErrorMessageValidation validation) {
        log.info("✅ Testing SUCCESS message for {}.{}", serviceName, tableName);
        
        try {
            TestCaseResult result = errorMessageTester.testSuccessMessage(serviceName, tableName);
            
            // Log test result
            log.info("📊 Success Message Test Result: {}", result.getStatus());
            log.info("   Expected: {}", result.getExpectedResult());
            log.info("   Actual: {}", result.getActualResult());
            
            // Assert based on test result
            if (result.isPassed()) {
                log.info("✅ Success message test PASSED for {}.{}", serviceName, tableName);
                Assert.assertTrue(true, "Success message test passed");
            } else {
                log.error("❌ Success message test FAILED for {}.{}: {}", 
                        serviceName, tableName, result.getErrorMessage());
                Assert.fail("Success message test failed: " + result.getActualResult());
            }
            
        } catch (Exception e) {
            log.error("💥 Exception during success message test: {}", e.getMessage());
            Assert.fail("Exception during success message test: " + e.getMessage());
        }
    }
    
    /**
     * Final error message validation summary
     */
    @Test(description = "Error message validation test summary", dependsOnMethods = {
            "testNullConstraintErrorMessage", "testUniqueConstraintErrorMessage", 
            "testForeignKeySameServiceErrorMessage", "testForeignKeyDifferentServiceErrorMessage",
            "testValidationErrorMessage", "testSuccessMessage"})
    public void errorMessageValidationSummary() {
        log.info("📊 ERROR MESSAGE VALIDATION TEST SUMMARY");
        log.info("==========================================");
        
        // Get test execution statistics
        int totalTestCases = errorMessageTester.getTestReporter().getTotalTestCases();
        int totalDefects = errorMessageTester.getTestReporter().getTotalDefects();
        String reportFile = errorMessageTester.getTestReporter().getReportFilePath();
        
        log.info("📋 Total Error Message Tests Executed: {}", totalTestCases);
        log.info("🐛 Total Defects Found: {}", totalDefects);
        log.info("📁 Test Report File: {}", reportFile);
        
        log.info("");
        log.info("🎯 ERROR MESSAGE VALIDATION FEATURES DEMONSTRATED:");
        log.info("✅ NULL constraint error message validation");
        log.info("✅ UNIQUE constraint error message validation");
        log.info("✅ FOREIGN KEY constraint error message validation (same service)");
        log.info("✅ FOREIGN KEY constraint error message validation (different service)");
        log.info("✅ VALIDATION error message validation");
        log.info("✅ SUCCESS message validation");
        log.info("✅ Status code validation for all constraint types");
        log.info("✅ Excel-driven error message configuration");
        log.info("✅ Field-specific error message testing");
        
        log.info("");
        log.info("📋 EXAMPLE ERROR MESSAGE VALIDATIONS TESTED:");
        log.info("• State.stateShortName NULL constraint: 'State short name cannot be null' (700)");
        log.info("• State.stateShortName UNIQUE constraint: 'State short name 'CA' already exists' (701)");
        log.info("• User.email NULL constraint: 'Email address is required' (700)");
        log.info("• User.email UNIQUE constraint: 'Email address '<EMAIL>' is already registered' (701)");
        log.info("• UserProfile.userId FK same service: 'User with ID 999 not found' (404)");
        log.info("• UserProfile.countryId FK different service: 'Country service is unavailable or country ID 999 not found' (702)");
        log.info("• Product.price VALIDATION error: 'Product price must be greater than 0' (400)");
        
        log.info("");
        log.info("🚀 NEXT STEPS:");
        log.info("1. Configure your specific error messages in Error_Message_Validation sheet");
        log.info("2. Add field-specific constraint validations");
        log.info("3. Test against your actual APIs");
        log.info("4. Verify error messages match your API specifications");
        
        Assert.assertTrue(totalTestCases > 0, "Should have executed error message validation test cases");
    }
}
