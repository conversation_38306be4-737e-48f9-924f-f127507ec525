package com.rbts.base.post;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.restassured.response.Response;
import org.slf4j.Logger;
import com.rbts.utils.ExcelUtils;

import static io.restassured.RestAssured.given;

public class PostWithAccessToken implements ApiMethod {
    private Logger logger;
    private String filePath;
    private String sheetName;
    private int url;
    private int body;
    private String accessToken;

    public PostWithAccessToken(Logger logger, String filePath, String sheetName, int url, int body, String accessToken) {
        this.logger = logger;
        this.filePath = filePath;
        this.sheetName = sheetName;
        this.url = url;
        this.body = body;
        this.accessToken = accessToken;
    }

    /**
     * Make a POST request with the access token
     * @param rowNum Excel row number
     * @return The response from the POST request
     */
    public Response post(int rowNum) {
        ExcelUtils excelUtils = new ExcelUtils();

        // Check if the row and cell data is valid before accessing
        String uri = excelUtils.getCellData(filePath, sheetName, rowNum, url);
        if (uri == null || uri.isEmpty()) {
            logger.error("URL not found in row " + rowNum);
            throw new RuntimeException("URL not found in row " + rowNum);
        }

        String originalRequestBody = excelUtils.getCellData(filePath, sheetName, rowNum, body);
        if (originalRequestBody == null || originalRequestBody.isEmpty()) {
            logger.error("Body not found in row " + rowNum);
            throw new RuntimeException("Body not found in row " + rowNum);
        }

        // Create a proper request body with the access token
        ObjectNode requestNode = createRequestBody(originalRequestBody);

        // Convert to JSON string
        String requestBody;
        try {
            ObjectMapper mapper = new ObjectMapper();
            requestBody = mapper.writeValueAsString(requestNode);
            logger.info("Request body: {}", requestBody);
        } catch (Exception e) {
            logger.error("Error converting request body to JSON: {}", e.getMessage());
            throw new RuntimeException("Error converting request body to JSON", e);
        }

        // Check if this is a decrypt endpoint
        boolean isDecryptEndpoint = uri != null && (uri.contains("/decrypt") || uri.endsWith("/decrypt"));

        // Get the request type
        String requestType = "post"; // Default to POST
        if (requestNode.has("type")) {
            requestType = requestNode.get("type").asText().toLowerCase();
        }

        // For decrypt endpoints, always use POST
        if (isDecryptEndpoint) {
            logger.info("Detected decrypt endpoint at: {}", uri);
            logger.info("Using POST HTTP method with {} operation type in the request body", requestType.toUpperCase());

            // Make the POST request
            Response response = given()
                    .contentType("application/json")
                    .body(requestBody)
                    .when()
                    .log().headers()
                    .post(uri);

            // Log the response details
            logResponse(response);

            return response;
        }

        // For non-decrypt endpoints, use the standard approach based on the request type
        logger.info("Making {} request to URL: {}", requestType.toUpperCase(), uri);

        // Create a request specification
        io.restassured.specification.RequestSpecification requestSpec = given()
                .contentType("application/json")
                .when()
                .log().headers();

        // Add body for non-GET requests
        if (!requestType.equalsIgnoreCase("get")) {
            requestSpec.body(requestBody);
        }

        // Execute the appropriate request type
        Response response;
        switch (requestType.toLowerCase()) {
            case "get":
                response = requestSpec.get(uri);
                break;
            case "put":
                response = requestSpec.put(uri);
                break;
            case "delete":
                response = requestSpec.delete(uri);
                break;
            case "patch":
                response = requestSpec.patch(uri);
                break;
            default: // Default to POST
                response = requestSpec.post(uri);
                break;
        }

        // Log the response details
        logResponse(response);

        return response;
    }

    /**
     * Create a proper request body with the access token
     * @param originalRequestBody The original request body from Excel
     * @return The prepared request node
     */
    private ObjectNode createRequestBody(String originalRequestBody) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode requestNode;

        try {
            // Try to parse the original request body as JSON
            JsonNode root = mapper.readTree(originalRequestBody);

            // Check if it already has the required structure
            if (root.has("endpoint") && root.has("type") && (root.has("payload") || root.has("auth"))) {
                // It already has the required structure, use it as is
                requestNode = root.deepCopy();
                logger.info("Using existing request body structure");
            } else {
                // It doesn't have the required structure, create a new one
                requestNode = mapper.createObjectNode();

                // Try to determine the endpoint from the original body
                if (root.has("entity") || root.has("entityType")) {
                    String entity = root.has("entity") ? root.get("entity").asText() :
                                   (root.has("entityType") ? root.get("entityType").asText() : "CountryMaster");
                    requestNode.put("endpoint", "/core/api/" + entity + "/save");
                } else {
                    // Default endpoint
                    requestNode.put("endpoint", "/core/api/CountryMaster/save");
                }

                requestNode.put("type", "post");

                // Use the original body as the payload
                if (root.isObject()) {
                    // If the original body is an object, use it as the payload
                    requestNode.set("payload", root);
                } else {
                    // Create a simple payload
                    ObjectNode payloadNode = mapper.createObjectNode();
                    payloadNode.put("countryShortName", "TEST" + System.currentTimeMillis());
                    payloadNode.put("countryFullDesc", "TEST COUNTRY " + System.currentTimeMillis());
                    requestNode.set("payload", payloadNode);
                }

                logger.info("Created new request body structure");
            }
        } catch (Exception e) {
            // The original body is not valid JSON, create a new request body
            logger.warn("Failed to parse original request body as JSON: {}", e.getMessage());

            requestNode = mapper.createObjectNode();
            requestNode.put("endpoint", "/core/api/CountryMaster/save");
            requestNode.put("type", "post");

            // Create a simple payload
            ObjectNode payloadNode = mapper.createObjectNode();
            payloadNode.put("countryShortName", "TEST" + System.currentTimeMillis());
            payloadNode.put("countryFullDesc", "TEST COUNTRY " + System.currentTimeMillis());
            requestNode.set("payload", payloadNode);

            logger.info("Created new request body with default payload");
        }

        // Ensure the auth token is set
        requestNode.put("auth", accessToken);

        // Process the endpoint to ensure it has the correct format
        processEndpoint(requestNode);

        return requestNode;
    }

    /**
     * Process the endpoint to ensure it has the correct format
     * @param requestNode The request node containing the endpoint
     */
    private void processEndpoint(ObjectNode requestNode) {
        if (!requestNode.has("endpoint")) {
            return;
        }

        String endpoint = requestNode.get("endpoint").asText();

        // Ensure the endpoint starts with a slash
        if (!endpoint.startsWith("/")) {
            endpoint = "/" + endpoint;
            requestNode.put("endpoint", endpoint);
        }

        // Extract the service name from the endpoint or use a default
        String serviceName = "";

        // Try to extract service name from the endpoint
        if (endpoint.contains("/api/")) {
            // Format: /serviceName/api/...
            int apiIndex = endpoint.indexOf("/api/");
            if (apiIndex > 0) {
                serviceName = endpoint.substring(1, apiIndex); // Extract service name
                logger.info("Extracted service name from endpoint: {}", serviceName);
            }
        }

        // If we couldn't extract a service name, check if the endpoint already has a service prefix
        if (serviceName.isEmpty()) {
            // Check common service patterns in the endpoint
            String[] commonServices = {"core", "auth", "authentication", "contact", "order", "user", "product", "payment"};
            for (String service : commonServices) {
                if (endpoint.startsWith("/" + service + "/api/") || endpoint.contains("/" + service + "/api/")) {
                    serviceName = service;
                    logger.info("Found existing service name in endpoint: {}", serviceName);
                    break;
                }
            }
        }

        // If we still don't have a service name, try to infer it from the endpoint content
        if (serviceName.isEmpty()) {
            if (endpoint.contains("country") || endpoint.contains("state") || endpoint.contains("master")) {
                serviceName = "core";
            } else if (endpoint.contains("auth") || endpoint.contains("login") || endpoint.contains("user")) {
                serviceName = "auth";
            } else if (endpoint.contains("contact") || endpoint.contains("address")) {
                serviceName = "contact";
            } else if (endpoint.contains("order") || endpoint.contains("product")) {
                serviceName = "order";
            } else {
                // Default to "core" if we can't determine the service
                serviceName = "core";
            }
            logger.info("Inferred service name from endpoint content: {}", serviceName);
        }

        // Format the endpoint with the service name
        String requestType = requestNode.has("type") ? requestNode.get("type").asText().toLowerCase() : "post";

        if (endpoint.startsWith("/api/")) {
            // If it already has /api/ but missing the service prefix
            endpoint = "/" + serviceName + endpoint;
            logger.info("Adding service prefix to endpoint: {}", endpoint);
            requestNode.put("endpoint", endpoint);
        } else if (!endpoint.contains("/api/")) {
            // If it doesn't have /api/ at all
            endpoint = "/" + serviceName + "/api" + endpoint;
            logger.info("Adding service and API prefix to endpoint: {}", endpoint);
            requestNode.put("endpoint", endpoint);
        }

        // For POST requests, ensure the endpoint ends with /save if it doesn't already
        if (requestType.equals("post")) {
            if (!endpoint.endsWith("/save") && !endpoint.endsWith("/update")) {
                endpoint = endpoint + (endpoint.endsWith("/") ? "save" : "/save");
                logger.info("Adding /save to POST endpoint: {}", endpoint);
                requestNode.put("endpoint", endpoint);
            }
        }
    }

    /**
     * Log the response details
     * @param response The response to log
     */
    private void logResponse(Response response) {
        String responseBody = response.getBody().asString();
        logger.info("Request Headers: {}", response.getHeaders());
        logger.info("Response Body: {}", responseBody);
        logger.info("Response Cookies: {}", response.getDetailedCookies());
        logger.info("Status Code: {}", response.getStatusCode());
        logger.info("Content-Type: {}", response.getContentType());
    }
}
