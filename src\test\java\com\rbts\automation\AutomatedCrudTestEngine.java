package com.rbts.automation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.rbts.api.ApiRequestHandler;
import com.rbts.comparison.JsonComparator;
import com.rbts.config.ConfigManager;
import com.rbts.config.ExcelConfigManager;
import com.rbts.crud.TestResult;
import com.rbts.database.DatabaseManager;
import com.rbts.defect.DefectManager;
import com.rbts.documentation.TestDocumentationGenerator;
import com.rbts.generator.AutoRequestBodyGenerator;
import com.rbts.generator.AutoUrlGenerator;
import com.rbts.schema.DatabaseSchemaDetector;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * Automated CRUD Test Engine
 * Zero test case writing - fully automated testing based on configuration
 */
@Slf4j
public class AutomatedCrudTestEngine {
    
    private final ConfigManager configManager;
    private final ExcelConfigManager excelConfigManager;
    private final DatabaseSchemaDetector schemaDetector;
    private final AutoRequestBodyGenerator requestBodyGenerator;
    private final AutoUrlGenerator urlGenerator;
    private final ApiRequestHandler apiRequestHandler;
    private final DatabaseManager databaseManager;
    private final DefectManager defectManager;
    private final JsonComparator jsonComparator;
    private final ObjectMapper objectMapper;
    private final TestDocumentationGenerator documentationGenerator;

    public AutomatedCrudTestEngine() {
        this.configManager = ConfigManager.getInstance();
        this.excelConfigManager = ExcelConfigManager.getInstance();
        this.schemaDetector = new DatabaseSchemaDetector();
        this.requestBodyGenerator = new AutoRequestBodyGenerator();
        this.urlGenerator = new AutoUrlGenerator();
        this.apiRequestHandler = new ApiRequestHandler();
        this.databaseManager = new DatabaseManager();
        this.defectManager = new DefectManager();
        this.jsonComparator = new JsonComparator();
        this.objectMapper = new ObjectMapper();
        this.documentationGenerator = new TestDocumentationGenerator();
    }
    
    /**
     * Execute all automated tests for all configured tables
     */
    public Map<String, List<TestResult>> executeAllAutomatedTests() {
        Map<String, List<TestResult>> allResults = new HashMap<>();
        
        log.info("Starting automated CRUD testing for all configured tables");
        
        // Get all configured services and their tables
        Set<String> services = urlGenerator.getConfiguredServices();
        
        for (String service : services) {
            log.info("Testing service: {}", service);
            List<String> tables = urlGenerator.getTablesForService(service);
            
            for (String table : tables) {
                log.info("Testing table: {} in service: {}", table, service);
                
                // Check if table exists in database
                if (!schemaDetector.tableExists(table)) {
                    log.warn("Table {} does not exist in database, skipping", table);
                    continue;
                }
                
                List<TestResult> tableResults = executeAutomatedTestsForTable(service, table);
                allResults.put(service + "." + table, tableResults);
            }
        }
        
        log.info("Completed automated CRUD testing. Total tables tested: {}", allResults.size());

        // Generate test documentation
        generateTestDocumentation();

        return allResults;
    }

    /**
     * Generate comprehensive test documentation Excel file
     */
    public void generateTestDocumentation() {
        String documentationFilePath = "data/Automated_CRUD_Test_Documentation.xlsx";
        log.info("Generating test documentation: {}", documentationFilePath);

        try {
            documentationGenerator.generateTestDocumentation(documentationFilePath);
            log.info("Test documentation generated successfully: {}", documentationFilePath);
        } catch (Exception e) {
            log.error("Error generating test documentation: {}", e.getMessage());
        }
    }

    /**
     * Generate test documentation with custom file path
     */
    public void generateTestDocumentation(String filePath) {
        log.info("Generating test documentation: {}", filePath);

        try {
            documentationGenerator.generateTestDocumentation(filePath);
            log.info("Test documentation generated successfully: {}", filePath);
        } catch (Exception e) {
            log.error("Error generating test documentation: {}", e.getMessage());
        }
    }
    
    /**
     * Execute automated tests for a specific table
     */
    public List<TestResult> executeAutomatedTestsForTable(String service, String tableName) {
        List<TestResult> results = new ArrayList<>();
        
        log.info("Executing automated tests for table: {} in service: {}", tableName, service);
        
        // Get operations to test for this specific table from Excel configuration
        List<String> operationsToTest = excelConfigManager.getOperationsToTestForTable(tableName);
        String[] testTypes = configManager.getProperty("auto.test.types", "normal,null_constraint,unique_constraint,foreign_key_invalid").split(",");

        if (operationsToTest.isEmpty()) {
            log.info("No operations configured for testing table: {}. Skipping all tests.", tableName);
            return results;
        }

        log.info("Operations to test for table {}: {}", tableName, operationsToTest);

        for (String testType : testTypes) {
            for (String operation : operationsToTest) {
                String trimmedTestType = testType.trim();
                String trimmedOperation = operation.trim();

                // Skip certain combinations
                if (shouldSkipTest(trimmedTestType, trimmedOperation)) {
                    continue;
                }
                
                try {
                    TestResult result = executeAutomatedTest(service, tableName, trimmedOperation, trimmedTestType);
                    results.add(result);
                    
                    log.info("Test completed: {} {} {} - {}", 
                            tableName, trimmedOperation, trimmedTestType, 
                            result.isTestPassed() ? "PASSED" : "FAILED");
                    
                } catch (Exception e) {
                    log.error("Error executing test for {} {} {}: {}", 
                            tableName, trimmedOperation, trimmedTestType, e.getMessage());
                    
                    TestResult errorResult = new TestResult(tableName, trimmedOperation, trimmedTestType);
                    errorResult.setTestPassed(false);
                    errorResult.setComparisonMessage("Test execution error: " + e.getMessage());
                    results.add(errorResult);
                }
            }
        }
        
        return results;
    }
    
    /**
     * Execute a single automated test
     */
    public TestResult executeAutomatedTest(String service, String tableName, String operation, String testType) {
        TestResult result = new TestResult(tableName, operation, testType);
        
        try {
            // Generate request body based on test type
            String requestBody = generateRequestBodyForTest(tableName, operation, testType);
            result.setRequestBody(requestBody);
            
            // Generate URL based on pattern
            String url = generateUrlForTest(service, tableName, operation);
            result.setFullUrl(url);
            
            // Execute API request
            Response response = executeApiRequest(service, tableName, operation, requestBody);
            result.setStatusCode(response.getStatusCode());
            result.setResponseBody(response.getBody().asString());
            
            // Validate response based on test type
            validateResponse(result, response, testType, operation);
            
            // For successful normal tests, validate database
            if ("normal".equals(testType) && result.isApiResponseSuccessful()) {
                validateDatabaseForNormalTest(result, tableName, response);
            }
            
        } catch (Exception e) {
            log.error("Error in automated test execution: {}", e.getMessage());
            result.setTestPassed(false);
            result.setComparisonMessage("Test execution error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Generate request body based on test type
     */
    private String generateRequestBodyForTest(String tableName, String operation, String testType) {
        switch (testType.toLowerCase()) {
            case "normal":
                return requestBodyGenerator.generateNormalRequestBody(tableName, operation);
            case "null_constraint":
                return requestBodyGenerator.generateNullConstraintViolationBody(tableName, operation);
            case "unique_constraint":
                return requestBodyGenerator.generateUniqueConstraintViolationBody(tableName, operation);
            case "foreign_key_invalid":
                return requestBodyGenerator.generateInvalidForeignKeyBody(tableName, operation);
            default:
                return requestBodyGenerator.generateNormalRequestBody(tableName, operation);
        }
    }
    
    /**
     * Generate URL for test
     */
    private String generateUrlForTest(String service, String tableName, String operation) {
        return urlGenerator.generateUrl(tableName, operation);
    }
    
    /**
     * Execute API request using appropriate pattern
     */
    private Response executeApiRequest(String service, String tableName, String operation, String requestBody) {
        String pattern = urlGenerator.getServicePattern(service);
        
        if ("proxy".equalsIgnoreCase(pattern)) {
            return executeProxyRequest(service, tableName, operation, requestBody);
        } else {
            return executeDirectRequest(service, tableName, operation, requestBody);
        }
    }
    
    /**
     * Execute direct pattern request
     */
    private Response executeDirectRequest(String service, String tableName, String operation, String requestBody) {
        // For operations that don't need request body
        if ("get".equalsIgnoreCase(operation) || "getall".equalsIgnoreCase(operation) || "delete".equalsIgnoreCase(operation)) {
            requestBody = null;
        }
        
        return apiRequestHandler.executeRequest(tableName, operation, requestBody);
    }
    
    /**
     * Execute proxy pattern request
     */
    private Response executeProxyRequest(String service, String tableName, String operation, String requestBody) {
        try {
            String internalEndpoint = urlGenerator.generateProxyInternalEndpoint(service, tableName, operation);
            String tenantId = configManager.getTenantId();

            // Create proxy request body
            ObjectNode proxyRequestBody = objectMapper.createObjectNode();
            proxyRequestBody.put("endpoint", internalEndpoint);
            proxyRequestBody.put("type", operation.toLowerCase());
            proxyRequestBody.put("tenantId", tenantId);
            proxyRequestBody.putNull("auth");

            // Add payload for operations that need it
            if (requestBody != null && !requestBody.trim().isEmpty()) {
                Object payloadObject = objectMapper.readValue(requestBody, Object.class);
                proxyRequestBody.set("payload", objectMapper.valueToTree(payloadObject));
            } else {
                proxyRequestBody.putNull("payload");
            }

            String proxyRequestBodyString = objectMapper.writeValueAsString(proxyRequestBody);
            return apiRequestHandler.executeRequest(tableName, "post", proxyRequestBodyString);
            
        } catch (Exception e) {
            throw new RuntimeException("Error executing proxy request", e);
        }
    }
    
    /**
     * Validate response based on test type
     */
    private void validateResponse(TestResult result, Response response, String testType, String operation) {
        int statusCode = response.getStatusCode();
        
        switch (testType.toLowerCase()) {
            case "normal":
                validateNormalResponse(result, statusCode, operation);
                break;
            case "null_constraint":
            case "unique_constraint":
            case "foreign_key_invalid":
                validateConstraintViolationResponse(result, statusCode, testType);
                break;
        }
    }
    
    /**
     * Validate normal response status codes
     */
    private void validateNormalResponse(TestResult result, int statusCode, String operation) {
        String expectedStatusCodes = configManager.getProperty("validation.status_code." + operation.toLowerCase(), "200");
        String[] validCodes = expectedStatusCodes.split(",");
        
        boolean isValidStatus = Arrays.stream(validCodes)
                .anyMatch(code -> Integer.parseInt(code.trim()) == statusCode);
        
        if (isValidStatus) {
            result.setTestPassed(true);
            result.setComparisonMessage("API response status code validation passed");
        } else {
            result.setTestPassed(false);
            result.setComparisonMessage("Invalid status code. Expected: " + expectedStatusCodes + ", Actual: " + statusCode);
            
            // Generate defect for invalid status code
            String defectId = defectManager.generateDefectId(result.getEntity(), operation, "status_code");
            result.setDefectId(defectId);
            
            defectManager.createDefectRecord(
                defectId, result.getEntity(), operation.toUpperCase(), "STATUS_CODE_VALIDATION",
                "Invalid status code for " + operation + " operation",
                "Status code: " + expectedStatusCodes, "Status code: " + statusCode, result.getRequestBody()
            );
        }
    }
    
    /**
     * Validate constraint violation response
     */
    private void validateConstraintViolationResponse(TestResult result, int statusCode, String testType) {
        String tableName = result.getEntity();

        // Get expected status code based on constraint type and service relationship
        String expectedStatusCodes;
        if (testType.equals("foreign_key_invalid")) {
            // For foreign key violations, we need to determine the foreign key table
            // This would typically come from the request body or constraint configuration
            String foreignKeyTable = extractForeignKeyTableFromConstraint(tableName, testType);
            expectedStatusCodes = excelConfigManager.getConstraintViolationStatusCodeForTable(testType, tableName, foreignKeyTable);
        } else {
            // For unique and null constraints, no foreign key table needed
            expectedStatusCodes = excelConfigManager.getConstraintViolationStatusCodeForTable(testType, tableName, null);
        }

        // Fallback to general constraint violation status codes if specific ones not found
        if (expectedStatusCodes == null || expectedStatusCodes.trim().isEmpty()) {
            expectedStatusCodes = configManager.getProperty("validation.constraint_violation.expected_status", "400,422");
        }

        String[] validCodes = expectedStatusCodes.split(",");
        boolean isValidStatus = Arrays.stream(validCodes)
                .anyMatch(code -> Integer.parseInt(code.trim()) == statusCode);

        if (isValidStatus) {
            result.setTestPassed(true);
            result.setComparisonMessage(String.format("Constraint violation correctly rejected by API. Expected: %s, Actual: %d", expectedStatusCodes, statusCode));
            log.info("✅ Constraint validation passed for {} - {} constraint, status: {}", tableName, testType, statusCode);
        } else {
            result.setTestPassed(false);
            result.setComparisonMessage(String.format("Constraint violation was not properly handled. Expected: %s, Actual: %d", expectedStatusCodes, statusCode));

            // Generate defect for constraint not enforced
            String defectId = defectManager.generateDefectId(result.getEntity(), result.getOperation(), testType);
            result.setDefectId(defectId);

            defectManager.createDefectRecord(
                defectId, result.getEntity(), result.getOperation().toUpperCase(), testType.toUpperCase(),
                "Constraint violation was not properly handled",
                "Status code: " + expectedStatusCodes, "Status code: " + statusCode, result.getRequestBody()
            );

            log.warn("❌ Constraint validation failed for {} - {} constraint. Expected: {}, Actual: {}",
                    tableName, testType, expectedStatusCodes, statusCode);
        }
    }

    /**
     * Extract foreign key table from constraint configuration
     * This is a simplified implementation - in practice, this would analyze the request body
     * or constraint configuration to determine which foreign key is being violated
     */
    private String extractForeignKeyTableFromConstraint(String tableName, String testType) {
        // This is a placeholder implementation
        // In a real scenario, you would:
        // 1. Parse the request body to find foreign key fields
        // 2. Look up constraint configuration to find foreign key relationships
        // 3. Return the referenced table name

        // For demonstration, return a sample foreign key table based on common patterns
        if ("Order".equals(tableName)) {
            return "User"; // Order typically references User (different service)
        } else if ("OrderItem".equals(tableName)) {
            return "Order"; // OrderItem references Order (same service)
        } else if ("Contact".equals(tableName)) {
            return "Country"; // Contact references Country (different service)
        }

        return null; // No foreign key relationship identified
    }
    
    /**
     * Validate database for normal tests
     */
    private void validateDatabaseForNormalTest(TestResult result, String tableName, Response response) {
        try {
            // Extract ID from response
            String extractedId = extractIdFromResponse(response.getBody().asString(), tableName);
            
            if (extractedId != null) {
                result.setExtractedId(extractedId);
                
                // Query database and compare
                String actualResult = databaseManager.queryByIdAndConvertToJson(tableName, extractedId);
                result.setActualResult(actualResult);
                
                // For automated tests, we consider it passed if we can extract ID and query database
                // More sophisticated comparison can be added based on requirements
                result.setTestPassed(true);
                result.setComparisonMessage("Database validation completed successfully");
            } else {
                result.setTestPassed(false);
                result.setComparisonMessage("Could not extract ID from response for database validation");
            }
            
        } catch (Exception e) {
            log.warn("Database validation failed: {}", e.getMessage());
            result.setComparisonMessage("Database validation error: " + e.getMessage());
        }
    }
    
    /**
     * Extract ID from response
     */
    private String extractIdFromResponse(String responseBody, String tableName) {
        // Implementation similar to CrudTestExecutor
        try {
            com.fasterxml.jackson.databind.JsonNode responseNode = objectMapper.readTree(responseBody);
            
            // Try common ID field patterns
            String[] idFields = {"id", "Id", "ID", tableName + "_id", tableName + "Id"};
            for (String idField : idFields) {
                if (responseNode.has(idField)) {
                    return responseNode.get(idField).asText();
                }
            }
        } catch (Exception e) {
            log.warn("Error extracting ID from response: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Check if test should be skipped
     */
    private boolean shouldSkipTest(String testType, String operation) {
        // Skip constraint tests for GET operations
        if (("null_constraint".equals(testType) || "unique_constraint".equals(testType) || "foreign_key_invalid".equals(testType)) &&
            ("get".equalsIgnoreCase(operation) || "getall".equalsIgnoreCase(operation) || "delete".equalsIgnoreCase(operation))) {
            return true;
        }
        
        return false;
    }
}
