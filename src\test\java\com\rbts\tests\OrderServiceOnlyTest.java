package com.rbts.tests;

import com.rbts.config.ServiceSpecificConfigManager;
import com.rbts.config.ServiceSpecificConfigTemplateGenerator;
import com.rbts.config.ServiceTableConfig;
import com.rbts.core.ErrorMessageTester;
import com.rbts.reporting.TestCaseResult;
import com.rbts.reporting.TestExecutionReporter;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * TestNG test class for Order Service only
 * Demonstrates testing a single service using service-specific configuration
 */
@Slf4j
public class OrderServiceOnlyTest {
    
    private ServiceSpecificConfigManager configManager;
    private TestExecutionReporter testReporter;
    private ErrorMessageTester errorMessageTester;
    private final String SERVICE_NAME = "order";
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up Order Service Only Test");
        
        // Generate service-specific configuration template
        ServiceSpecificConfigTemplateGenerator templateGenerator = new ServiceSpecificConfigTemplateGenerator();
        templateGenerator.generateServiceSpecificConfigurationTemplate();
        
        // Initialize managers
        configManager = ServiceSpecificConfigManager.getInstance();
        configManager.loadConfigurations();
        testReporter = new TestExecutionReporter();
        errorMessageTester = new ErrorMessageTester();
        
        // Validate Order service is configured
        validateOrderServiceConfiguration();
        
        log.info("✅ Order Service Only Test setup completed");
    }
    
    /**
     * Validate Order service configuration exists
     */
    private void validateOrderServiceConfiguration() {
        Set<String> configuredServices = configManager.getConfiguredServices();
        
        if (!configuredServices.contains(SERVICE_NAME)) {
            log.error("❌ Order service not found in configuration. Available services: {}", configuredServices);
            throw new RuntimeException("Order service configuration not found");
        }
        
        Set<String> orderTables = configManager.getTablesForService(SERVICE_NAME);
        log.info("📋 Order service tables configured: {}", orderTables);
        
        if (orderTables.isEmpty()) {
            log.error("❌ No tables configured for Order service");
            throw new RuntimeException("No tables configured for Order service");
        }
    }
    
    /**
     * Data provider for Order service tables
     */
    @DataProvider(name = "orderTables")
    public Object[][] getOrderTableData() {
        List<Object[]> tableData = new ArrayList<>();
        
        Set<String> orderTables = configManager.getTablesForService(SERVICE_NAME);
        
        for (String tableName : orderTables) {
            // Check if table is enabled for testing
            if (configManager.isTableEnabledForTesting(SERVICE_NAME, tableName)) {
                // Check if POST operation is configured
                String postEndpoint = configManager.getApiEndpoint(SERVICE_NAME, tableName, "POST");
                if (postEndpoint != null && !postEndpoint.trim().isEmpty()) {
                    tableData.add(new Object[]{tableName});
                    log.info("📋 Added Order table for testing: {}", tableName);
                } else {
                    log.info("⏭️ Skipping Order table {} - no POST endpoint configured", tableName);
                }
            } else {
                log.info("⏭️ Skipping Order table {} - testing disabled", tableName);
            }
        }
        
        if (tableData.isEmpty()) {
            log.warn("⚠️ No Order tables enabled for testing");
            // Add a dummy entry to prevent DataProvider from failing
            tableData.add(new Object[]{"NoTablesConfigured"});
        }
        
        log.info("🎯 Total Order tables selected for testing: {}", tableData.size());
        return tableData.toArray(new Object[0][]);
    }
    
    /**
     * Test Order service POST operations
     */
    @Test(dataProvider = "orderTables", description = "Test Order service POST operations")
    public void testOrderServicePostOperation(String tableName) {
        if ("NoTablesConfigured".equals(tableName)) {
            log.warn("⚠️ No tables configured for testing - skipping");
            return;
        }
        
        log.info("🧪 Testing POST operation for Order.{}", tableName);
        
        try {
            // Generate request body for Order table
            JSONObject requestBody = generateOrderRequestBody(tableName);
            log.info("📝 Generated request body: {}", requestBody.toString());
            
            // Get API endpoint
            String endpoint = configManager.getApiEndpoint(SERVICE_NAME, tableName, "POST");
            log.info("🌐 POST Endpoint: {}", endpoint);
            
            // Execute API call
            Response apiResponse = executeOrderApiCall("POST", endpoint, requestBody.toString());
            log.info("📡 API Response - Status: {}, Body: {}", apiResponse.getStatusCode(), apiResponse.getBody().asString());
            
            // Validate response
            validateOrderPostResponse(tableName, apiResponse, requestBody);
            
            // Report test result
            reportOrderTestResult(tableName, "POST", requestBody, apiResponse);
            
        } catch (Exception e) {
            log.error("❌ Error testing Order.{} POST operation: {}", tableName, e.getMessage());
            
            TestCaseResult failResult = TestCaseResult.fail(tableName, "POST", "Order POST operation", 
                    "Successful API call", "Exception: " + e.getMessage(), e.getMessage());
            testReporter.reportTestCaseWithColorCoding(failResult);
        }
    }
    
    /**
     * Test Order service constraint validations
     */
    @Test(dataProvider = "orderTables", description = "Test Order service constraint validations")
    public void testOrderServiceConstraints(String tableName) {
        if ("NoTablesConfigured".equals(tableName)) {
            return;
        }
        
        log.info("🔍 Testing constraints for Order.{}", tableName);
        
        try {
            // Get all constraint configurations for this table
            List<ServiceTableConfig> tableConfigs = configManager.getTableConfigurations(SERVICE_NAME, tableName);
            
            for (ServiceTableConfig config : tableConfigs) {
                if (config.hasConstraintValidation()) {
                    testOrderConstraint(tableName, config);
                }
            }
            
        } catch (Exception e) {
            log.error("❌ Error testing Order.{} constraints: {}", tableName, e.getMessage());
        }
    }
    
    /**
     * Test specific Order constraint
     */
    private void testOrderConstraint(String tableName, ServiceTableConfig config) {
        log.info("🔍 Testing {} constraint for Order.{}.{}", 
                config.getConstraintType(), tableName, config.getDatabaseFieldName());
        
        try {
            // Generate constraint violation request
            JSONObject requestBody = generateConstraintViolationRequest(tableName, config);
            
            // Get API endpoint
            String endpoint = configManager.getApiEndpoint(SERVICE_NAME, tableName, "POST");
            
            // Execute API call
            Response apiResponse = executeOrderApiCall("POST", endpoint, requestBody.toString());
            
            // Validate constraint violation response
            validateConstraintViolationResponse(config, apiResponse);
            
            // Report constraint test result
            reportConstraintTestResult(tableName, config, requestBody, apiResponse);
            
        } catch (Exception e) {
            log.error("❌ Error testing constraint {}: {}", config.getConstraintType(), e.getMessage());
        }
    }
    
    /**
     * Generate request body for Order table
     */
    private JSONObject generateOrderRequestBody(String tableName) {
        JSONObject requestBody = new JSONObject();
        
        try {
            List<ServiceTableConfig> tableConfigs = configManager.getTableConfigurations(SERVICE_NAME, tableName);
            
            for (ServiceTableConfig config : tableConfigs) {
                // Only include fields that should be in POST requests
                if (config.isPresentInRequest("POST") && !config.isPrimaryKey()) {
                    String apiFieldName = config.getApiRequestFieldName();
                    Object fieldValue = generateValidFieldValue(config);
                    requestBody.put(apiFieldName, fieldValue);
                    
                    log.debug("Added field to request: {} = {}", apiFieldName, fieldValue);
                }
            }
            
            // Add default Order-specific fields if not configured
            if (!requestBody.has("customerId")) {
                requestBody.put("customerId", 1);
            }
            if (!requestBody.has("orderItems")) {
                requestBody.put("orderItems", generateOrderItems());
            }
            if (!requestBody.has("orderDate")) {
                requestBody.put("orderDate", "2024-01-01T10:00:00Z");
            }
            
        } catch (Exception e) {
            log.error("Error generating Order request body: {}", e.getMessage());
            // Fallback to minimal Order request
            requestBody.put("customerId", 1);
            requestBody.put("orderItems", generateOrderItems());
            requestBody.put("orderDate", "2024-01-01T10:00:00Z");
        }
        
        return requestBody;
    }
    
    /**
     * Generate order items for Order request
     */
    private List<Object> generateOrderItems() {
        List<Object> orderItems = new ArrayList<>();
        
        JSONObject item1 = new JSONObject();
        item1.put("productId", 1);
        item1.put("quantity", 2);
        item1.put("unitPrice", 29.99);
        
        JSONObject item2 = new JSONObject();
        item2.put("productId", 2);
        item2.put("quantity", 1);
        item2.put("unitPrice", 49.99);
        
        orderItems.add(item1);
        orderItems.add(item2);
        
        return orderItems;
    }
    
    /**
     * Generate constraint violation request
     */
    private JSONObject generateConstraintViolationRequest(String tableName, ServiceTableConfig config) {
        JSONObject requestBody = generateOrderRequestBody(tableName);
        
        // Override the specific field with constraint violation value
        String fieldName = config.getApiRequestFieldName();
        Object testValue = config.getTestValueAsObject();
        
        if (fieldName != null && !fieldName.trim().isEmpty()) {
            requestBody.put(fieldName, testValue);
            log.info("Set constraint violation: {} = {}", fieldName, testValue);
        }
        
        return requestBody;
    }
    
    /**
     * Generate valid field value based on configuration
     */
    private Object generateValidFieldValue(ServiceTableConfig config) {
        String fieldType = config.getFieldType();
        String fieldName = config.getDatabaseFieldName();
        
        if (fieldType == null) {
            return "Valid Value";
        }
        
        switch (fieldType.toUpperCase()) {
            case "STRING":
                return "Valid " + fieldName;
            case "INTEGER":
            case "FOREIGN_KEY":
                return 1;
            case "DECIMAL":
                return 29.99;
            case "BOOLEAN":
                return true;
            case "TIMESTAMP":
                return "2024-01-01T10:00:00Z";
            case "EMAIL":
                return "<EMAIL>";
            default:
                return "Valid Value";
        }
    }
    
    /**
     * Execute Order API call
     */
    private Response executeOrderApiCall(String method, String endpoint, String requestBody) {
        log.info("📡 Executing {} call to Order API: {}", method, endpoint);
        
        String baseUrl = configManager.getGeneralConfig("base.url.direct");
        String fullUrl = baseUrl + endpoint;
        
        return RestAssured.given()
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer order-service-token")
                .body(requestBody)
                .when()
                .post(fullUrl)
                .then()
                .extract()
                .response();
    }
    
    /**
     * Validate Order POST response
     */
    private void validateOrderPostResponse(String tableName, Response response, JSONObject requestBody) {
        int statusCode = response.getStatusCode();
        String responseBody = response.getBody().asString();
        
        // Check for success status codes
        int expectedSuccessCode = configManager.getStatusCode("POST", "success");
        
        if (statusCode == expectedSuccessCode || statusCode == 201 || statusCode == 200) {
            log.info("✅ Order.{} POST successful - Status: {}", tableName, statusCode);
            
            // Validate response contains ID (for successful creation)
            try {
                JSONObject responseJson = new JSONObject(responseBody);
                if (responseJson.has("id")) {
                    int generatedId = responseJson.getInt("id");
                    log.info("✅ Order.{} created with ID: {}", tableName, generatedId);
                }
            } catch (Exception e) {
                log.warn("Could not parse response JSON: {}", e.getMessage());
            }
        } else {
            log.warn("⚠️ Order.{} POST returned status: {} - {}", tableName, statusCode, responseBody);
        }
    }
    
    /**
     * Validate constraint violation response
     */
    private void validateConstraintViolationResponse(ServiceTableConfig config, Response response) {
        int actualStatusCode = response.getStatusCode();
        String actualMessage = response.getBody().asString();
        
        // Get expected status code for this constraint type
        int expectedStatusCode = configManager.getStatusCode("POST", config.getConstraintType());
        
        boolean statusCodeValid = (actualStatusCode == expectedStatusCode);
        boolean messageValid = config.validateErrorMessage(actualMessage);
        
        if (statusCodeValid && messageValid) {
            log.info("✅ Constraint validation passed for {}: Status {}, Message validated", 
                    config.getConstraintType(), actualStatusCode);
        } else {
            log.warn("⚠️ Constraint validation failed for {}: Expected status {}, got {}. Message valid: {}", 
                    config.getConstraintType(), expectedStatusCode, actualStatusCode, messageValid);
        }
    }
    
    /**
     * Report Order test result
     */
    private void reportOrderTestResult(String tableName, String operation, JSONObject requestBody, Response response) {
        String testCase = String.format("Order.%s %s Operation", tableName, operation);
        String expectedResult = "Successful API call with appropriate response";
        String actualResult = String.format("Status: %d, Response: %s", 
                response.getStatusCode(), response.getBody().asString());
        
        boolean isSuccess = (response.getStatusCode() >= 200 && response.getStatusCode() < 300);
        String status = isSuccess ? "PASS" : "FAIL";
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName("Order." + tableName)
                .operation(operation)
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody.toString())
                .responseBody(response.getBody().asString())
                .statusCode(response.getStatusCode())
                .build();
        
        testReporter.reportTestCaseWithColorCoding(result);
    }
    
    /**
     * Report constraint test result
     */
    private void reportConstraintTestResult(String tableName, ServiceTableConfig config, 
                                          JSONObject requestBody, Response response) {
        String testCase = String.format("Order.%s %s Constraint", tableName, config.getConstraintType());
        String expectedResult = String.format("Status: %d, Message: %s", 
                configManager.getStatusCode("POST", config.getConstraintType()), 
                config.getExpectedErrorMessage());
        String actualResult = String.format("Status: %d, Message: %s", 
                response.getStatusCode(), response.getBody().asString());
        
        boolean statusValid = (response.getStatusCode() == configManager.getStatusCode("POST", config.getConstraintType()));
        boolean messageValid = config.validateErrorMessage(response.getBody().asString());
        boolean isSuccess = statusValid && messageValid;
        
        String status = isSuccess ? "PASS" : "FAIL";
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName("Order." + tableName)
                .operation("POST")
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody.toString())
                .responseBody(response.getBody().asString())
                .statusCode(response.getStatusCode())
                .build();
        
        testReporter.reportTestCaseWithColorCoding(result);
    }
    
    /**
     * Order service test summary
     */
    @Test(description = "Order service test summary", dependsOnMethods = {"testOrderServicePostOperation", "testOrderServiceConstraints"})
    public void orderServiceTestSummary() {
        log.info("📊 ORDER SERVICE TEST SUMMARY");
        log.info("=============================");
        
        Set<String> orderTables = configManager.getTablesForService(SERVICE_NAME);
        int totalTestCases = testReporter.getTotalTestCases();
        int totalDefects = testReporter.getTotalDefects();
        String reportFile = testReporter.getReportFilePath();
        
        log.info("🏢 Service Tested: {}", SERVICE_NAME.toUpperCase());
        log.info("📋 Tables Configured: {}", orderTables);
        log.info("📊 Total Test Cases: {}", totalTestCases);
        log.info("🐛 Total Defects: {}", totalDefects);
        log.info("📁 Test Report: {}", reportFile);
        
        log.info("");
        log.info("🎯 ORDER SERVICE TESTING FEATURES DEMONSTRATED:");
        log.info("✅ Service-specific configuration loading");
        log.info("✅ Order table POST operation testing");
        log.info("✅ Order-specific request body generation");
        log.info("✅ Order constraint validation testing");
        log.info("✅ Order API endpoint configuration");
        log.info("✅ Order service error message validation");
        log.info("✅ Excel reporting with Order test results");
        
        log.info("");
        log.info("📋 ORDER SERVICE CONFIGURATION USED:");
        for (String table : orderTables) {
            List<String> operations = configManager.getOperationsToTest(SERVICE_NAME, table);
            boolean enabled = configManager.isTableEnabledForTesting(SERVICE_NAME, table);
            log.info("  📋 {}: Enabled={}, Operations={}", table, enabled, operations);
        }
        
        log.info("");
        log.info("🚀 NEXT STEPS FOR ORDER SERVICE:");
        log.info("1. Configure your actual Order service API endpoints");
        log.info("2. Update Order_Service_Config sheet with your table structures");
        log.info("3. Add Order-specific constraint validations");
        log.info("4. Run tests against your actual Order service");
        
        Assert.assertTrue(orderTables.size() > 0, "Should have Order tables configured");
    }
}
