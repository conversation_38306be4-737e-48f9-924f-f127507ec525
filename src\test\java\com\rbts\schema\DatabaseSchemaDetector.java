package com.rbts.schema;

import com.rbts.config.ConfigManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;

/**
 * Database Schema Auto-Detection Component
 * Automatically detects table structure, constraints, and relationships
 */
@Slf4j
public class DatabaseSchemaDetector {
    
    private final ConfigManager configManager;
    
    public DatabaseSchemaDetector() {
        this.configManager = ConfigManager.getInstance();
    }
    
    /**
     * Get complete table schema information
     */
    public TableSchema getTableSchema(String tableName) {
        TableSchema schema = new TableSchema();
        schema.setTableName(tableName.toLowerCase());
        
        try (Connection connection = getConnection()) {
            schema.setColumns(getTableColumns(connection, tableName));
            schema.setPrimaryKeys(getPrimaryKeys(connection, tableName));
            schema.setForeignKeys(getForeignKeys(connection, tableName));
            schema.setUniqueConstraints(getUniqueConstraints(connection, tableName));
            schema.setNotNullConstraints(getNotNullConstraints(connection, tableName));
            
            log.info("Schema detected for table {}: {} columns, {} foreign keys, {} unique constraints", 
                    tableName, schema.getColumns().size(), schema.getForeignKeys().size(), 
                    schema.getUniqueConstraints().size());
            
        } catch (SQLException e) {
            log.error("Error detecting schema for table {}: {}", tableName, e.getMessage());
            throw new RuntimeException("Schema detection failed", e);
        }
        
        return schema;
    }
    
    /**
     * Get all columns for a table
     */
    private List<ColumnInfo> getTableColumns(Connection connection, String tableName) throws SQLException {
        List<ColumnInfo> columns = new ArrayList<>();
        String query = configManager.getProperty("schema.query.columns");
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName.toLowerCase());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ColumnInfo column = new ColumnInfo();
                    column.setColumnName(rs.getString("column_name"));
                    column.setDataType(rs.getString("data_type"));
                    column.setNullable("YES".equals(rs.getString("is_nullable")));
                    column.setDefaultValue(rs.getString("column_default"));
                    columns.add(column);
                }
            }
        }
        
        return columns;
    }
    
    /**
     * Get primary key columns
     */
    private List<String> getPrimaryKeys(Connection connection, String tableName) throws SQLException {
        List<String> primaryKeys = new ArrayList<>();
        String query = configManager.getProperty("schema.query.primary_keys");
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName.toLowerCase());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    primaryKeys.add(rs.getString("column_name"));
                }
            }
        }
        
        return primaryKeys;
    }
    
    /**
     * Get foreign key relationships
     */
    private List<ForeignKeyInfo> getForeignKeys(Connection connection, String tableName) throws SQLException {
        List<ForeignKeyInfo> foreignKeys = new ArrayList<>();
        String query = configManager.getProperty("schema.query.foreign_keys");
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName.toLowerCase());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ForeignKeyInfo fk = new ForeignKeyInfo();
                    fk.setColumnName(rs.getString("column_name"));
                    fk.setReferencedTable(rs.getString("referenced_table_name"));
                    fk.setReferencedColumn(rs.getString("referenced_column_name"));
                    foreignKeys.add(fk);
                }
            }
        }
        
        return foreignKeys;
    }
    
    /**
     * Get unique constraint columns
     */
    private List<String> getUniqueConstraints(Connection connection, String tableName) throws SQLException {
        List<String> uniqueConstraints = new ArrayList<>();
        String query = configManager.getProperty("schema.query.unique_constraints");
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName.toLowerCase());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    uniqueConstraints.add(rs.getString("column_name"));
                }
            }
        }
        
        return uniqueConstraints;
    }
    
    /**
     * Get not null constraint columns
     */
    private List<String> getNotNullConstraints(Connection connection, String tableName) throws SQLException {
        List<String> notNullConstraints = new ArrayList<>();
        String query = configManager.getProperty("schema.query.not_null_constraints");
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName.toLowerCase());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    notNullConstraints.add(rs.getString("column_name"));
                }
            }
        }
        
        return notNullConstraints;
    }
    
    /**
     * Get existing foreign key value for testing
     */
    public Object getExistingForeignKeyValue(String referencedTable, String referencedColumn) {
        String query = String.format("SELECT %s FROM %s WHERE %s IS NOT NULL LIMIT 1", 
                referencedColumn, referencedTable, referencedColumn);
        
        try (Connection connection = getConnection();
             PreparedStatement stmt = connection.prepareStatement(query);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getObject(1);
            }
            
        } catch (SQLException e) {
            log.warn("Could not get existing foreign key value from {}.{}: {}", 
                    referencedTable, referencedColumn, e.getMessage());
        }
        
        // Return fallback value
        return Integer.parseInt(configManager.getProperty("foreign.key.fallback.value", "1"));
    }
    
    /**
     * Get invalid foreign key value for testing constraint violations
     */
    public Object getInvalidForeignKeyValue(String referencedTable, String referencedColumn) {
        String query = String.format("SELECT MAX(%s) + 1000 FROM %s", referencedColumn, referencedTable);
        
        try (Connection connection = getConnection();
             PreparedStatement stmt = connection.prepareStatement(query);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                Object maxValue = rs.getObject(1);
                if (maxValue != null) {
                    return ((Number) maxValue).intValue() + 1000;
                }
            }
            
        } catch (SQLException e) {
            log.warn("Could not get invalid foreign key value for {}.{}: {}", 
                    referencedTable, referencedColumn, e.getMessage());
        }
        
        // Return a large number that likely doesn't exist
        return 999999;
    }
    
    /**
     * Check if table exists
     */
    public boolean tableExists(String tableName) {
        String query = "SELECT 1 FROM information_schema.tables WHERE table_name = ?";
        
        try (Connection connection = getConnection();
             PreparedStatement stmt = connection.prepareStatement(query)) {
            
            stmt.setString(1, tableName.toLowerCase());
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
            
        } catch (SQLException e) {
            log.error("Error checking if table {} exists: {}", tableName, e.getMessage());
            return false;
        }
    }
    
    /**
     * Get database connection
     */
    private Connection getConnection() throws SQLException {
        String url = configManager.getJdbcUrl();
        String user = configManager.getJdbcUser();
        String password = configManager.getJdbcPassword();
        
        return DriverManager.getConnection(url, user, password);
    }
    
    // Data classes for schema information
    
    @Data
    public static class TableSchema {
        private String tableName;
        private List<ColumnInfo> columns = new ArrayList<>();
        private List<String> primaryKeys = new ArrayList<>();
        private List<ForeignKeyInfo> foreignKeys = new ArrayList<>();
        private List<String> uniqueConstraints = new ArrayList<>();
        private List<String> notNullConstraints = new ArrayList<>();
    }
    
    @Data
    public static class ColumnInfo {
        private String columnName;
        private String dataType;
        private boolean nullable;
        private String defaultValue;
    }
    
    @Data
    public static class ForeignKeyInfo {
        private String columnName;
        private String referencedTable;
        private String referencedColumn;
    }
}
