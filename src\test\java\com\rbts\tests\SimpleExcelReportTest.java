package com.rbts.tests;

import com.rbts.reporting.TestCaseResult;
import com.rbts.reporting.TestExecutionReporter;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;

/**
 * Simple test to demonstrate Excel reporting functionality
 * This test creates a clean Excel report with sample test results
 */
@Slf4j
public class SimpleExcelReportTest {
    
    private TestExecutionReporter testReporter;
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up Simple Excel Report Test");
        
        // Create reports directory if it doesn't exist
        File reportsDir = new File("reports");
        if (!reportsDir.exists()) {
            reportsDir.mkdirs();
            log.info("📁 Created reports directory");
        }
        
        // Delete existing report file to start fresh
        File existingReport = new File("reports/Test_Execution_Report.xlsx");
        if (existingReport.exists()) {
            existingReport.delete();
            log.info("🗑️ Deleted existing report file");
        }
        
        // Initialize test reporter
        testReporter = new TestExecutionReporter();
        
        log.info("✅ Simple Excel Report Test setup completed");
        log.info("📊 Report will be generated at: {}", testReporter.getReportFilePath());
    }
    
    /**
     * Test 1: Successful Order Creation
     */
    @Test(description = "Test successful order creation")
    public void testSuccessfulOrderCreation() {
        log.info("🧪 Testing successful order creation");
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName("Order.Order")
                .operation("POST")
                .testCase("Create Order - Valid Customer and Products")
                .expectedResult("Status: 201, Order created successfully")
                .actualResult("Status: 201, Order created with ID: 12345")
                .status("PASS")
                .requestBody("{\"customerId\": 1, \"orderDate\": \"2024-01-01T10:00:00Z\", \"orderItems\": [{\"productId\": 1, \"quantity\": 2, \"unitPrice\": 29.99}]}")
                .responseBody("{\"id\": 12345, \"customerId\": 1, \"orderDate\": \"2024-01-01T10:00:00Z\", \"status\": \"PENDING\", \"total\": 59.98}")
                .statusCode(201)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(result);
        log.info("✅ Order creation test reported to Excel");
    }
    
    /**
     * Test 2: Failed User Creation
     */
    @Test(description = "Test failed user creation")
    public void testFailedUserCreation() {
        log.info("🧪 Testing failed user creation");
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName("Authentication.User")
                .operation("POST")
                .testCase("Create User - Duplicate Email")
                .expectedResult("Status: 409, Email already exists")
                .actualResult("Status: 500, Internal server error")
                .status("FAIL")
                .requestBody("{\"email\": \"<EMAIL>\", \"firstName\": \"John\", \"lastName\": \"Doe\", \"password\": \"SecurePass123!\"}")
                .responseBody("{\"error\": \"Internal server error\", \"message\": \"Database connection failed\"}")
                .statusCode(500)
                .errorMessage("Expected 409 but got 500 - Server error instead of validation error")
                .build();
        
        testReporter.reportTestCaseWithColorCoding(result);
        log.info("❌ User creation failure test reported to Excel");
    }
    
    /**
     * Test 3: Constraint Violation
     */
    @Test(description = "Test constraint violation")
    public void testConstraintViolation() {
        log.info("🧪 Testing constraint violation");
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName("Core.State")
                .operation("POST")
                .testCase("Constraint Violation - Null State Short Name")
                .expectedResult("Status: 400, State short name cannot be null")
                .actualResult("Status: 400, State short name is required")
                .status("PASS")
                .requestBody("{\"stateShortName\": null, \"stateName\": \"California\", \"countryId\": 1}")
                .responseBody("{\"error\": \"Validation failed\", \"message\": \"State short name is required\", \"field\": \"stateShortName\"}")
                .statusCode(400)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(result);
        log.info("🔍 Constraint violation test reported to Excel");
    }
    
    /**
     * Test 4: Successful Product Retrieval
     */
    @Test(description = "Test successful product retrieval")
    public void testSuccessfulProductRetrieval() {
        log.info("🧪 Testing successful product retrieval");
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName("Product.Product")
                .operation("GET")
                .testCase("Get Product by ID - Valid Product")
                .expectedResult("Status: 200, Product details returned")
                .actualResult("Status: 200, Product found with complete details")
                .status("PASS")
                .requestBody("")
                .responseBody("{\"id\": 1, \"name\": \"Wireless Headphones\", \"price\": 99.99, \"categoryId\": 2, \"inStock\": true}")
                .statusCode(200)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(result);
        log.info("📖 Product retrieval test reported to Excel");
    }
    
    /**
     * Test 5: Foreign Key Violation
     */
    @Test(description = "Test foreign key violation")
    public void testForeignKeyViolation() {
        log.info("🧪 Testing foreign key violation");
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName("Authentication.UserProfile")
                .operation("POST")
                .testCase("Constraint Violation - Invalid Country ID")
                .expectedResult("Status: 502, Country service unavailable or country ID not found")
                .actualResult("Status: 502, External service error - Country service timeout")
                .status("PASS")
                .requestBody("{\"userId\": 1, \"countryId\": 999, \"firstName\": \"John\", \"lastName\": \"Doe\"}")
                .responseBody("{\"error\": \"External service error\", \"message\": \"Country service timeout\", \"service\": \"country-service\"}")
                .statusCode(502)
                .build();
        
        testReporter.reportTestCaseWithColorCoding(result);
        log.info("🔗 Foreign key violation test reported to Excel");
    }
    
    /**
     * Test Summary and Report Verification
     */
    @Test(description = "Test summary and report verification", 
          dependsOnMethods = {"testSuccessfulOrderCreation", "testFailedUserCreation", 
                             "testConstraintViolation", "testSuccessfulProductRetrieval", 
                             "testForeignKeyViolation"})
    public void testSummaryAndReportVerification() {
        log.info("📊 SIMPLE EXCEL REPORT TEST SUMMARY");
        log.info("===================================");
        
        int totalTestCases = testReporter.getTotalTestCases();
        int totalDefects = testReporter.getTotalDefects();
        String reportFile = testReporter.getReportFilePath();
        
        log.info("📋 Total Test Cases Reported: {}", totalTestCases);
        log.info("🐛 Total Defects Generated: {}", totalDefects);
        log.info("📁 Excel Report File: {}", reportFile);
        
        // Check if report file exists and get its size
        File reportFileObj = new File(reportFile);
        if (reportFileObj.exists()) {
            log.info("✅ Excel report file created successfully");
            log.info("📊 File size: {} bytes", reportFileObj.length());
            log.info("📅 Last modified: {}", new java.util.Date(reportFileObj.lastModified()));
        } else {
            log.error("❌ Excel report file not found!");
        }
        
        log.info("");
        log.info("🎯 EXCEL REPORT CONTENTS:");
        log.info("1. ✅ Order.Order POST - Order creation (PASS)");
        log.info("2. ❌ Authentication.User POST - User creation (FAIL) - Defect: D_Authentication.User_POST_001");
        log.info("3. ✅ Core.State POST - Null constraint violation (PASS)");
        log.info("4. ✅ Product.Product GET - Product retrieval (PASS)");
        log.info("5. ✅ Authentication.UserProfile POST - Foreign key violation (PASS)");
        
        log.info("");
        log.info("📊 EXCEL REPORT STRUCTURE:");
        log.info("Column A: TestCaseId (TC_TableName_Operation_001)");
        log.info("Column B: TableName (Service.Table)");
        log.info("Column C: TestCase (Description)");
        log.info("Column D: ExpectedResult");
        log.info("Column E: ActualResult");
        log.info("Column F: Status (PASS/FAIL with color coding)");
        log.info("Column G: DefectId (D_TableName_Operation_001 for failures)");
        log.info("Column H: ExecutionTime (Timestamp)");
        log.info("Column I: Operation (POST/GET/PUT/DELETE)");
        log.info("Column J: RequestBody (JSON)");
        log.info("Column K: ResponseBody (JSON)");
        log.info("Column L: StatusCode (HTTP status)");
        
        log.info("");
        log.info("🎨 COLOR CODING:");
        log.info("🟢 Green: PASS status");
        log.info("🔴 Red: FAIL status");
        
        log.info("");
        log.info("📋 HOW TO VIEW THE EXCEL REPORT:");
        log.info("1. Navigate to: {}", reportFile);
        log.info("2. Open with Microsoft Excel or LibreOffice Calc");
        log.info("3. Look for the 'Test_Execution_Report' sheet");
        log.info("4. Review test results with color coding");
        log.info("5. Check defect IDs for failed test cases");
        
        log.info("");
        log.info("🚀 NEXT STEPS FOR YOUR TESTING:");
        log.info("1. Use this TestExecutionReporter in your actual API tests");
        log.info("2. Call testReporter.reportTestCaseWithColorCoding(result) for each test");
        log.info("3. Excel report will be automatically generated with all results");
        log.info("4. Share the Excel report with your team for test result analysis");
        
        log.info("");
        log.info("✅ SIMPLE EXCEL REPORT TEST COMPLETED SUCCESSFULLY!");
        log.info("📊 Excel report with {} test cases and {} defects has been generated!", totalTestCases, totalDefects);
        
        // Verify that we have the expected number of test cases
        if (totalTestCases >= 5) {
            log.info("🎉 SUCCESS: All test cases were successfully reported to Excel!");
        } else {
            log.warn("⚠️ WARNING: Expected 5 test cases but only {} were reported", totalTestCases);
        }
    }
}
