package com.rbts.base.post;


import io.restassured.response.Response;
import lombok.*;
import org.slf4j.Logger;
import com.rbts.utils.ExcelUtils;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

import static io.restassured.RestAssured.given;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class PostWithHeader implements ApiMethod {
    private Logger logger;
    private String filePath;
    private String sheetName;
    private int url;
    private int body;
    @Override
    public Response post(int rowNum) {
        Properties properties = new Properties();
        try {
            FileInputStream fileInputStream = new FileInputStream("config.properties");
            properties.load(fileInputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String baseUrl=properties.getProperty("baseURI_Qa");
        ExcelUtils excelUtils=new ExcelUtils();
        String uri = excelUtils.getCellData(filePath, sheetName, rowNum, url);
        String body1 = excelUtils.getCellData(filePath, sheetName, rowNum, body);
        Response response = given()
                .contentType("application/json")
                .body(body1)
                .when()
                .log().headers()
                .post(baseUrl+uri);
        String responseBody = response.getBody().asString();
        System.out.println("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@"+baseUrl+uri);
        logger.info("Request: {}", response.getHeaders());
        logger.info("ResponseBody: {}", responseBody);
        logger.info("Response: {}", response.getDetailedCookies());
        logger.info("Request: {}", response.getStatusCode());
        logger.info(response.getContentType());
        return response;
    }
}

