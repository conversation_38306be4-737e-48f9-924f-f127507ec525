package com.rbts.tests;

import com.rbts.reporting.ServiceSpecificTestExecutionReporter;
import com.rbts.reporting.TestCaseResult;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;

/**
 * Test class to demonstrate service-specific Excel reporting functionality
 * Creates separate Excel sheets for each service (Order, Authentication, Core, etc.)
 */
@Slf4j
public class ServiceSpecificExcelReportTest {
    
    private ServiceSpecificTestExecutionReporter serviceReporter;
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up Service-Specific Excel Report Test");
        
        // Create reports directory if it doesn't exist
        File reportsDir = new File("reports");
        if (!reportsDir.exists()) {
            reportsDir.mkdirs();
            log.info("📁 Created reports directory");
        }
        
        // Delete existing report file to start fresh
        File existingReport = new File("reports/Service_Specific_Test_Execution_Report.xlsx");
        if (existingReport.exists()) {
            existingReport.delete();
            log.info("🗑️ Deleted existing service-specific report file");
        }
        
        // Initialize service-specific test reporter
        serviceReporter = new ServiceSpecificTestExecutionReporter();
        
        log.info("✅ Service-Specific Excel Report Test setup completed");
        log.info("📊 Report will be generated at: {}", serviceReporter.getReportFilePath());
    }
    
    /**
     * Test Order Service reporting
     */
    @Test(description = "Test Order service reporting to separate Excel sheet")
    public void testOrderServiceReporting() {
        log.info("🧪 Testing Order service reporting");
        
        // Order creation success
        TestCaseResult orderSuccess = TestCaseResult.builder()
                .tableName("Order.Order")
                .operation("POST")
                .testCase("Create Order - Valid Customer and Products")
                .expectedResult("Status: 201, Order created successfully")
                .actualResult("Status: 201, Order created with ID: 12345")
                .status("PASS")
                .requestBody("{\"customerId\": 1, \"orderDate\": \"2024-01-01T10:00:00Z\", \"orderItems\": [{\"productId\": 1, \"quantity\": 2}]}")
                .responseBody("{\"id\": 12345, \"customerId\": 1, \"total\": 59.98}")
                .statusCode(201)
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Order", orderSuccess);
        
        // Order constraint violation
        TestCaseResult orderConstraint = TestCaseResult.builder()
                .tableName("Order.Order")
                .operation("POST")
                .testCase("Constraint Violation - Null Customer ID")
                .expectedResult("Status: 400, Customer ID cannot be null")
                .actualResult("Status: 400, Customer ID is required")
                .status("PASS")
                .requestBody("{\"customerId\": null, \"orderDate\": \"2024-01-01T10:00:00Z\"}")
                .responseBody("{\"error\": \"Validation failed\", \"message\": \"Customer ID is required\"}")
                .statusCode(400)
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Order", orderConstraint);
        
        // Order failure case
        TestCaseResult orderFailure = TestCaseResult.builder()
                .tableName("Order.OrderItem")
                .operation("POST")
                .testCase("Create Order Item - Invalid Product ID")
                .expectedResult("Status: 404, Product not found")
                .actualResult("Status: 500, Internal server error")
                .status("FAIL")
                .requestBody("{\"orderId\": 1, \"productId\": 999, \"quantity\": 2}")
                .responseBody("{\"error\": \"Internal server error\"}")
                .statusCode(500)
                .errorMessage("Expected 404 but got 500")
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Order", orderFailure);
        
        log.info("✅ Order service test cases reported to Order_Test_Results sheet");
    }
    
    /**
     * Test Authentication Service reporting
     */
    @Test(description = "Test Authentication service reporting to separate Excel sheet")
    public void testAuthenticationServiceReporting() {
        log.info("🧪 Testing Authentication service reporting");
        
        // User creation success
        TestCaseResult userSuccess = TestCaseResult.builder()
                .tableName("Authentication.User")
                .operation("POST")
                .testCase("Create User - Valid Email and Password")
                .expectedResult("Status: 201, User created successfully")
                .actualResult("Status: 201, User created with ID: 456")
                .status("PASS")
                .requestBody("{\"email\": \"<EMAIL>\", \"firstName\": \"John\", \"password\": \"SecurePass123!\"}")
                .responseBody("{\"id\": 456, \"email\": \"<EMAIL>\", \"firstName\": \"John\"}")
                .statusCode(201)
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Authentication", userSuccess);
        
        // User unique constraint violation
        TestCaseResult userUnique = TestCaseResult.builder()
                .tableName("Authentication.User")
                .operation("POST")
                .testCase("Constraint Violation - Duplicate Email")
                .expectedResult("Status: 409, Email already exists")
                .actualResult("Status: 409, User <NAME_EMAIL> already exists")
                .status("PASS")
                .requestBody("{\"email\": \"<EMAIL>\", \"firstName\": \"John2\"}")
                .responseBody("{\"error\": \"Duplicate entry\", \"message\": \"User <NAME_EMAIL> already exists\"}")
                .statusCode(409)
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Authentication", userUnique);
        
        // UserProfile foreign key test
        TestCaseResult userProfileFK = TestCaseResult.builder()
                .tableName("Authentication.UserProfile")
                .operation("POST")
                .testCase("Foreign Key Validation - Invalid User ID")
                .expectedResult("Status: 404, User not found")
                .actualResult("Status: 404, User with ID 999 not found")
                .status("PASS")
                .requestBody("{\"userId\": 999, \"firstName\": \"Test\", \"lastName\": \"User\"}")
                .responseBody("{\"error\": \"Foreign key violation\", \"message\": \"User with ID 999 not found\"}")
                .statusCode(404)
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Authentication", userProfileFK);
        
        log.info("✅ Authentication service test cases reported to Authentication_Test_Results sheet");
    }
    
    /**
     * Test Core Service reporting
     */
    @Test(description = "Test Core service reporting to separate Excel sheet")
    public void testCoreServiceReporting() {
        log.info("🧪 Testing Core service reporting");
        
        // State creation success
        TestCaseResult stateSuccess = TestCaseResult.builder()
                .tableName("Core.State")
                .operation("POST")
                .testCase("Create State - Valid Data")
                .expectedResult("Status: 201, State created successfully")
                .actualResult("Status: 201, State created with ID: 789")
                .status("PASS")
                .requestBody("{\"stateShortName\": \"TX\", \"stateName\": \"Texas\", \"countryId\": 1}")
                .responseBody("{\"id\": 789, \"stateShortName\": \"TX\", \"stateName\": \"Texas\"}")
                .statusCode(201)
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Core", stateSuccess);
        
        // State null constraint
        TestCaseResult stateNull = TestCaseResult.builder()
                .tableName("Core.State")
                .operation("POST")
                .testCase("Constraint Violation - Null State Short Name")
                .expectedResult("Status: 400, State short name cannot be null")
                .actualResult("Status: 400, State short name is required")
                .status("PASS")
                .requestBody("{\"stateShortName\": null, \"stateName\": \"California\"}")
                .responseBody("{\"error\": \"Validation failed\", \"message\": \"State short name is required\"}")
                .statusCode(400)
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Core", stateNull);
        
        log.info("✅ Core service test cases reported to Core_Test_Results sheet");
    }
    
    /**
     * Test Product Service reporting
     */
    @Test(description = "Test Product service reporting to separate Excel sheet")
    public void testProductServiceReporting() {
        log.info("🧪 Testing Product service reporting");
        
        // Product GET success
        TestCaseResult productGet = TestCaseResult.builder()
                .tableName("Product.Product")
                .operation("GET")
                .testCase("Get Product by ID - Valid Product")
                .expectedResult("Status: 200, Product details returned")
                .actualResult("Status: 200, Product found with complete details")
                .status("PASS")
                .requestBody("")
                .responseBody("{\"id\": 1, \"name\": \"Wireless Headphones\", \"price\": 99.99}")
                .statusCode(200)
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Product", productGet);
        
        // Product validation error
        TestCaseResult productValidation = TestCaseResult.builder()
                .tableName("Product.Product")
                .operation("POST")
                .testCase("Validation Error - Negative Price")
                .expectedResult("Status: 400, Price must be greater than 0")
                .actualResult("Status: 422, Invalid price value")
                .status("FAIL")
                .requestBody("{\"name\": \"Test Product\", \"price\": -10.50}")
                .responseBody("{\"error\": \"Validation error\", \"message\": \"Invalid price value\"}")
                .statusCode(422)
                .errorMessage("Expected 400 but got 422")
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Product", productValidation);
        
        log.info("✅ Product service test cases reported to Product_Test_Results sheet");
    }
    
    /**
     * Test Contact Service reporting
     */
    @Test(description = "Test Contact service reporting to separate Excel sheet")
    public void testContactServiceReporting() {
        log.info("🧪 Testing Contact service reporting");
        
        // AddressType creation
        TestCaseResult addressTypeSuccess = TestCaseResult.builder()
                .tableName("Contact.AddressType")
                .operation("POST")
                .testCase("Create Address Type - Valid Data")
                .expectedResult("Status: 201, Address type created successfully")
                .actualResult("Status: 201, Address type created with ID: 101")
                .status("PASS")
                .requestBody("{\"typeName\": \"Home Address\", \"description\": \"Primary residence address\"}")
                .responseBody("{\"id\": 101, \"typeName\": \"Home Address\", \"description\": \"Primary residence address\"}")
                .statusCode(201)
                .build();
        
        serviceReporter.reportTestCaseWithColorCodingForService("Contact", addressTypeSuccess);
        
        log.info("✅ Contact service test cases reported to Contact_Test_Results sheet");
    }
    
    /**
     * Service-specific Excel reporting test summary
     */
    @Test(description = "Service-specific Excel reporting test summary", 
          dependsOnMethods = {"testOrderServiceReporting", "testAuthenticationServiceReporting", 
                             "testCoreServiceReporting", "testProductServiceReporting", 
                             "testContactServiceReporting"})
    public void serviceSpecificExcelReportingTestSummary() {
        log.info("📊 SERVICE-SPECIFIC EXCEL REPORTING TEST SUMMARY");
        log.info("=================================================");
        
        int totalTestCases = serviceReporter.getTotalTestCases();
        int totalDefects = serviceReporter.getTotalDefects();
        String reportFile = serviceReporter.getReportFilePath();
        
        log.info("📋 Total Test Cases Reported: {}", totalTestCases);
        log.info("🐛 Total Defects Generated: {}", totalDefects);
        log.info("📁 Excel Report File: {}", reportFile);
        
        // Check if report file exists
        File reportFileObj = new File(reportFile);
        if (reportFileObj.exists()) {
            log.info("✅ Service-specific Excel report file created successfully");
            log.info("📊 File size: {} bytes", reportFileObj.length());
        } else {
            log.error("❌ Service-specific Excel report file not found!");
        }
        
        log.info("");
        log.info("🏢 SERVICES TESTED AND THEIR RESULTS:");
        for (String service : serviceReporter.getTestedServices()) {
            int serviceTestCases = serviceReporter.getTestCasesForService(service);
            int serviceDefects = serviceReporter.getDefectsForService(service);
            log.info("   📋 {}: {} test cases, {} defects", service, serviceTestCases, serviceDefects);
        }
        
        log.info("");
        log.info("📊 EXCEL SHEETS CREATED:");
        log.info("1. 📋 Order_Test_Results - Order service test cases");
        log.info("2. 🔐 Authentication_Test_Results - Authentication service test cases");
        log.info("3. 🌐 Core_Test_Results - Core service test cases");
        log.info("4. 📦 Product_Test_Results - Product service test cases");
        log.info("5. 📞 Contact_Test_Results - Contact service test cases");
        
        log.info("");
        log.info("🎯 SERVICE-SPECIFIC FEATURES DEMONSTRATED:");
        log.info("✅ Separate Excel sheets for each service");
        log.info("✅ Service-specific test case IDs (TC_ServiceName_Table_Operation_001)");
        log.info("✅ Service-specific defect IDs (D_ServiceName_Table_Operation_001)");
        log.info("✅ Independent counters for each service");
        log.info("✅ Automatic sheet creation when new service is tested");
        log.info("✅ Color coding within each service sheet");
        log.info("✅ Complete test data recording per service");
        
        log.info("");
        log.info("📋 EXCEL REPORT STRUCTURE:");
        log.info("File: Service_Specific_Test_Execution_Report.xlsx");
        log.info("├── Order_Test_Results (3 test cases)");
        log.info("├── Authentication_Test_Results (3 test cases)");
        log.info("├── Core_Test_Results (2 test cases)");
        log.info("├── Product_Test_Results (2 test cases)");
        log.info("└── Contact_Test_Results (1 test case)");
        
        log.info("");
        log.info("🚀 BENEFITS OF SERVICE-SPECIFIC REPORTING:");
        log.info("1. 🎯 Clear separation of test results by service");
        log.info("2. 📊 Easy service-specific analysis and metrics");
        log.info("3. 🔍 Independent defect tracking per service");
        log.info("4. 📋 Service team can focus on their own results");
        log.info("5. 🎛️ Scalable - automatically creates sheets for new services");
        log.info("6. 📈 Service-specific pass/fail ratios");
        log.info("7. 🔄 Easy to update existing service sheets");
        
        log.info("");
        log.info("📋 HOW TO VIEW SERVICE-SPECIFIC RESULTS:");
        log.info("1. Open Excel file: {}", reportFile);
        log.info("2. Click on service-specific sheet tabs at the bottom");
        log.info("3. Each sheet contains only that service's test results");
        log.info("4. Review color-coded results (Green=PASS, Red=FAIL)");
        log.info("5. Check service-specific defect IDs for failures");
        
        log.info("");
        log.info("✅ SERVICE-SPECIFIC EXCEL REPORTING TEST COMPLETED SUCCESSFULLY!");
        log.info("📊 {} services tested with {} total test cases and {} defects!", 
                serviceReporter.getTestedServices().size(), totalTestCases, totalDefects);
    }
}
