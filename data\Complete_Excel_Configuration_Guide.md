# Complete Excel Configuration Guide - Where to Add Data Types

## 📊 **EXCEL FILE STRUCTURE WITH DATA TYPES**

### **📁 Main Configuration File: `api_test_configuration.xlsx`**

This Excel file contains **multiple sheets** for different configuration purposes:

---

## 🔧 **SHEET 1: FRAMEWORK_CONFIGURATION**
**Purpose**: Service endpoints and basic configuration
**Data Types**: ❌ NO - This sheet does NOT contain data types

| ServiceName | TableName | POST_Endpoint | PUT_Endpoint | GET_Endpoint | DELETE_Endpoint | getAll_Endpoint |
|-------------|-----------|---------------|--------------|--------------|-----------------|-----------------|
| Order | bundle_products | /api/bundles | /api/bundles/{id} | /api/bundles/{id} | /api/bundles/{id} | /api/bundles |
| Order | Order | /api/orders | /api/orders/{id} | /api/orders/{id} | /api/orders/{id} | /api/orders |
| Authentication | User | /api/users | /api/users/{id} | /api/users/{id} | /api/users/{id} | /api/users |

---

## 📋 **SHEET 2: ORDER_SERVICE_FIELD_DATATYPES** ⭐
**Purpose**: ✅ **THIS IS WHERE YOU ADD DATA TYPES FOR ORDER SERVICE**
**Data Types**: ✅ YES - This sheet contains ALL data type definitions

| TableName | FieldName | DataType | MaxLength | IsRequired | IsUnique | DefaultValue | IsPrimaryKey | IsForeignKey | ReferencedTable |
|-----------|-----------|----------|-----------|------------|----------|--------------|--------------|--------------|-----------------|
| bundle_products | bundle_id | BIGINT | 19 | FALSE | TRUE | AUTO_INCREMENT | TRUE | FALSE | |
| bundle_products | bundle_name | VARCHAR | 100 | TRUE | TRUE | | FALSE | FALSE | |
| bundle_products | bundle_description | TEXT | 65535 | FALSE | FALSE | | FALSE | FALSE | |
| bundle_products | discount_percentage | DECIMAL | 5,2 | FALSE | FALSE | 0.00 | FALSE | FALSE | |
| bundle_products | is_active | BOOLEAN | 1 | FALSE | FALSE | TRUE | FALSE | FALSE | |
| bundle_products | created_at | TIMESTAMP | 19 | FALSE | FALSE | CURRENT_TIMESTAMP | FALSE | FALSE | |
| bundle_products | created_by | VARCHAR | 50 | FALSE | FALSE | | FALSE | FALSE | |
| Order | order_id | BIGINT | 19 | FALSE | TRUE | AUTO_INCREMENT | TRUE | FALSE | |
| Order | customer_id | BIGINT | 19 | TRUE | FALSE | | FALSE | TRUE | Customer |
| Order | order_date | TIMESTAMP | 19 | TRUE | FALSE | | FALSE | FALSE | |
| Order | order_status | VARCHAR | 20 | TRUE | FALSE | PENDING | FALSE | FALSE | |
| Order | total_amount | DECIMAL | 10,2 | FALSE | FALSE | 0.00 | FALSE | FALSE | |
| Order | created_at | TIMESTAMP | 19 | FALSE | FALSE | CURRENT_TIMESTAMP | FALSE | FALSE | |
| Order | last_modified_at | TIMESTAMP | 19 | FALSE | FALSE | | FALSE | FALSE | |

---

## 📋 **SHEET 3: AUTHENTICATION_SERVICE_FIELD_DATATYPES** ⭐
**Purpose**: ✅ **THIS IS WHERE YOU ADD DATA TYPES FOR AUTHENTICATION SERVICE**
**Data Types**: ✅ YES - This sheet contains ALL data type definitions

| TableName | FieldName | DataType | MaxLength | IsRequired | IsUnique | DefaultValue | IsPrimaryKey | IsForeignKey | ReferencedTable |
|-----------|-----------|----------|-----------|------------|----------|--------------|--------------|--------------|-----------------|
| User | user_id | BIGINT | 19 | FALSE | TRUE | AUTO_INCREMENT | TRUE | FALSE | |
| User | username | VARCHAR | 50 | TRUE | TRUE | | FALSE | FALSE | |
| User | email_address | VARCHAR | 100 | TRUE | TRUE | | FALSE | FALSE | |
| User | password_hash | VARCHAR | 255 | TRUE | FALSE | | FALSE | FALSE | |
| User | first_name | VARCHAR | 50 | FALSE | FALSE | | FALSE | FALSE | |
| User | last_name | VARCHAR | 50 | FALSE | FALSE | | FALSE | FALSE | |
| User | phone_number | VARCHAR | 20 | FALSE | FALSE | | FALSE | FALSE | |
| User | is_active | BOOLEAN | 1 | FALSE | FALSE | TRUE | FALSE | FALSE | |
| User | created_at | TIMESTAMP | 19 | FALSE | FALSE | CURRENT_TIMESTAMP | FALSE | FALSE | |

---

## 📋 **SHEET 4: CORE_SERVICE_FIELD_DATATYPES** ⭐
**Purpose**: ✅ **THIS IS WHERE YOU ADD DATA TYPES FOR CORE SERVICE**
**Data Types**: ✅ YES - This sheet contains ALL data type definitions

| TableName | FieldName | DataType | MaxLength | IsRequired | IsUnique | DefaultValue | IsPrimaryKey | IsForeignKey | ReferencedTable |
|-----------|-----------|----------|-----------|------------|----------|--------------|--------------|--------------|-----------------|
| State | state_id | BIGINT | 19 | FALSE | TRUE | AUTO_INCREMENT | TRUE | FALSE | |
| State | state_short_name | VARCHAR | 5 | TRUE | TRUE | | FALSE | FALSE | |
| State | state_name | VARCHAR | 100 | TRUE | FALSE | | FALSE | FALSE | |
| State | country_id | BIGINT | 19 | TRUE | FALSE | | FALSE | TRUE | Country |
| State | is_active | BOOLEAN | 1 | FALSE | FALSE | TRUE | FALSE | FALSE | |

---

## 📋 **SHEET 5: DATA_TYPE_VALIDATION_RULES**
**Purpose**: Global validation rules for each data type
**Data Types**: ❌ NO - This sheet defines validation rules, not field data types

| DataType | ValidationRule | MinValue | MaxValue | Pattern | SampleValue |
|----------|----------------|----------|----------|---------|-------------|
| VARCHAR | LENGTH_CHECK | 1 | MAX_LENGTH | | Test String |
| TEXT | LENGTH_CHECK | 0 | 65535 | | Lorem ipsum... |
| BIGINT | RANGE_CHECK | -9223372036854775808 | 9223372036854775807 | | 1234567890 |
| DECIMAL | PRECISION_CHECK | MIN_DECIMAL | MAX_DECIMAL | ^\d+\.\d{1,2}$ | 123.45 |
| BOOLEAN | VALUE_CHECK | FALSE | TRUE | ^(true\|false)$ | true |
| TIMESTAMP | FORMAT_CHECK | | | yyyy-MM-dd HH:mm:ss | 2024-01-01 10:00:00 |
| EMAIL | PATTERN_CHECK | | | ^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$ | <EMAIL> |

---

## 📋 **SHEET 6: ORDER_SERVICE_CONSTRAINTS**
**Purpose**: Constraint testing configuration
**Data Types**: ❌ NO - This sheet uses data types but doesn't define them

| TableName | FieldName | ConstraintType | ExpectedStatusCode | ExpectedMessage | TestValue |
|-----------|-----------|----------------|-------------------|-----------------|-----------|
| bundle_products | bundle_name | NULL_CONSTRAINT | 400 | Bundle name is required | null |
| bundle_products | bundle_name | UNIQUE_CONSTRAINT | 409 | Bundle name already exists | DUPLICATE_NAME |
| bundle_products | bundle_name | LENGTH_CONSTRAINT | 400 | Bundle name too long | STRING_101_CHARS |

---

## 🎯 **ANSWER: WHERE TO ADD DATA TYPES**

### **✅ Data types are added in these specific sheets:**

1. **ORDER_SERVICE_FIELD_DATATYPES** - For Order service tables (bundle_products, Order, etc.)
2. **AUTHENTICATION_SERVICE_FIELD_DATATYPES** - For Authentication service tables (User, UserProfile, etc.)
3. **CORE_SERVICE_FIELD_DATATYPES** - For Core service tables (State, Country, etc.)
4. **{YOUR_SERVICE}_FIELD_DATATYPES** - For any other service you add

### **❌ Data types are NOT added in these sheets:**
- FRAMEWORK_CONFIGURATION (only endpoints)
- DATA_TYPE_VALIDATION_RULES (only validation rules)
- {SERVICE}_CONSTRAINTS (only constraint tests)
- API_Tests (only test data)

---

## 📝 **HOW TO ADD DATA TYPES FOR YOUR BUNDLE_PRODUCTS TABLE**

### **Step 1: Open Excel File**
- File: `api_test_configuration.xlsx`
- Sheet: `ORDER_SERVICE_FIELD_DATATYPES`

### **Step 2: Add Your Table Fields**
For each field in your bundle_products table, add a row with:

| TableName | FieldName | DataType | MaxLength | IsRequired | IsUnique | DefaultValue |
|-----------|-----------|----------|-----------|------------|----------|--------------|
| bundle_products | bundle_id | BIGINT | 19 | FALSE | TRUE | AUTO_INCREMENT |
| bundle_products | bundle_name | VARCHAR | 100 | TRUE | TRUE | |
| bundle_products | bundle_description | TEXT | 65535 | FALSE | FALSE | |
| bundle_products | discount_percentage | DECIMAL | 5,2 | FALSE | FALSE | 0.00 |
| bundle_products | is_active | BOOLEAN | 1 | FALSE | FALSE | TRUE |

### **Step 3: Column Definitions**
- **TableName**: Your database table name
- **FieldName**: Actual database column name
- **DataType**: SQL data type (VARCHAR, BIGINT, DECIMAL, BOOLEAN, TIMESTAMP, TEXT)
- **MaxLength**: For VARCHAR(100) use "100", for DECIMAL(5,2) use "5,2"
- **IsRequired**: TRUE if NOT NULL constraint, FALSE if nullable
- **IsUnique**: TRUE if UNIQUE constraint, FALSE if not unique
- **DefaultValue**: Default value, AUTO_INCREMENT, CURRENT_TIMESTAMP, or empty

---

## 🔧 **SUPPORTED DATA TYPES TO USE**

### **✅ String Types:**
- **VARCHAR** - Variable length strings (specify max length)
- **TEXT** - Large text fields (use 65535 for max length)
- **EMAIL** - Email validation (use VARCHAR with EMAIL pattern)
- **PHONE** - Phone validation (use VARCHAR with PHONE pattern)

### **✅ Numeric Types:**
- **BIGINT** - 64-bit integers (use 19 for max length)
- **INT** - 32-bit integers (use 10 for max length)
- **DECIMAL** - Decimal numbers (use "precision,scale" like "10,2")

### **✅ Other Types:**
- **BOOLEAN** - True/false values (use 1 for max length)
- **TIMESTAMP** - Date and time (use 19 for max length)
- **DATE** - Date only (use 10 for max length)

---

## 🎯 **EXAMPLE: Complete bundle_products Configuration**

In the **ORDER_SERVICE_FIELD_DATATYPES** sheet, add these rows:

```
TableName: bundle_products, FieldName: bundle_id, DataType: BIGINT, MaxLength: 19, IsRequired: FALSE, IsUnique: TRUE, DefaultValue: AUTO_INCREMENT
TableName: bundle_products, FieldName: bundle_name, DataType: VARCHAR, MaxLength: 100, IsRequired: TRUE, IsUnique: TRUE, DefaultValue: 
TableName: bundle_products, FieldName: bundle_description, DataType: TEXT, MaxLength: 65535, IsRequired: FALSE, IsUnique: FALSE, DefaultValue: 
TableName: bundle_products, FieldName: discount_percentage, DataType: DECIMAL, MaxLength: 5,2, IsRequired: FALSE, IsUnique: FALSE, DefaultValue: 0.00
TableName: bundle_products, FieldName: is_active, DataType: BOOLEAN, MaxLength: 1, IsRequired: FALSE, IsUnique: FALSE, DefaultValue: TRUE
TableName: bundle_products, FieldName: created_at, DataType: TIMESTAMP, MaxLength: 19, IsRequired: FALSE, IsUnique: FALSE, DefaultValue: CURRENT_TIMESTAMP
```

**This configuration will generate correct payloads with actual database field names and proper data types!** 🎯📊✅
