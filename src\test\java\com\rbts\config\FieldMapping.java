package com.rbts.config;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * Field mapping configuration for database to API field name mapping
 * Handles the mapping between database field names and API request/response field names
 */
@Data
@NoArgsConstructor
public class FieldMapping {
    
    /**
     * Database field name (e.g., "user_id", "first_name", "email_address")
     */
    private String databaseField;
    
    /**
     * API request field name (e.g., "userId", "firstName", "email")
     */
    private String apiRequestField;
    
    /**
     * API response field name (e.g., "id", "firstName", "emailAddress")
     */
    private String apiResponseField;
    
    /**
     * Field type for validation (e.g., "PRIMARY_KEY", "STRING", "EMAIL", "TIMESTAMP", "FOREIGN_KEY", "DECIMAL")
     */
    private String fieldType;

    /**
     * Description of the field
     */
    private String description;

    /**
     * Operations where this field is present in request (e.g., "PUT,PATCH" or "NONE" for audit fields)
     */
    private String requestOperations;

    /**
     * Operations where this field is present in response (e.g., "POST,PUT,PATCH,GET,GETALL")
     */
    private String responseOperations;

    /**
     * Service name if this is a foreign key from the same service (for full payload)
     */
    private String fkSameService;

    /**
     * Service name if this is a foreign key from a different service (for ID only)
     */
    private String fkDifferentService;

    /**
     * Constructor for basic field mapping (backward compatibility)
     */
    public FieldMapping(String databaseField, String apiRequestField, String apiResponseField, String fieldType, String description) {
        this.databaseField = databaseField;
        this.apiRequestField = apiRequestField;
        this.apiResponseField = apiResponseField;
        this.fieldType = fieldType;
        this.description = description;
        this.requestOperations = "ALL"; // Default: present in all request operations
        this.responseOperations = "ALL"; // Default: present in all response operations
        this.fkSameService = "";
        this.fkDifferentService = "";
    }

    /**
     * Constructor for advanced field mapping with operation-specific presence
     */
    public FieldMapping(String databaseField, String apiRequestField, String apiResponseField,
                       String fieldType, String description, String requestOperations, String responseOperations) {
        this.databaseField = databaseField;
        this.apiRequestField = apiRequestField;
        this.apiResponseField = apiResponseField;
        this.fieldType = fieldType;
        this.description = description;
        this.requestOperations = requestOperations != null ? requestOperations : "ALL";
        this.responseOperations = responseOperations != null ? responseOperations : "ALL";
        this.fkSameService = "";
        this.fkDifferentService = "";
    }

    /**
     * Constructor for complete field mapping with foreign key service information
     */
    public FieldMapping(String databaseField, String apiRequestField, String apiResponseField,
                       String fieldType, String description, String requestOperations, String responseOperations,
                       String fkSameService, String fkDifferentService) {
        this.databaseField = databaseField;
        this.apiRequestField = apiRequestField;
        this.apiResponseField = apiResponseField;
        this.fieldType = fieldType;
        this.description = description;
        this.requestOperations = requestOperations != null ? requestOperations : "ALL";
        this.responseOperations = responseOperations != null ? responseOperations : "ALL";
        this.fkSameService = fkSameService != null ? fkSameService : "";
        this.fkDifferentService = fkDifferentService != null ? fkDifferentService : "";
    }

    /**
     * Check if this field is a primary key
     */
    public boolean isPrimaryKey() {
        return "PRIMARY_KEY".equalsIgnoreCase(fieldType);
    }
    
    /**
     * Check if this field is a foreign key
     */
    public boolean isForeignKey() {
        return "FOREIGN_KEY".equalsIgnoreCase(fieldType);
    }
    
    /**
     * Check if this field is an email field
     */
    public boolean isEmailField() {
        return "EMAIL".equalsIgnoreCase(fieldType);
    }
    
    /**
     * Check if this field is a timestamp field
     */
    public boolean isTimestampField() {
        return "TIMESTAMP".equalsIgnoreCase(fieldType);
    }
    
    /**
     * Check if this field is a decimal/numeric field
     */
    public boolean isDecimalField() {
        return "DECIMAL".equalsIgnoreCase(fieldType) || "NUMERIC".equalsIgnoreCase(fieldType);
    }
    
    /**
     * Get the appropriate field name for API requests
     */
    public String getRequestFieldName() {
        return apiRequestField != null && !apiRequestField.trim().isEmpty() ? apiRequestField : databaseField;
    }
    
    /**
     * Get the appropriate field name for API responses
     */
    public String getResponseFieldName() {
        return apiResponseField != null && !apiResponseField.trim().isEmpty() ? apiResponseField : databaseField;
    }
    
    /**
     * Get the database field name
     */
    public String getDatabaseFieldName() {
        return databaseField;
    }

    /**
     * Check if this field is present in request for a specific operation
     */
    public boolean isPresentInRequest(String operation) {
        if (requestOperations == null || requestOperations.trim().isEmpty()) {
            return false;
        }

        String ops = requestOperations.toUpperCase();
        if ("ALL".equals(ops)) {
            return true;
        }
        if ("NONE".equals(ops)) {
            return false;
        }

        String[] operations = ops.split(",");
        for (String op : operations) {
            if (operation.toUpperCase().equals(op.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if this field is present in response for a specific operation
     */
    public boolean isPresentInResponse(String operation) {
        if (responseOperations == null || responseOperations.trim().isEmpty()) {
            return false;
        }

        String ops = responseOperations.toUpperCase();
        if ("ALL".equals(ops)) {
            return true;
        }
        if ("NONE".equals(ops)) {
            return false;
        }

        String[] operations = ops.split(",");
        for (String op : operations) {
            if (operation.toUpperCase().equals(op.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if this field is an audit field (created/modified fields)
     */
    public boolean isAuditField() {
        if (databaseField == null) return false;
        String field = databaseField.toLowerCase();
        return field.contains("created") || field.contains("modified") ||
               field.contains("updated") || field.contains("audit");
    }

    /**
     * Check if this field is a creation audit field
     */
    public boolean isCreationAuditField() {
        if (databaseField == null) return false;
        String field = databaseField.toLowerCase();
        return field.contains("created");
    }

    /**
     * Check if this field is a modification audit field
     */
    public boolean isModificationAuditField() {
        if (databaseField == null) return false;
        String field = databaseField.toLowerCase();
        return field.contains("modified") || field.contains("updated");
    }

    /**
     * Check if this field is a foreign key from the same service
     */
    public boolean isForeignKeyFromSameService() {
        return fkSameService != null && !fkSameService.trim().isEmpty();
    }

    /**
     * Check if this field is a foreign key from a different service
     */
    public boolean isForeignKeyFromDifferentService() {
        return fkDifferentService != null && !fkDifferentService.trim().isEmpty();
    }

    /**
     * Get the service name for same service foreign key
     */
    public String getSameServiceName() {
        return fkSameService != null ? fkSameService.trim() : "";
    }

    /**
     * Get the service name for different service foreign key
     */
    public String getDifferentServiceName() {
        return fkDifferentService != null ? fkDifferentService.trim() : "";
    }

    /**
     * Get foreign key payload type (FULL for same service, ID_ONLY for different service)
     */
    public String getForeignKeyPayloadType() {
        if (isForeignKeyFromSameService()) {
            return "FULL_PAYLOAD";
        } else if (isForeignKeyFromDifferentService()) {
            return "ID_ONLY";
        }
        return "NOT_FK";
    }

    /**
     * Get the referenced service name for foreign key
     */
    public String getReferencedServiceName() {
        if (isForeignKeyFromSameService()) {
            return getSameServiceName();
        } else if (isForeignKeyFromDifferentService()) {
            return getDifferentServiceName();
        }
        return "";
    }

    @Override
    public String toString() {
        return String.format("FieldMapping{db='%s', request='%s', response='%s', type='%s', reqOps='%s', respOps='%s', fkSame='%s', fkDiff='%s'}",
                databaseField, apiRequestField, apiResponseField, fieldType, requestOperations, responseOperations, fkSameService, fkDifferentService);
    }
}
