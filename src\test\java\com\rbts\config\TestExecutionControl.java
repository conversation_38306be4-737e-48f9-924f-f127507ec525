package com.rbts.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * Configuration class for controlling test execution
 * Allows selective testing of services, tables, and operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestExecutionControl {
    
    private String serviceName;
    private String tableName;
    private boolean enableService;
    private boolean enableTable;
    private List<String> operationsToTest;
    private String testPriority;
    private List<String> testTypes;
    private int executionOrder;
    private String comments;
    
    /**
     * Check if service is enabled for testing
     */
    public boolean isServiceEnabled() {
        return enableService;
    }
    
    /**
     * Check if table is enabled for testing
     */
    public boolean isTableEnabled() {
        return enableTable && enableService; // Table can only be enabled if service is enabled
    }
    
    /**
     * Check if specific operation should be tested
     */
    public boolean shouldTestOperation(String operation) {
        if (!isTableEnabled()) {
            return false;
        }
        
        if (operationsToTest == null || operationsToTest.isEmpty()) {
            return false;
        }
        
        return operationsToTest.contains(operation.toLowerCase());
    }
    
    /**
     * Check if specific test type should be executed
     */
    public boolean shouldExecuteTestType(String testType) {
        if (!isTableEnabled()) {
            return false;
        }
        
        if (testTypes == null || testTypes.isEmpty()) {
            return true; // If no specific test types configured, run all
        }
        
        return testTypes.contains(testType.toLowerCase());
    }
    
    /**
     * Get priority level as integer (high=1, medium=2, low=3)
     */
    public int getPriorityLevel() {
        if (testPriority == null) {
            return 2; // Default to medium priority
        }
        
        switch (testPriority.toLowerCase()) {
            case "high":
                return 1;
            case "medium":
                return 2;
            case "low":
                return 3;
            default:
                return 2;
        }
    }
    
    /**
     * Check if this control has higher priority than another
     */
    public boolean hasHigherPriorityThan(TestExecutionControl other) {
        return this.getPriorityLevel() < other.getPriorityLevel();
    }
    
    /**
     * Parse operations from comma-separated string
     */
    public static List<String> parseOperations(String operationsStr) {
        if (operationsStr == null || operationsStr.trim().isEmpty()) {
            return Arrays.asList();
        }
        
        return Arrays.asList(operationsStr.toLowerCase().split(","));
    }
    
    /**
     * Parse test types from comma-separated string
     */
    public static List<String> parseTestTypes(String testTypesStr) {
        if (testTypesStr == null || testTypesStr.trim().isEmpty()) {
            return Arrays.asList("normal"); // Default to normal tests
        }
        
        return Arrays.asList(testTypesStr.toLowerCase().split(","));
    }
    
    /**
     * Create TestExecutionControl from Excel row data
     */
    public static TestExecutionControl fromExcelRow(String serviceName, String tableName, 
                                                   String enableService, String enableTable,
                                                   String operationsToTest, String testPriority,
                                                   String testTypes, String executionOrder,
                                                   String comments) {
        return TestExecutionControl.builder()
                .serviceName(serviceName != null ? serviceName.trim() : "")
                .tableName(tableName != null ? tableName.trim() : "")
                .enableService(parseBoolean(enableService))
                .enableTable(parseBoolean(enableTable))
                .operationsToTest(parseOperations(operationsToTest))
                .testPriority(testPriority != null ? testPriority.trim() : "medium")
                .testTypes(parseTestTypes(testTypes))
                .executionOrder(parseInteger(executionOrder))
                .comments(comments != null ? comments.trim() : "")
                .build();
    }
    
    /**
     * Parse boolean from string (true/false, yes/no, 1/0)
     */
    private static boolean parseBoolean(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = value.trim().toLowerCase();
        return "true".equals(trimmed) || "yes".equals(trimmed) || "1".equals(trimmed);
    }
    
    /**
     * Parse integer from string with default value
     */
    private static int parseInteger(String value) {
        if (value == null || value.trim().isEmpty()) {
            return 0;
        }
        
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * Get unique key for this control (service + table)
     */
    public String getKey() {
        return serviceName + "." + tableName;
    }
    
    /**
     * Check if this control matches a service and table
     */
    public boolean matches(String service, String table) {
        return serviceName.equals(service) && tableName.equals(table);
    }
    
    @Override
    public String toString() {
        return String.format("TestExecutionControl{service='%s', table='%s', serviceEnabled=%s, tableEnabled=%s, operations=%s, priority='%s', order=%d}",
                serviceName, tableName, enableService, enableTable, operationsToTest, testPriority, executionOrder);
    }
}
