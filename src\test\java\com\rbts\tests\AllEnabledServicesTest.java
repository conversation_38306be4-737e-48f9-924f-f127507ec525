package com.rbts.tests;

import com.rbts.config.ServiceSpecificConfigManager;
import com.rbts.config.ServiceSpecificConfigTemplateGenerator;
import com.rbts.config.ServiceTableConfig;
import com.rbts.reporting.TestCaseResult;
import com.rbts.reporting.TestExecutionReporter;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * TestNG test class for testing ALL enabled services
 * Automatically discovers and tests all services that are enabled in configuration
 */
@Slf4j
public class AllEnabledServicesTest {
    
    private ServiceSpecificConfigManager configManager;
    private TestExecutionReporter testReporter;
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up All Enabled Services Test");
        
        // Generate service-specific configuration template
        ServiceSpecificConfigTemplateGenerator templateGenerator = new ServiceSpecificConfigTemplateGenerator();
        templateGenerator.generateServiceSpecificConfigurationTemplate();
        
        // Initialize managers
        configManager = ServiceSpecificConfigManager.getInstance();
        configManager.loadConfigurations();
        testReporter = new TestExecutionReporter();
        
        // Display enabled services summary
        displayEnabledServicesSummary();
        
        log.info("✅ All Enabled Services Test setup completed");
    }
    
    /**
     * Display summary of enabled services and tables
     */
    private void displayEnabledServicesSummary() {
        log.info("📊 ENABLED SERVICES SUMMARY");
        log.info("===========================");
        
        Set<String> allServices = configManager.getConfiguredServices();
        log.info("🏢 Total Configured Services: {}", allServices.size());
        
        int totalEnabledServices = 0;
        int totalEnabledTables = 0;
        
        for (String service : allServices) {
            Set<String> serviceTables = configManager.getTablesForService(service);
            int enabledTablesInService = 0;
            
            for (String table : serviceTables) {
                if (configManager.isTableEnabledForTesting(service, table)) {
                    enabledTablesInService++;
                    totalEnabledTables++;
                }
            }
            
            if (enabledTablesInService > 0) {
                totalEnabledServices++;
                log.info("✅ Service '{}': {} enabled tables out of {}", 
                        service, enabledTablesInService, serviceTables.size());
                
                // Show enabled tables
                for (String table : serviceTables) {
                    if (configManager.isTableEnabledForTesting(service, table)) {
                        List<String> operations = configManager.getOperationsToTest(service, table);
                        log.info("   📋 {}: operations={}", table, operations);
                    }
                }
            } else {
                log.info("❌ Service '{}': No enabled tables", service);
            }
        }
        
        log.info("");
        log.info("🎯 TESTING SCOPE:");
        log.info("   Enabled Services: {} out of {}", totalEnabledServices, allServices.size());
        log.info("   Enabled Tables: {}", totalEnabledTables);
        log.info("");
    }
    
    /**
     * Data provider for all enabled service tables
     */
    @DataProvider(name = "enabledServiceTables")
    public Object[][] getEnabledServiceTableData() {
        List<Object[]> tableData = new ArrayList<>();
        
        Set<String> allServices = configManager.getConfiguredServices();
        
        for (String service : allServices) {
            Set<String> serviceTables = configManager.getTablesForService(service);
            
            for (String table : serviceTables) {
                // Check if table is enabled for testing
                if (configManager.isTableEnabledForTesting(service, table)) {
                    // Check if any operations are configured for testing
                    List<String> operationsToTest = configManager.getOperationsToTest(service, table);
                    
                    if (!operationsToTest.isEmpty()) {
                        tableData.add(new Object[]{service, table});
                        log.info("📋 Added for testing: {}.{} - operations: {}", 
                                service, table, operationsToTest);
                    } else {
                        log.info("⏭️ Skipping {}.{} - no operations configured", service, table);
                    }
                } else {
                    log.info("⏭️ Skipping {}.{} - testing disabled", service, table);
                }
            }
        }
        
        if (tableData.isEmpty()) {
            log.warn("⚠️ No enabled service tables found for testing");
            // Add dummy entry to prevent DataProvider failure
            tableData.add(new Object[]{"NoServices", "NoTables"});
        }
        
        log.info("🎯 Total enabled service tables for testing: {}", tableData.size());
        return tableData.toArray(new Object[0][]);
    }
    
    /**
     * Test POST operations for all enabled service tables
     */
    @Test(dataProvider = "enabledServiceTables", description = "Test POST operations for all enabled services")
    public void testEnabledServicesPostOperations(String serviceName, String tableName) {
        if ("NoServices".equals(serviceName)) {
            log.warn("⚠️ No enabled services found - skipping test");
            return;
        }
        
        log.info("🧪 Testing POST operation for {}.{}", serviceName, tableName);
        
        try {
            // Check if POST operation is enabled for this table
            List<String> operationsToTest = configManager.getOperationsToTest(serviceName, tableName);
            if (!operationsToTest.contains("post")) {
                log.info("⏭️ POST operation not enabled for {}.{}", serviceName, tableName);
                return;
            }
            
            // Generate request body
            JSONObject requestBody = generateServiceRequestBody(serviceName, tableName);
            log.info("📝 Generated request body for {}.{}: {}", serviceName, tableName, requestBody.toString());
            
            // Get API endpoint
            String endpoint = configManager.getApiEndpoint(serviceName, tableName, "POST");
            if (endpoint == null || endpoint.trim().isEmpty()) {
                log.warn("⚠️ No POST endpoint configured for {}.{}", serviceName, tableName);
                return;
            }
            
            // Execute API call
            Response apiResponse = executeServiceApiCall(serviceName, "POST", endpoint, requestBody.toString());
            log.info("📡 API Response for {}.{} - Status: {}, Body: {}", 
                    serviceName, tableName, apiResponse.getStatusCode(), apiResponse.getBody().asString());
            
            // Validate response
            validateServicePostResponse(serviceName, tableName, apiResponse, requestBody);
            
            // Report test result
            reportServiceTestResult(serviceName, tableName, "POST", requestBody, apiResponse);
            
        } catch (Exception e) {
            log.error("❌ Error testing {}.{} POST operation: {}", serviceName, tableName, e.getMessage());
            
            TestCaseResult failResult = TestCaseResult.fail(serviceName + "." + tableName, "POST", 
                    "Service POST operation", "Successful API call", "Exception: " + e.getMessage(), e.getMessage());
            testReporter.reportTestCaseWithColorCoding(failResult);
        }
    }
    
    /**
     * Test constraint validations for all enabled service tables
     */
    @Test(dataProvider = "enabledServiceTables", description = "Test constraint validations for all enabled services")
    public void testEnabledServicesConstraints(String serviceName, String tableName) {
        if ("NoServices".equals(serviceName)) {
            return;
        }
        
        log.info("🔍 Testing constraints for {}.{}", serviceName, tableName);
        
        try {
            // Get all constraint configurations for this table
            List<ServiceTableConfig> tableConfigs = configManager.getTableConfigurations(serviceName, tableName);
            
            for (ServiceTableConfig config : tableConfigs) {
                if (config.hasConstraintValidation()) {
                    testServiceConstraint(serviceName, tableName, config);
                }
            }
            
        } catch (Exception e) {
            log.error("❌ Error testing {}.{} constraints: {}", serviceName, tableName, e.getMessage());
        }
    }
    
    /**
     * Test GET operations for all enabled service tables
     */
    @Test(dataProvider = "enabledServiceTables", description = "Test GET operations for all enabled services")
    public void testEnabledServicesGetOperations(String serviceName, String tableName) {
        if ("NoServices".equals(serviceName)) {
            return;
        }
        
        log.info("📖 Testing GET operation for {}.{}", serviceName, tableName);
        
        try {
            // Check if GET operation is enabled for this table
            List<String> operationsToTest = configManager.getOperationsToTest(serviceName, tableName);
            if (!operationsToTest.contains("get")) {
                log.info("⏭️ GET operation not enabled for {}.{}", serviceName, tableName);
                return;
            }
            
            // Get API endpoint
            String endpoint = configManager.getApiEndpoint(serviceName, tableName, "GET");
            if (endpoint == null || endpoint.trim().isEmpty()) {
                log.warn("⚠️ No GET endpoint configured for {}.{}", serviceName, tableName);
                return;
            }
            
            // Replace {id} placeholder with test ID
            String getEndpoint = endpoint.replace("{id}", "1");
            
            // Execute API call
            Response apiResponse = executeServiceApiCall(serviceName, "GET", getEndpoint, null);
            log.info("📡 GET Response for {}.{} - Status: {}, Body: {}", 
                    serviceName, tableName, apiResponse.getStatusCode(), apiResponse.getBody().asString());
            
            // Validate GET response
            validateServiceGetResponse(serviceName, tableName, apiResponse);
            
            // Report test result
            reportServiceTestResult(serviceName, tableName, "GET", null, apiResponse);
            
        } catch (Exception e) {
            log.error("❌ Error testing {}.{} GET operation: {}", serviceName, tableName, e.getMessage());
        }
    }
    
    /**
     * Generate request body for any service table
     */
    private JSONObject generateServiceRequestBody(String serviceName, String tableName) throws JSONException {
        JSONObject requestBody = new JSONObject();
        
        try {
            List<ServiceTableConfig> tableConfigs = configManager.getTableConfigurations(serviceName, tableName);
            
            for (ServiceTableConfig config : tableConfigs) {
                // Only include fields that should be in POST requests
                if (config.isPresentInRequest("POST") && !config.isPrimaryKey()) {
                    try {
                        String apiFieldName = config.getApiRequestFieldName();
                        Object fieldValue = generateValidFieldValue(config);
                        requestBody.put(apiFieldName, fieldValue);

                        log.debug("Added field to {}.{} request: {} = {}", serviceName, tableName, apiFieldName, fieldValue);
                    } catch (Exception e) {
                        log.error("Error adding field to request body: {}", e.getMessage());
                    }
                }
            }
            
            // Add service-specific default fields if needed
            addServiceSpecificDefaults(serviceName, tableName, requestBody);
            
        } catch (Exception e) {
            log.error("Error generating request body for {}.{}: {}", serviceName, tableName, e.getMessage());
            // Fallback to minimal request
            requestBody.put("name", "Test " + tableName);
            requestBody.put("description", "Test data for " + serviceName + "." + tableName);
        }
        
        return requestBody;
    }
    
    /**
     * Add service-specific default fields
     */
    private void addServiceSpecificDefaults(String serviceName, String tableName, JSONObject requestBody) {
        try {
            switch (serviceName.toLowerCase()) {
                case "order":
                    if (!requestBody.has("customerId")) {
                        requestBody.put("customerId", 1);
                    }
                    if (!requestBody.has("orderDate")) {
                        requestBody.put("orderDate", "2024-01-01T10:00:00Z");
                    }
                    if ("Order".equals(tableName) && !requestBody.has("orderItems")) {
                        requestBody.put("orderItems", generateOrderItems());
                    }
                    break;

                case "authentication":
                    if ("User".equals(tableName)) {
                        if (!requestBody.has("email")) {
                            requestBody.put("email", "test" + System.currentTimeMillis() + "@example.com");
                        }
                        if (!requestBody.has("password")) {
                            requestBody.put("password", "SecurePassword123!");
                        }
                    }
                    break;

                case "contact":
                    if ("AddressType".equals(tableName) && !requestBody.has("typeName")) {
                        requestBody.put("typeName", "Test Address Type " + System.currentTimeMillis());
                    }
                    break;

                case "product":
                    if ("Product".equals(tableName)) {
                        if (!requestBody.has("price")) {
                            requestBody.put("price", 29.99);
                        }
                        if (!requestBody.has("categoryId")) {
                            requestBody.put("categoryId", 1);
                        }
                    }
                    break;

                case "core":
                    if ("State".equals(tableName)) {
                        if (!requestBody.has("stateShortName")) {
                            requestBody.put("stateShortName", "TS" + System.currentTimeMillis() % 100);
                        }
                        if (!requestBody.has("countryId")) {
                            requestBody.put("countryId", 1);
                        }
                    }
                    break;
            }
        } catch (Exception e) {
            log.error("Error adding service-specific defaults for {}.{}: {}", serviceName, tableName, e.getMessage());
        }
    }
    
    /**
     * Generate order items for Order requests
     */
    private List<Object> generateOrderItems() {
        List<Object> orderItems = new ArrayList<>();

        try {
            JSONObject item1 = new JSONObject();
            item1.put("productId", 1);
            item1.put("quantity", 2);
            item1.put("unitPrice", 29.99);

            orderItems.add(item1);
        } catch (Exception e) {
            log.error("Error generating order items: {}", e.getMessage());
        }

        return orderItems;
    }
    
    /**
     * Generate valid field value based on configuration
     */
    private Object generateValidFieldValue(ServiceTableConfig config) {
        String fieldType = config.getFieldType();
        String fieldName = config.getDatabaseFieldName();
        
        if (fieldType == null) {
            return "Valid Value";
        }
        
        switch (fieldType.toUpperCase()) {
            case "STRING":
                return "Valid " + fieldName + " " + System.currentTimeMillis() % 1000;
            case "EMAIL":
                return "test" + System.currentTimeMillis() % 1000 + "@example.com";
            case "INTEGER":
            case "FOREIGN_KEY":
                return 1;
            case "DECIMAL":
                return 29.99;
            case "BOOLEAN":
                return true;
            case "TIMESTAMP":
                return "2024-01-01T10:00:00Z";
            default:
                return "Valid Value";
        }
    }
    
    /**
     * Test specific service constraint
     */
    private void testServiceConstraint(String serviceName, String tableName, ServiceTableConfig config) {
        log.info("🔍 Testing {} constraint for {}.{}.{}", 
                config.getConstraintType(), serviceName, tableName, config.getDatabaseFieldName());
        
        try {
            // Generate constraint violation request
            JSONObject requestBody = generateConstraintViolationRequest(serviceName, tableName, config);
            
            // Get API endpoint
            String endpoint = configManager.getApiEndpoint(serviceName, tableName, "POST");
            if (endpoint == null) {
                log.warn("No POST endpoint for constraint testing: {}.{}", serviceName, tableName);
                return;
            }
            
            // Execute API call
            Response apiResponse = executeServiceApiCall(serviceName, "POST", endpoint, requestBody.toString());
            
            // Validate constraint violation response
            validateConstraintViolationResponse(serviceName, config, apiResponse);
            
            // Report constraint test result
            reportConstraintTestResult(serviceName, tableName, config, requestBody, apiResponse);
            
        } catch (Exception e) {
            log.error("❌ Error testing constraint {} for {}.{}: {}", 
                    config.getConstraintType(), serviceName, tableName, e.getMessage());
        }
    }
    
    /**
     * Generate constraint violation request
     */
    private JSONObject generateConstraintViolationRequest(String serviceName, String tableName, ServiceTableConfig config) throws JSONException {
        JSONObject requestBody = generateServiceRequestBody(serviceName, tableName);
        
        // Override the specific field with constraint violation value
        String fieldName = config.getApiRequestFieldName();
        Object testValue = config.getTestValueAsObject();

        try {
            if (fieldName != null && !fieldName.trim().isEmpty()) {
                requestBody.put(fieldName, testValue);
                log.info("Set constraint violation for {}.{}: {} = {}", serviceName, tableName, fieldName, testValue);
            }
        } catch (Exception e) {
            log.error("Error setting constraint violation value: {}", e.getMessage());
        }
        
        return requestBody;
    }
    
    /**
     * Execute service API call
     */
    private Response executeServiceApiCall(String serviceName, String method, String endpoint, String requestBody) {
        log.info("📡 Executing {} call to {}: {}", method, serviceName, endpoint);
        
        String baseUrl = configManager.getGeneralConfig("base.url.direct");
        String fullUrl = baseUrl + endpoint;
        
        String authToken = getServiceAuthToken(serviceName);
        
        if ("GET".equalsIgnoreCase(method)) {
            return RestAssured.given()
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + authToken)
                    .when()
                    .get(fullUrl)
                    .then()
                    .extract()
                    .response();
        } else {
            return RestAssured.given()
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + authToken)
                    .body(requestBody != null ? requestBody : "{}")
                    .when()
                    .post(fullUrl)
                    .then()
                    .extract()
                    .response();
        }
    }
    
    /**
     * Get authentication token for service
     */
    private String getServiceAuthToken(String serviceName) {
        // Try service-specific token first
        String serviceToken = configManager.getGeneralConfig(serviceName + ".service.token");
        if (serviceToken != null) {
            return serviceToken;
        }
        
        // Fallback to default token
        return "default-service-token";
    }
    
    /**
     * Validate service POST response
     */
    private void validateServicePostResponse(String serviceName, String tableName, Response response, JSONObject requestBody) {
        int statusCode = response.getStatusCode();
        String responseBody = response.getBody().asString();
        
        // Check for success status codes
        int expectedSuccessCode = configManager.getStatusCode("POST", "success");
        
        if (statusCode == expectedSuccessCode || statusCode == 201 || statusCode == 200) {
            log.info("✅ {}.{} POST successful - Status: {}", serviceName, tableName, statusCode);
            
            // Validate response contains ID (for successful creation)
            try {
                JSONObject responseJson = new JSONObject(responseBody);
                if (responseJson.has("id")) {
                    int generatedId = responseJson.getInt("id");
                    log.info("✅ {}.{} created with ID: {}", serviceName, tableName, generatedId);
                }
            } catch (Exception e) {
                log.warn("Could not parse response JSON for {}.{}: {}", serviceName, tableName, e.getMessage());
            }
        } else {
            log.warn("⚠️ {}.{} POST returned status: {} - {}", serviceName, tableName, statusCode, responseBody);
        }
    }
    
    /**
     * Validate service GET response
     */
    private void validateServiceGetResponse(String serviceName, String tableName, Response response) {
        int statusCode = response.getStatusCode();
        String responseBody = response.getBody().asString();
        
        if (statusCode == 200) {
            log.info("✅ {}.{} GET successful - Status: {}", serviceName, tableName, statusCode);
        } else if (statusCode == 404) {
            log.info("ℹ️ {}.{} GET returned 404 - Record not found (expected for test ID)", serviceName, tableName);
        } else {
            log.warn("⚠️ {}.{} GET returned status: {} - {}", serviceName, tableName, statusCode, responseBody);
        }
    }
    
    /**
     * Validate constraint violation response
     */
    private void validateConstraintViolationResponse(String serviceName, ServiceTableConfig config, Response response) {
        int actualStatusCode = response.getStatusCode();
        String actualMessage = response.getBody().asString();
        
        // Get expected status code for this constraint type
        int expectedStatusCode = configManager.getStatusCode("POST", config.getConstraintType());
        
        boolean statusCodeValid = (actualStatusCode == expectedStatusCode);
        boolean messageValid = config.validateErrorMessage(actualMessage);
        
        if (statusCodeValid && messageValid) {
            log.info("✅ Constraint validation passed for {}: Status {}, Message validated", 
                    config.getConstraintType(), actualStatusCode);
        } else {
            log.warn("⚠️ Constraint validation failed for {}: Expected status {}, got {}. Message valid: {}", 
                    config.getConstraintType(), expectedStatusCode, actualStatusCode, messageValid);
        }
    }
    
    /**
     * Report service test result
     */
    private void reportServiceTestResult(String serviceName, String tableName, String operation, 
                                       JSONObject requestBody, Response response) {
        String testCase = String.format("%s.%s %s Operation", serviceName, tableName, operation);
        String expectedResult = "Successful API call with appropriate response";
        String actualResult = String.format("Status: %d, Response: %s", 
                response.getStatusCode(), response.getBody().asString());
        
        boolean isSuccess = (response.getStatusCode() >= 200 && response.getStatusCode() < 300) ||
                           (response.getStatusCode() == 404 && "GET".equals(operation)); // 404 is OK for GET with test ID
        String status = isSuccess ? "PASS" : "FAIL";
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName(serviceName + "." + tableName)
                .operation(operation)
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody != null ? requestBody.toString() : "")
                .responseBody(response.getBody().asString())
                .statusCode(response.getStatusCode())
                .build();
        
        testReporter.reportTestCaseWithColorCoding(result);
    }
    
    /**
     * Report constraint test result
     */
    private void reportConstraintTestResult(String serviceName, String tableName, ServiceTableConfig config, 
                                          JSONObject requestBody, Response response) {
        String testCase = String.format("%s.%s %s Constraint", serviceName, tableName, config.getConstraintType());
        String expectedResult = String.format("Status: %d, Message: %s", 
                configManager.getStatusCode("POST", config.getConstraintType()), 
                config.getExpectedErrorMessage());
        String actualResult = String.format("Status: %d, Message: %s", 
                response.getStatusCode(), response.getBody().asString());
        
        boolean statusValid = (response.getStatusCode() == configManager.getStatusCode("POST", config.getConstraintType()));
        boolean messageValid = config.validateErrorMessage(response.getBody().asString());
        boolean isSuccess = statusValid && messageValid;
        
        String status = isSuccess ? "PASS" : "FAIL";
        
        TestCaseResult result = TestCaseResult.builder()
                .tableName(serviceName + "." + tableName)
                .operation("POST")
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody.toString())
                .responseBody(response.getBody().asString())
                .statusCode(response.getStatusCode())
                .build();
        
        testReporter.reportTestCaseWithColorCoding(result);
    }
    
    /**
     * All enabled services test summary
     */
    @Test(description = "All enabled services test summary", 
          dependsOnMethods = {"testEnabledServicesPostOperations", "testEnabledServicesConstraints", "testEnabledServicesGetOperations"})
    public void allEnabledServicesTestSummary() {
        log.info("📊 ALL ENABLED SERVICES TEST SUMMARY");
        log.info("====================================");
        
        Set<String> allServices = configManager.getConfiguredServices();
        int totalTestCases = testReporter.getTotalTestCases();
        int totalDefects = testReporter.getTotalDefects();
        String reportFile = testReporter.getReportFilePath();
        
        // Count enabled services and tables
        int enabledServices = 0;
        int enabledTables = 0;
        
        for (String service : allServices) {
            Set<String> serviceTables = configManager.getTablesForService(service);
            int enabledTablesInService = 0;
            
            for (String table : serviceTables) {
                if (configManager.isTableEnabledForTesting(service, table)) {
                    enabledTablesInService++;
                    enabledTables++;
                }
            }
            
            if (enabledTablesInService > 0) {
                enabledServices++;
            }
        }
        
        log.info("🏢 Total Configured Services: {}", allServices.size());
        log.info("✅ Enabled Services Tested: {}", enabledServices);
        log.info("📋 Enabled Tables Tested: {}", enabledTables);
        log.info("📊 Total Test Cases Executed: {}", totalTestCases);
        log.info("🐛 Total Defects Found: {}", totalDefects);
        log.info("📁 Test Report File: {}", reportFile);
        
        log.info("");
        log.info("🎯 ALL ENABLED SERVICES TESTING FEATURES DEMONSTRATED:");
        log.info("✅ Automatic service discovery from configuration");
        log.info("✅ Dynamic testing of all enabled services");
        log.info("✅ Service-specific request body generation");
        log.info("✅ Multi-service constraint validation");
        log.info("✅ Service-specific API endpoint handling");
        log.info("✅ Service-specific authentication tokens");
        log.info("✅ Comprehensive Excel reporting for all services");
        log.info("✅ POST, GET, and constraint testing for all enabled tables");
        
        log.info("");
        log.info("📋 SERVICES TESTED:");
        for (String service : allServices) {
            Set<String> serviceTables = configManager.getTablesForService(service);
            int enabledTablesInService = 0;
            
            for (String table : serviceTables) {
                if (configManager.isTableEnabledForTesting(service, table)) {
                    enabledTablesInService++;
                }
            }
            
            if (enabledTablesInService > 0) {
                log.info("  ✅ {}: {} enabled tables", service, enabledTablesInService);
            } else {
                log.info("  ❌ {}: No enabled tables", service);
            }
        }
        
        log.info("");
        log.info("🚀 BENEFITS OF TESTING ALL ENABLED SERVICES:");
        log.info("1. 🎯 Comprehensive coverage of all enabled functionality");
        log.info("2. 🔄 Automatic discovery - no manual service selection needed");
        log.info("3. 📊 Single test execution for entire enabled system");
        log.info("4. 🎛️ Easy enable/disable control via Excel configuration");
        log.info("5. 📋 Unified reporting across all services");
        log.info("6. 🚀 Scalable - automatically includes new enabled services");
        
        log.info("");
        log.info("✅ ALL ENABLED SERVICES TESTING COMPLETED SUCCESSFULLY!");
        
        Assert.assertTrue(enabledServices > 0, "Should have at least one enabled service");
        Assert.assertTrue(totalTestCases > 0, "Should have executed test cases");
    }
}
