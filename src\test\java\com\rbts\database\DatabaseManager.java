package com.rbts.database;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.rbts.config.ConfigManager;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;

/**
 * Database Manager for CRUD Testing Framework
 * Handles database connections, queries, and JSON conversion with foreign key resolution
 */
@Slf4j
public class DatabaseManager {
    
    private final ConfigManager configManager;
    private final ObjectMapper objectMapper;
    
    public DatabaseManager() {
        this.configManager = ConfigManager.getInstance();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Get database connection
     */
    private Connection getConnection() throws SQLException {
        String url = configManager.getJdbcUrl();
        String user = configManager.getJdbcUser();
        String password = configManager.getJdbcPassword();
        
        log.debug("Connecting to database: {}", url);
        return DriverManager.getConnection(url, user, password);
    }
    
    /**
     * Query database by ID and convert result to JSON with foreign key resolution
     */
    public String queryByIdAndConvertToJson(String entity, Object id) {
        String tableName = configManager.getTableName(entity);
        String primaryKey = configManager.getPrimaryKey(entity);
        
        if (tableName == null || primaryKey == null) {
            throw new IllegalArgumentException("Entity " + entity + " not configured properly");
        }
        
        String query = String.format("SELECT * FROM %s WHERE %s = ?", tableName, primaryKey);
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            
            statement.setObject(1, id);
            log.debug("Executing query: {} with ID: {}", query, id);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    ObjectNode jsonNode = convertResultSetToJson(resultSet, entity);
                    return objectMapper.writeValueAsString(jsonNode);
                } else {
                    log.warn("No record found for entity {} with ID {}", entity, id);
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("Error querying database for entity {} with ID {}: {}", entity, id, e.getMessage());
            throw new RuntimeException("Database query failed", e);
        }
    }
    
    /**
     * Convert ResultSet to JSON with foreign key resolution
     */
    private ObjectNode convertResultSetToJson(ResultSet resultSet, String entity) throws Exception {
        ObjectNode jsonNode = objectMapper.createObjectNode();
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        // Get foreign key mappings for this entity
        Map<String, String> foreignKeys = configManager.getForeignKeys(entity);
        
        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            Object value = resultSet.getObject(i);
            
            if (value == null) {
                jsonNode.putNull(columnName);
            } else if (foreignKeys.containsKey(columnName)) {
                // Handle foreign key - resolve to full object
                String referencedTable = foreignKeys.get(columnName);
                String[] parts = referencedTable.split("\\.");
                if (parts.length == 2) {
                    String refTableName = parts[0];
                    String refEntity = getEntityFromTableName(refTableName);
                    
                    if (refEntity != null) {
                        // Get the referenced object
                        String referencedJson = queryByIdAndConvertToJson(refEntity, value);
                        if (referencedJson != null) {
                            JsonNode referencedNode = objectMapper.readTree(referencedJson);
                            jsonNode.set(columnName.replace("_id", ""), referencedNode);
                        } else {
                            jsonNode.put(columnName, value.toString());
                        }
                    } else {
                        jsonNode.put(columnName, value.toString());
                    }
                } else {
                    jsonNode.put(columnName, value.toString());
                }
            } else {
                // Handle regular fields based on type
                if (value instanceof String) {
                    jsonNode.put(columnName, (String) value);
                } else if (value instanceof Integer) {
                    jsonNode.put(columnName, (Integer) value);
                } else if (value instanceof Long) {
                    jsonNode.put(columnName, (Long) value);
                } else if (value instanceof Double) {
                    jsonNode.put(columnName, (Double) value);
                } else if (value instanceof Boolean) {
                    jsonNode.put(columnName, (Boolean) value);
                } else if (value instanceof Timestamp) {
                    jsonNode.put(columnName, value.toString());
                } else if (value instanceof Date) {
                    jsonNode.put(columnName, value.toString());
                } else {
                    jsonNode.put(columnName, value.toString());
                }
            }
        }
        
        return jsonNode;
    }
    
    /**
     * Get entity name from table name
     */
    private String getEntityFromTableName(String tableName) {
        for (String entity : configManager.getConfiguredEntities()) {
            if (tableName.equals(configManager.getTableName(entity))) {
                return entity;
            }
        }
        return null;
    }
    
    /**
     * Test database connection
     */
    public boolean testConnection() {
        try (Connection connection = getConnection()) {
            return connection.isValid(configManager.getJdbcConnectionTimeout());
        } catch (SQLException e) {
            log.error("Database connection test failed: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Execute custom query and return JSON result
     */
    public String executeQueryAndConvertToJson(String query, Object... parameters) {
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            
            // Set parameters
            for (int i = 0; i < parameters.length; i++) {
                statement.setObject(i + 1, parameters[i]);
            }
            
            log.debug("Executing custom query: {} with parameters: {}", query, parameters);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    ObjectNode jsonNode = objectMapper.createObjectNode();
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();
                    
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = resultSet.getObject(i);
                        
                        if (value == null) {
                            jsonNode.putNull(columnName);
                        } else {
                            jsonNode.put(columnName, value.toString());
                        }
                    }
                    
                    return objectMapper.writeValueAsString(jsonNode);
                }
            }
        } catch (Exception e) {
            log.error("Error executing custom query: {}", e.getMessage());
            throw new RuntimeException("Custom query execution failed", e);
        }
        
        return null;
    }
    
    /**
     * Check if record exists by ID
     */
    public boolean recordExists(String entity, Object id) {
        String tableName = configManager.getTableName(entity);
        String primaryKey = configManager.getPrimaryKey(entity);
        
        String query = String.format("SELECT 1 FROM %s WHERE %s = ?", tableName, primaryKey);
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            
            statement.setObject(1, id);
            try (ResultSet resultSet = statement.executeQuery()) {
                return resultSet.next();
            }
        } catch (SQLException e) {
            log.error("Error checking record existence: {}", e.getMessage());
            return false;
        }
    }
}
