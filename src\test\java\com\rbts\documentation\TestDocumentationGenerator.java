package com.rbts.documentation;

import com.rbts.config.ConfigManager;
import com.rbts.generator.AutoRequestBodyGenerator;
import com.rbts.generator.AutoUrlGenerator;
import com.rbts.schema.DatabaseSchemaDetector;
import com.rbts.schema.DatabaseSchemaDetector.TableSchema;
import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Test Documentation Generator
 * Automatically generates Excel documentation of all tests being executed
 */
@Slf4j
public class TestDocumentationGenerator {
    
    private final ConfigManager configManager;
    private final DatabaseSchemaDetector schemaDetector;
    private final AutoRequestBodyGenerator requestBodyGenerator;
    private final AutoUrlGenerator urlGenerator;
    private final ExcelUtils excelUtils;
    
    public TestDocumentationGenerator() {
        this.configManager = ConfigManager.getInstance();
        this.schemaDetector = new DatabaseSchemaDetector();
        this.requestBodyGenerator = new AutoRequestBodyGenerator();
        this.urlGenerator = new AutoUrlGenerator();
        this.excelUtils = new ExcelUtils();
    }
    
    /**
     * Generate comprehensive test documentation Excel file
     */
    public void generateTestDocumentation(String filePath) {
        log.info("Generating comprehensive test documentation Excel file: {}", filePath);
        
        // Create multiple sheets for different views
        generateTestOverviewSheet(filePath, "Test_Overview");
        generateDetailedTestSheet(filePath, "Detailed_Tests");
        generateTableSchemaSheet(filePath, "Table_Schemas");
        generateTestConfigurationSheet(filePath, "Test_Configuration");
        generateTestSummarySheet(filePath, "Test_Summary");
        
        log.info("Test documentation generated successfully: {}", filePath);
    }
    
    /**
     * Generate test overview sheet
     */
    private void generateTestOverviewSheet(String filePath, String sheetName) {
        log.info("Generating test overview sheet: {}", sheetName);
        
        // Create headers
        excelUtils.setCellData(filePath, sheetName, 1, 1, "Service");
        excelUtils.setCellData(filePath, sheetName, 1, 2, "Table");
        excelUtils.setCellData(filePath, sheetName, 1, 3, "Pattern");
        excelUtils.setCellData(filePath, sheetName, 1, 4, "Operations Tested");
        excelUtils.setCellData(filePath, sheetName, 1, 5, "Test Types");
        excelUtils.setCellData(filePath, sheetName, 1, 6, "Total Tests");
        excelUtils.setCellData(filePath, sheetName, 1, 7, "Primary Keys");
        excelUtils.setCellData(filePath, sheetName, 1, 8, "Foreign Keys");
        excelUtils.setCellData(filePath, sheetName, 1, 9, "Unique Constraints");
        excelUtils.setCellData(filePath, sheetName, 1, 10, "Not Null Constraints");
        
        int rowNum = 2;
        Set<String> services = urlGenerator.getConfiguredServices();
        
        for (String service : services) {
            List<String> tables = urlGenerator.getTablesForService(service);
            String pattern = urlGenerator.getServicePattern(service);
            
            for (String table : tables) {
                if (!schemaDetector.tableExists(table)) {
                    continue;
                }
                
                TableSchema schema = schemaDetector.getTableSchema(table);
                String[] operations = getConfiguredOperations();
                String[] testTypes = getConfiguredTestTypes();
                
                int totalTests = calculateTotalTests(operations, testTypes);
                
                excelUtils.setCellData(filePath, sheetName, rowNum, 1, service);
                excelUtils.setCellData(filePath, sheetName, rowNum, 2, table);
                excelUtils.setCellData(filePath, sheetName, rowNum, 3, pattern);
                excelUtils.setCellData(filePath, sheetName, rowNum, 4, String.join(", ", operations));
                excelUtils.setCellData(filePath, sheetName, rowNum, 5, String.join(", ", testTypes));
                excelUtils.setCellData(filePath, sheetName, rowNum, 6, String.valueOf(totalTests));
                excelUtils.setCellData(filePath, sheetName, rowNum, 7, String.join(", ", schema.getPrimaryKeys()));
                excelUtils.setCellData(filePath, sheetName, rowNum, 8, 
                    schema.getForeignKeys().stream()
                        .map(fk -> fk.getColumnName() + "->" + fk.getReferencedTable())
                        .reduce((a, b) -> a + ", " + b).orElse(""));
                excelUtils.setCellData(filePath, sheetName, rowNum, 9, String.join(", ", schema.getUniqueConstraints()));
                excelUtils.setCellData(filePath, sheetName, rowNum, 10, String.join(", ", schema.getNotNullConstraints()));
                
                rowNum++;
            }
        }
    }
    
    /**
     * Generate detailed test sheet with all individual tests
     */
    private void generateDetailedTestSheet(String filePath, String sheetName) {
        log.info("Generating detailed test sheet: {}", sheetName);
        
        // Create headers
        excelUtils.setCellData(filePath, sheetName, 1, 1, "Test ID");
        excelUtils.setCellData(filePath, sheetName, 1, 2, "Service");
        excelUtils.setCellData(filePath, sheetName, 1, 3, "Table");
        excelUtils.setCellData(filePath, sheetName, 1, 4, "Operation");
        excelUtils.setCellData(filePath, sheetName, 1, 5, "Test Type");
        excelUtils.setCellData(filePath, sheetName, 1, 6, "Pattern");
        excelUtils.setCellData(filePath, sheetName, 1, 7, "URL");
        excelUtils.setCellData(filePath, sheetName, 1, 8, "Sample Request Body");
        excelUtils.setCellData(filePath, sheetName, 1, 9, "Expected Status Code");
        excelUtils.setCellData(filePath, sheetName, 1, 10, "Test Description");
        excelUtils.setCellData(filePath, sheetName, 1, 11, "Validation Points");
        
        int rowNum = 2;
        int testId = 1;
        Set<String> services = urlGenerator.getConfiguredServices();
        
        for (String service : services) {
            List<String> tables = urlGenerator.getTablesForService(service);
            String pattern = urlGenerator.getServicePattern(service);
            
            for (String table : tables) {
                if (!schemaDetector.tableExists(table)) {
                    continue;
                }
                
                String[] operations = getConfiguredOperations();
                String[] testTypes = getConfiguredTestTypes();
                
                for (String operation : operations) {
                    for (String testType : testTypes) {
                        if (shouldSkipTest(testType, operation)) {
                            continue;
                        }
                        
                        // Generate test details
                        String url = generateUrlForDocumentation(service, table, operation, pattern);
                        String requestBody = generateRequestBodyForDocumentation(table, operation, testType);
                        String expectedStatusCode = getExpectedStatusCode(operation, testType);
                        String testDescription = generateTestDescription(table, operation, testType);
                        String validationPoints = generateValidationPoints(operation, testType);
                        
                        excelUtils.setCellData(filePath, sheetName, rowNum, 1, "TEST_" + String.format("%03d", testId));
                        excelUtils.setCellData(filePath, sheetName, rowNum, 2, service);
                        excelUtils.setCellData(filePath, sheetName, rowNum, 3, table);
                        excelUtils.setCellData(filePath, sheetName, rowNum, 4, operation.toUpperCase());
                        excelUtils.setCellData(filePath, sheetName, rowNum, 5, testType);
                        excelUtils.setCellData(filePath, sheetName, rowNum, 6, pattern);
                        excelUtils.setCellData(filePath, sheetName, rowNum, 7, url);
                        excelUtils.setCellData(filePath, sheetName, rowNum, 8, requestBody);
                        excelUtils.setCellData(filePath, sheetName, rowNum, 9, expectedStatusCode);
                        excelUtils.setCellData(filePath, sheetName, rowNum, 10, testDescription);
                        excelUtils.setCellData(filePath, sheetName, rowNum, 11, validationPoints);
                        
                        rowNum++;
                        testId++;
                    }
                }
            }
        }
    }
    
    /**
     * Generate table schema documentation sheet
     */
    private void generateTableSchemaSheet(String filePath, String sheetName) {
        log.info("Generating table schema sheet: {}", sheetName);
        
        // Create headers
        excelUtils.setCellData(filePath, sheetName, 1, 1, "Service");
        excelUtils.setCellData(filePath, sheetName, 1, 2, "Table");
        excelUtils.setCellData(filePath, sheetName, 1, 3, "Column Name");
        excelUtils.setCellData(filePath, sheetName, 1, 4, "Data Type");
        excelUtils.setCellData(filePath, sheetName, 1, 5, "Nullable");
        excelUtils.setCellData(filePath, sheetName, 1, 6, "Primary Key");
        excelUtils.setCellData(filePath, sheetName, 1, 7, "Foreign Key");
        excelUtils.setCellData(filePath, sheetName, 1, 8, "Unique Constraint");
        excelUtils.setCellData(filePath, sheetName, 1, 9, "Default Value");
        
        int rowNum = 2;
        Set<String> services = urlGenerator.getConfiguredServices();
        
        for (String service : services) {
            List<String> tables = urlGenerator.getTablesForService(service);
            
            for (String table : tables) {
                if (!schemaDetector.tableExists(table)) {
                    continue;
                }
                
                TableSchema schema = schemaDetector.getTableSchema(table);
                
                for (var column : schema.getColumns()) {
                    boolean isPrimaryKey = schema.getPrimaryKeys().contains(column.getColumnName());
                    boolean isUniqueConstraint = schema.getUniqueConstraints().contains(column.getColumnName());
                    
                    String foreignKeyInfo = schema.getForeignKeys().stream()
                        .filter(fk -> fk.getColumnName().equals(column.getColumnName()))
                        .map(fk -> fk.getReferencedTable() + "." + fk.getReferencedColumn())
                        .findFirst().orElse("");
                    
                    excelUtils.setCellData(filePath, sheetName, rowNum, 1, service);
                    excelUtils.setCellData(filePath, sheetName, rowNum, 2, table);
                    excelUtils.setCellData(filePath, sheetName, rowNum, 3, column.getColumnName());
                    excelUtils.setCellData(filePath, sheetName, rowNum, 4, column.getDataType());
                    excelUtils.setCellData(filePath, sheetName, rowNum, 5, column.isNullable() ? "YES" : "NO");
                    excelUtils.setCellData(filePath, sheetName, rowNum, 6, isPrimaryKey ? "YES" : "NO");
                    excelUtils.setCellData(filePath, sheetName, rowNum, 7, foreignKeyInfo);
                    excelUtils.setCellData(filePath, sheetName, rowNum, 8, isUniqueConstraint ? "YES" : "NO");
                    excelUtils.setCellData(filePath, sheetName, rowNum, 9, column.getDefaultValue() != null ? column.getDefaultValue() : "");
                    
                    rowNum++;
                }
            }
        }
    }
    
    /**
     * Generate test configuration sheet
     */
    private void generateTestConfigurationSheet(String filePath, String sheetName) {
        log.info("Generating test configuration sheet: {}", sheetName);
        
        // Create headers
        excelUtils.setCellData(filePath, sheetName, 1, 1, "Configuration Category");
        excelUtils.setCellData(filePath, sheetName, 1, 2, "Configuration Key");
        excelUtils.setCellData(filePath, sheetName, 1, 3, "Configuration Value");
        excelUtils.setCellData(filePath, sheetName, 1, 4, "Description");
        
        int rowNum = 2;
        
        // Add framework configuration
        rowNum = addConfigSection(filePath, sheetName, rowNum, "Framework Settings", Map.of(
            "auto.test.types", configManager.getProperty("auto.test.types", "normal,null_constraint,unique_constraint,foreign_key_invalid"),
            "auto.test.operations", configManager.getProperty("auto.test.operations", "post,put,patch,get,delete"),
            "auto.schema.detection", configManager.getProperty("auto.schema.detection", "true")
        ));
        
        // Add URL configuration
        rowNum = addConfigSection(filePath, sheetName, rowNum, "URL Configuration", Map.of(
            "direct.pattern.base_url", configManager.getProperty("direct.pattern.base_url"),
            "proxy.pattern.base_url", configManager.getProperty("proxy.pattern.base_url"),
            "proxy.pattern.endpoint", configManager.getProperty("proxy.pattern.endpoint")
        ));
        
        // Add validation configuration
        rowNum = addConfigSection(filePath, sheetName, rowNum, "Validation Rules", Map.of(
            "validation.status_code.post", configManager.getProperty("validation.status_code.post", "201,200"),
            "validation.status_code.put", configManager.getProperty("validation.status_code.put", "200"),
            "validation.constraint_violation.expected_status", configManager.getProperty("validation.constraint_violation.expected_status", "400,422")
        ));
        
        // Add service configuration
        Set<String> services = urlGenerator.getConfiguredServices();
        Map<String, String> serviceConfig = new HashMap<>();
        for (String service : services) {
            serviceConfig.put("service.tables." + service, configManager.getProperty("service.tables." + service));
            serviceConfig.put("service.pattern." + service, configManager.getProperty("service.pattern." + service));
        }
        addConfigSection(filePath, sheetName, rowNum, "Service Configuration", serviceConfig);
    }
    
    /**
     * Generate test summary sheet
     */
    private void generateTestSummarySheet(String filePath, String sheetName) {
        log.info("Generating test summary sheet: {}", sheetName);
        
        // Create summary information
        excelUtils.setCellData(filePath, sheetName, 1, 1, "Test Documentation Summary");
        excelUtils.setCellData(filePath, sheetName, 2, 1, "Generated On:");
        excelUtils.setCellData(filePath, sheetName, 2, 2, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        excelUtils.setCellData(filePath, sheetName, 4, 1, "Framework Information");
        excelUtils.setCellData(filePath, sheetName, 5, 1, "Framework Name:");
        excelUtils.setCellData(filePath, sheetName, 5, 2, "Automated CRUD Testing Framework");
        excelUtils.setCellData(filePath, sheetName, 6, 1, "Version:");
        excelUtils.setCellData(filePath, sheetName, 6, 2, "1.0.0");
        
        // Count totals
        Set<String> services = urlGenerator.getConfiguredServices();
        int totalServices = services.size();
        int totalTables = 0;
        int totalTests = 0;
        
        for (String service : services) {
            List<String> tables = urlGenerator.getTablesForService(service);
            totalTables += tables.size();
            
            for (String table : tables) {
                if (schemaDetector.tableExists(table)) {
                    String[] operations = getConfiguredOperations();
                    String[] testTypes = getConfiguredTestTypes();
                    totalTests += calculateTotalTests(operations, testTypes);
                }
            }
        }
        
        excelUtils.setCellData(filePath, sheetName, 8, 1, "Test Statistics");
        excelUtils.setCellData(filePath, sheetName, 9, 1, "Total Services:");
        excelUtils.setCellData(filePath, sheetName, 9, 2, String.valueOf(totalServices));
        excelUtils.setCellData(filePath, sheetName, 10, 1, "Total Tables:");
        excelUtils.setCellData(filePath, sheetName, 10, 2, String.valueOf(totalTables));
        excelUtils.setCellData(filePath, sheetName, 11, 1, "Total Tests:");
        excelUtils.setCellData(filePath, sheetName, 11, 2, String.valueOf(totalTests));
        
        // Add sheet descriptions
        excelUtils.setCellData(filePath, sheetName, 13, 1, "Sheet Descriptions");
        excelUtils.setCellData(filePath, sheetName, 14, 1, "Test_Overview:");
        excelUtils.setCellData(filePath, sheetName, 14, 2, "High-level overview of all tables and their test coverage");
        excelUtils.setCellData(filePath, sheetName, 15, 1, "Detailed_Tests:");
        excelUtils.setCellData(filePath, sheetName, 15, 2, "Complete list of all individual tests being executed");
        excelUtils.setCellData(filePath, sheetName, 16, 1, "Table_Schemas:");
        excelUtils.setCellData(filePath, sheetName, 16, 2, "Database schema information for all tables");
        excelUtils.setCellData(filePath, sheetName, 17, 1, "Test_Configuration:");
        excelUtils.setCellData(filePath, sheetName, 17, 2, "Framework configuration settings and parameters");
    }
    
    // Helper methods
    
    private String[] getConfiguredOperations() {
        return configManager.getProperty("auto.test.operations", "post,put,patch,get,delete").split(",");
    }
    
    private String[] getConfiguredTestTypes() {
        return configManager.getProperty("auto.test.types", "normal,null_constraint,unique_constraint,foreign_key_invalid").split(",");
    }
    
    private int calculateTotalTests(String[] operations, String[] testTypes) {
        int total = 0;
        for (String operation : operations) {
            for (String testType : testTypes) {
                if (!shouldSkipTest(testType.trim(), operation.trim())) {
                    total++;
                }
            }
        }
        return total;
    }
    
    private boolean shouldSkipTest(String testType, String operation) {
        // Skip constraint tests for GET operations
        return (("null_constraint".equals(testType) || "unique_constraint".equals(testType) || "foreign_key_invalid".equals(testType)) &&
                ("get".equalsIgnoreCase(operation) || "getall".equalsIgnoreCase(operation) || "delete".equalsIgnoreCase(operation)));
    }
    
    private String generateUrlForDocumentation(String service, String table, String operation, String pattern) {
        if ("proxy".equalsIgnoreCase(pattern)) {
            return urlGenerator.generateProxyUrl() + " (Internal: " + urlGenerator.generateProxyInternalEndpoint(service, table, operation) + ")";
        } else {
            return urlGenerator.generateDirectUrl(service, table, operation);
        }
    }
    
    private String generateRequestBodyForDocumentation(String table, String operation, String testType) {
        try {
            switch (testType.toLowerCase()) {
                case "normal":
                    return requestBodyGenerator.generateNormalRequestBody(table, operation);
                case "null_constraint":
                    return requestBodyGenerator.generateNullConstraintViolationBody(table, operation);
                case "unique_constraint":
                    return requestBodyGenerator.generateUniqueConstraintViolationBody(table, operation);
                case "foreign_key_invalid":
                    return requestBodyGenerator.generateInvalidForeignKeyBody(table, operation);
                default:
                    return "No request body for " + operation.toUpperCase() + " operation";
            }
        } catch (Exception e) {
            return "Error generating request body: " + e.getMessage();
        }
    }
    
    private String getExpectedStatusCode(String operation, String testType) {
        if ("normal".equals(testType)) {
            return configManager.getProperty("validation.status_code." + operation.toLowerCase(), "200");
        } else {
            return configManager.getProperty("validation.constraint_violation.expected_status", "400,422");
        }
    }
    
    private String generateTestDescription(String table, String operation, String testType) {
        switch (testType.toLowerCase()) {
            case "normal":
                return "Normal " + operation.toUpperCase() + " operation test for " + table + " table";
            case "null_constraint":
                return "Null constraint violation test for " + operation.toUpperCase() + " operation on " + table + " table";
            case "unique_constraint":
                return "Unique constraint violation test for " + operation.toUpperCase() + " operation on " + table + " table";
            case "foreign_key_invalid":
                return "Invalid foreign key test for " + operation.toUpperCase() + " operation on " + table + " table";
            default:
                return operation.toUpperCase() + " operation test for " + table + " table";
        }
    }
    
    private String generateValidationPoints(String operation, String testType) {
        List<String> validations = new ArrayList<>();
        
        if ("normal".equals(testType)) {
            validations.add("Status code validation");
            if ("post".equalsIgnoreCase(operation) || "put".equalsIgnoreCase(operation)) {
                validations.add("ID extraction from response");
                validations.add("Database validation");
                validations.add("Request/Response body comparison");
            }
        } else {
            validations.add("Status code validation (expecting error)");
            validations.add("Constraint enforcement validation");
        }
        
        return String.join(", ", validations);
    }
    
    private int addConfigSection(String filePath, String sheetName, int startRow, String category, Map<String, String> configs) {
        int rowNum = startRow;
        
        for (Map.Entry<String, String> entry : configs.entrySet()) {
            excelUtils.setCellData(filePath, sheetName, rowNum, 1, category);
            excelUtils.setCellData(filePath, sheetName, rowNum, 2, entry.getKey());
            excelUtils.setCellData(filePath, sheetName, rowNum, 3, entry.getValue() != null ? entry.getValue() : "");
            excelUtils.setCellData(filePath, sheetName, rowNum, 4, getConfigDescription(entry.getKey()));
            rowNum++;
        }
        
        return rowNum;
    }
    
    private String getConfigDescription(String key) {
        Map<String, String> descriptions = Map.of(
            "auto.test.types", "Types of tests to execute automatically",
            "auto.test.operations", "CRUD operations to test automatically",
            "direct.pattern.base_url", "Base URL for direct API pattern",
            "proxy.pattern.base_url", "Base URL for proxy API pattern",
            "validation.status_code.post", "Expected status codes for POST operations"
        );
        
        return descriptions.getOrDefault(key, "Configuration parameter");
    }
}
