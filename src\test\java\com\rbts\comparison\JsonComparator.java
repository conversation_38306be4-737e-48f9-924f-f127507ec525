package com.rbts.comparison;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * JSON Comparator for CRUD Testing Framework
 * Handles comparison between expected and actual JSON responses
 */
@Slf4j
public class JsonComparator {
    
    private final ObjectMapper objectMapper;
    
    public JsonComparator() {
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Compare two JSON strings and return comparison result
     */
    public ComparisonResult compareJson(String expectedJson, String actualJson) {
        return compareJson(expectedJson, actualJson, JSONCompareMode.LENIENT);
    }
    
    /**
     * Compare two JSON strings with specified comparison mode
     */
    public ComparisonResult compareJson(String expectedJson, String actualJson, JSONCompareMode compareMode) {
        ComparisonResult result = new ComparisonResult();
        
        try {
            // Handle null cases
            if (expectedJson == null && actualJson == null) {
                result.setMatch(true);
                result.setMessage("Both JSON strings are null");
                return result;
            }
            
            if (expectedJson == null) {
                result.setMatch(false);
                result.setMessage("Expected JSON is null but actual JSON is not null");
                result.addDifference("Expected: null, Actual: " + actualJson);
                return result;
            }
            
            if (actualJson == null) {
                result.setMatch(false);
                result.setMessage("Actual JSON is null but expected JSON is not null");
                result.addDifference("Expected: " + expectedJson + ", Actual: null");
                return result;
            }
            
            // Parse JSON strings to validate format
            JsonNode expectedNode = objectMapper.readTree(expectedJson);
            JsonNode actualNode = objectMapper.readTree(actualJson);
            
            // Use JSONAssert for comparison
            JSONAssert.assertEquals(expectedJson, actualJson, compareMode);
            
            result.setMatch(true);
            result.setMessage("JSON comparison successful");
            
            log.debug("JSON comparison passed for expected: {} and actual: {}", expectedJson, actualJson);
            
        } catch (Exception e) {
            result.setMatch(false);
            result.setMessage("JSON comparison failed: " + e.getMessage());
            result.addDifference(e.getMessage());
            
            // Add detailed field-by-field comparison
            try {
                List<String> differences = getDetailedDifferences(expectedJson, actualJson);
                result.getDifferences().addAll(differences);
            } catch (Exception ex) {
                log.warn("Could not generate detailed differences: {}", ex.getMessage());
            }
            
            log.warn("JSON comparison failed: {}", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Get detailed field-by-field differences between two JSON strings
     */
    private List<String> getDetailedDifferences(String expectedJson, String actualJson) {
        List<String> differences = new ArrayList<>();
        
        try {
            JsonNode expectedNode = objectMapper.readTree(expectedJson);
            JsonNode actualNode = objectMapper.readTree(actualJson);
            
            // Compare all fields in expected JSON
            Iterator<String> fieldNames = expectedNode.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                JsonNode expectedValue = expectedNode.get(fieldName);
                JsonNode actualValue = actualNode.get(fieldName);
                
                if (actualValue == null) {
                    differences.add(String.format("Field '%s' is missing in actual JSON. Expected: %s", 
                                                fieldName, expectedValue.toString()));
                } else if (!expectedValue.equals(actualValue)) {
                    differences.add(String.format("Field '%s' mismatch. Expected: %s, Actual: %s", 
                                                fieldName, expectedValue.toString(), actualValue.toString()));
                }
            }
            
            // Check for extra fields in actual JSON
            Iterator<String> actualFieldNames = actualNode.fieldNames();
            while (actualFieldNames.hasNext()) {
                String fieldName = actualFieldNames.next();
                if (!expectedNode.has(fieldName)) {
                    JsonNode actualValue = actualNode.get(fieldName);
                    differences.add(String.format("Extra field '%s' found in actual JSON: %s", 
                                                fieldName, actualValue.toString()));
                }
            }
            
        } catch (Exception e) {
            differences.add("Error generating detailed differences: " + e.getMessage());
        }
        
        return differences;
    }
    
    /**
     * Compare JSON with ignore fields
     */
    public ComparisonResult compareJsonIgnoringFields(String expectedJson, String actualJson, 
                                                    List<String> fieldsToIgnore) {
        try {
            JsonNode expectedNode = objectMapper.readTree(expectedJson);
            JsonNode actualNode = objectMapper.readTree(actualJson);
            
            // Remove ignored fields from both JSON objects
            JsonNode filteredExpected = removeFields(expectedNode, fieldsToIgnore);
            JsonNode filteredActual = removeFields(actualNode, fieldsToIgnore);
            
            String filteredExpectedJson = objectMapper.writeValueAsString(filteredExpected);
            String filteredActualJson = objectMapper.writeValueAsString(filteredActual);
            
            return compareJson(filteredExpectedJson, filteredActualJson);
            
        } catch (Exception e) {
            ComparisonResult result = new ComparisonResult();
            result.setMatch(false);
            result.setMessage("Error comparing JSON with ignored fields: " + e.getMessage());
            result.addDifference(e.getMessage());
            return result;
        }
    }
    
    /**
     * Remove specified fields from JSON node
     */
    private JsonNode removeFields(JsonNode node, List<String> fieldsToRemove) {
        if (node.isObject()) {
            JsonNode copy = node.deepCopy();
            for (String field : fieldsToRemove) {
                ((com.fasterxml.jackson.databind.node.ObjectNode) copy).remove(field);
            }
            return copy;
        }
        return node;
    }
    
    /**
     * Validate if string is valid JSON
     */
    public boolean isValidJson(String jsonString) {
        try {
            objectMapper.readTree(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Pretty print JSON string
     */
    public String prettyPrintJson(String jsonString) {
        try {
            JsonNode node = objectMapper.readTree(jsonString);
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(node);
        } catch (Exception e) {
            log.warn("Could not pretty print JSON: {}", e.getMessage());
            return jsonString;
        }
    }
    
    /**
     * Comparison Result class
     */
    public static class ComparisonResult {
        private boolean match;
        private String message;
        private List<String> differences;
        
        public ComparisonResult() {
            this.differences = new ArrayList<>();
        }
        
        public boolean isMatch() {
            return match;
        }
        
        public void setMatch(boolean match) {
            this.match = match;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public List<String> getDifferences() {
            return differences;
        }
        
        public void setDifferences(List<String> differences) {
            this.differences = differences;
        }
        
        public void addDifference(String difference) {
            this.differences.add(difference);
        }
        
        @Override
        public String toString() {
            return String.format("ComparisonResult{match=%s, message='%s', differences=%s}", 
                               match, message, differences);
        }
    }
}
