package com.rbts.config;

import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * Excel Configuration Template Generator
 * Creates the Excel configuration template with all necessary sheets and sample data
 */
@Slf4j
public class ExcelConfigTemplateGenerator {
    
    private final ExcelUtils excelUtils;
    private final String templateFilePath;
    
    public ExcelConfigTemplateGenerator() {
        this.excelUtils = new ExcelUtils();
        this.templateFilePath = "config/Framework_Configuration.xlsx";
    }
    
    /**
     * Generate complete Excel configuration template
     */
    public void generateConfigurationTemplate() {
        log.info("Generating Excel configuration template: {}", templateFilePath);
        
        try {
            generateGeneralConfigSheet();
            generateServiceConfigSheet();
            generateUrlConfigSheet();
            generateDatabaseConfigSheet();
            generateValidationConfigSheet();
            generateTableEndpointsSheet();
            generateConstraintConfigSheet();
            generateErrorMessageValidationSheet();
            generateTestExecutionControlSheet();
            generateServiceSpecificFieldMappingSheets();
            generateInstructionsSheet();
            
            log.info("✅ Excel configuration template generated successfully: {}", templateFilePath);
            
        } catch (Exception e) {
            log.error("❌ Error generating Excel configuration template: {}", e.getMessage());
            throw new RuntimeException("Failed to generate configuration template", e);
        }
    }
    
    /**
     * Generate General Configuration sheet
     */
    private void generateGeneralConfigSheet() {
        String sheetName = "General_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Configuration Key");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Configuration Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "framework.name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Automated CRUD Testing Framework");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Name of the testing framework");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "framework.version");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "1.0.0");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Version of the framework");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "auto.test.operations");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "post,put,patch,get,getall,delete");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "CRUD operations to test automatically");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "auto.test.types");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "normal,null_constraint,unique_constraint,foreign_key_invalid");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Types of tests to execute");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "auto.schema.detection");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Enable automatic database schema detection");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "auto.defect.generation");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Enable automatic defect ID generation");
    }
    
    /**
     * Generate Service Configuration sheet
     */
    private void generateServiceConfigSheet() {
        String sheetName = "Service_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Service Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Tables (comma-separated)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "API Pattern");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "contact");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "AddressType,ContactType,Address,Contact");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "proxy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Contact management service");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "User,Role,Permission,UserRole,UserPermission");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "proxy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Authentication and authorization service");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "core");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Country,State,City,Currency,Language");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "direct");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Core master data service");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "order");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Order,OrderItem,OrderStatus,Payment");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "proxy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Order management service");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "product");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Product,Category,Brand,ProductCategory");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "direct");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Product catalog service");
    }
    
    /**
     * Generate URL Configuration sheet
     */
    private void generateUrlConfigSheet() {
        String sheetName = "URL_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "URL Key");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "URL Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "direct.pattern.base_url");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "http://localhost:8071");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Base URL for direct API pattern");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "proxy.pattern.base_url");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "http://localhost:9762");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Base URL for proxy API pattern");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "proxy.pattern.endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "/decrypt");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Proxy endpoint for encrypted requests");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "proxy.pattern.tenant_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "redberyl_redberyltech_com");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Tenant ID for proxy requests");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "direct.pattern.url_structure");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "{base_url}/{service}/api/{table}/{operation}");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "URL structure template for direct pattern");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "api.pattern.direct.bearer_token");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "your_bearer_token_here");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Bearer token for direct API authentication");
    }
    
    /**
     * Generate Database Configuration sheet
     */
    private void generateDatabaseConfigSheet() {
        String sheetName = "Database_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Database Key");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Database Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "JDBC_URL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "**********************************************");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database connection URL");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "JDBC_USER");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "your_username");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database username");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "JDBC_PASSWORD");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "your_password");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database password");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "JDBC_DRIVER");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "org.postgresql.Driver");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "JDBC driver class");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "database.schema");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "public");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database schema name");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "database.connection.timeout");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "30");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Connection timeout in seconds");
    }
    
    /**
     * Generate Validation Configuration sheet
     */
    private void generateValidationConfigSheet() {
        String sheetName = "Validation_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Validation Key");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Expected Values");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.post");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "201,200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for POST operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.put");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for PUT operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.patch");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for PATCH operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.get");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for GET operations");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.getall");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for GET ALL operations");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.delete");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200,204");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for DELETE operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.constraint_violation.expected_status");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "400,422");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for general constraint violations");
        row++;

        // Specific constraint violation status codes
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.unique_constraint.expected_status");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "701");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status code for unique constraint violations");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.null_constraint.expected_status");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "700");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status code for null constraint violations");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.foreign_key_same_service.expected_status");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status code for foreign key violations within same service");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.foreign_key_other_service.expected_status");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "702");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status code for foreign key violations from other services");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.response.timeout");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "30");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Response timeout in seconds");
    }
    
    /**
     * Generate Table Endpoints sheet
     */
    private void generateTableEndpointsSheet() {
        String sheetName = "Table_Endpoints";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "POST Endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "PUT Endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "PATCH Endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "GET Endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "GET ALL Endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "DELETE Endpoint");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "/contact/api/AddressType/save");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "/contact/api/AddressType/update");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "/contact/api/AddressType/patch");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "/contact/api/AddressType/getById");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "/contact/api/AddressType/getAll");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "/contact/api/AddressType/delete");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "/authentication/api/User/save");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "/authentication/api/User/update");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "/authentication/api/User/patch");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "/authentication/api/User/getById");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "/authentication/api/User/getAll");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "/authentication/api/User/delete");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Country");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "/core/api/Country/save");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "/core/api/Country/update");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "/core/api/Country/patch");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "/core/api/Country/getById");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "/core/api/Country/getAll");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "/core/api/Country/delete");
        row++;

        // Example table with some operations disabled (null endpoints)
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "ExampleTable");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "/example/api/ExampleTable/save");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, ""); // Empty PUT endpoint - will skip PUT tests
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "null"); // Null PATCH endpoint - will skip PATCH tests
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "/example/api/ExampleTable/getById");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "/example/api/ExampleTable/getAll");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, ""); // Empty DELETE endpoint - will skip DELETE tests
    }
    
    /**
     * Generate Constraint Configuration sheet
     */
    private void generateConstraintConfigSheet() {
        String sheetName = "Constraint_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Null Constraints (comma-separated)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Unique Constraints (comma-separated)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "Foreign Keys (comma-separated)");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "type,isActive");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "type");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "username,email,firstName,lastName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "username,email");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Address");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "addressLine1,city,state,country");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "addressTypeId,countryId,stateId");
    }

    /**
     * Generate Error Message Validation sheet
     */
    private void generateErrorMessageValidationSheet() {
        String sheetName = "Error_Message_Validation";
        log.info("Generating {} sheet", sheetName);

        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Service Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Field Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "Constraint Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "Expected Status Code");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "Expected Error Message");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Test Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 8, "Message Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 9, "Description");

        // Sample data for error message validation
        int row = 2;

        // State table - null constraint on stateShortName
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "core");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "State");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "stateShortName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "null_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "700");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "State short name cannot be null");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "null");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "error");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test null constraint violation for state short name");
        row++;

        // State table - unique constraint on stateShortName
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "core");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "State");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "stateShortName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "unique_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "701");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "State short name 'CA' already exists");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "CA");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "error");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test unique constraint violation for duplicate state short name");
        row++;

        // User table - null constraint on email
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "email");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "null_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "700");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "Email address is required");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "null");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "error");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test null constraint violation for user email");
        row++;

        // User table - unique constraint on email
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "email");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "unique_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "701");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "Email address '<EMAIL>' is already registered");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "<EMAIL>");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "error");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test unique constraint violation for duplicate email");
        row++;

        // UserProfile table - foreign key constraint (same service)
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "UserProfile");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "userId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "foreign_key_same_service");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "User with ID 999 not found");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "999");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "error");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test foreign key constraint violation for invalid user ID");
        row++;

        // UserProfile table - foreign key constraint (different service)
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "UserProfile");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "countryId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "foreign_key_different_service");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "702");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "Country service is unavailable or country ID 999 not found");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "999");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "error");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test foreign key constraint violation for invalid country ID from different service");
        row++;

        // AddressType table - successful creation message
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "contact");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "success");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "201");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "Address type created successfully");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "valid_data");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "success");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test successful creation message for address type");
        row++;

        // Order table - successful creation message
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "order");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Order");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "success");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "201");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "Order created successfully with ID {orderId}");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "valid_data");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "success");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test successful creation message for order with dynamic ID");
        row++;

        // Product table - validation error message
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "product");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Product");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "price");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "validation_error");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "400");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "Product price must be greater than 0");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "-10.50");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "error");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test validation error for negative product price");
    }

    /**
     * Generate Test Execution Control sheet
     */
    private void generateTestExecutionControlSheet() {
        String sheetName = "Test_Execution_Control";
        log.info("Generating {} sheet", sheetName);

        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Service Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Enable Service");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "Enable Table");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "Operations to Test");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "Test Priority");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Test Types");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 8, "Execution Order");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 9, "Comments");

        // Sample data for selective testing
        int row = 2;

        // Contact Service - Enable only AddressType table
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "contact");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "post,get,getall");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "high");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "normal,constraint_validation");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "1");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Test only basic CRUD operations");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "contact");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "ContactType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "false");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "low");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Disabled for this test run");
        row++;

        // Authentication Service - Enable only User table with specific operations
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "post,put,get");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "high");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "normal,unique_constraint,null_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "2");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Focus on user management operations");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Role");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "false");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "medium");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Skip role testing for now");
        row++;

        // Core Service - Disable entire service
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "core");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Country");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "false");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "false");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "low");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Core service disabled for this test cycle");
        row++;

        // Order Service - Enable specific table with all operations
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "order");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Order");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "post,put,patch,get,getall,delete");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "high");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "normal,constraint_validation,foreign_key_validation");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "3");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Complete testing for order management");
        row++;

        // Product Service - Enable with limited operations
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "product");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Product");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "post,get");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "medium");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "normal");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "4");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "Basic product operations only");
    }

    /**
     * Generate service-specific field mapping sheets
     */
    private void generateServiceSpecificFieldMappingSheets() {
        log.info("Generating service-specific field mapping sheets");

        // Generate field mapping sheets for each service
        generateContactServiceFieldMappingSheet();
        generateAuthenticationServiceFieldMappingSheet();
        generateCoreServiceFieldMappingSheet();
        generateOrderServiceFieldMappingSheet();
        generateProductServiceFieldMappingSheet();
    }

    /**
     * Generate Contact Service Field Mapping sheet
     */
    private void generateContactServiceFieldMappingSheet() {
        String sheetName = "FieldMapping_Contact";
        log.info("Generating {} sheet", sheetName);

        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Database Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "API Request Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "API Response Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "Field Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "Request Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Response Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 8, "FK Same Service");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 9, "FK Different Service");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 10, "Description");

        int row = 2;

        // AddressType table field mappings
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "address_type_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "addressTypeId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "Address type unique identifier");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "type_name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "typeName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "STRING");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "Address type name (Home, Work, etc.)");
        row++;

        // Audit fields for AddressType - creation fields in all responses
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "created_by");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "createdBy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "AUDIT_FIELD");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "NONE");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "POST,PUT,PATCH,GET,GETALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "User who created the record - in all responses");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "created_at");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "createdAt");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "AUDIT_TIMESTAMP");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "NONE");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "POST,PUT,PATCH,GET,GETALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "Timestamp when record was created - in all responses");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "modified_by");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "modifiedBy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "lastModifiedBy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "AUDIT_FIELD");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "PUT,PATCH,GET,GETALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "User who last modified the record");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "modified_at");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "lastModifiedAt");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "lastModifiedAt");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "AUDIT_TIMESTAMP");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "PUT,PATCH,GET,GETALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "Timestamp when record was last modified");
    }

    /**
     * Generate Authentication Service Field Mapping sheet
     */
    private void generateAuthenticationServiceFieldMappingSheet() {
        String sheetName = "FieldMapping_Auth";
        log.info("Generating {} sheet", sheetName);

        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Database Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "API Request Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "API Response Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "Field Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "Request Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Response Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 8, "FK Same Service");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 9, "FK Different Service");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 10, "Description");

        int row = 2;

        // User table field mappings with audit fields
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "user_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "userId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "User unique identifier");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "email_address");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "email");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "emailAddress");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "EMAIL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "User email address");
        row++;

        // Example: UserProfile table with foreign key to User (same service)
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "UserProfile");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "user_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "userId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "userId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "FOREIGN_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "FK to User table (same service) - full payload");
        row++;

        // Example: UserProfile table with foreign key to Country (different service)
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "UserProfile");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "country_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "countryId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "countryId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "FOREIGN_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "core");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "FK to Country table (different service) - ID only");
        row++;

        // Audit fields - created fields not in request, but in all responses including PUT
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "created_by");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "createdBy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "AUDIT_FIELD");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "NONE");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "POST,PUT,PATCH,GET,GETALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "User who created the record - not in requests, but in all responses");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "created_at");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "createdAt");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "AUDIT_TIMESTAMP");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "NONE");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "POST,PUT,PATCH,GET,GETALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "Creation timestamp - not in requests, but in all responses");
        row++;

        // Modified fields - in PUT/PATCH requests and responses
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "modified_by");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "modifiedBy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "lastModifiedBy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "AUDIT_FIELD");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "PUT,PATCH,GET,GETALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "User who last modified - in PUT/PATCH requests and responses");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "last_modified_at");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "lastModifiedAt");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "lastModifiedAt");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "AUDIT_TIMESTAMP");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "PUT,PATCH,GET,GETALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "Last modification timestamp - in PUT/PATCH requests and responses");
    }

    /**
     * Generate Core Service Field Mapping sheet
     */
    private void generateCoreServiceFieldMappingSheet() {
        String sheetName = "FieldMapping_Core";
        log.info("Generating {} sheet", sheetName);

        // Headers (same structure)
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Database Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "API Request Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "API Response Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "Field Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "Request Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Response Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 8, "Description");

        // Add Country table example with minimal audit fields
        int row = 2;
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Country");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "country_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "countryId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "Country unique identifier");
    }

    /**
     * Generate Order Service Field Mapping sheet
     */
    private void generateOrderServiceFieldMappingSheet() {
        String sheetName = "FieldMapping_Order";
        log.info("Generating {} sheet", sheetName);

        // Headers (same structure)
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Database Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "API Request Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "API Response Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "Field Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "Request Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Response Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 8, "Description");

        // Add Order table example with full audit trail
        int row = 2;
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Order");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "order_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "orderId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "Order unique identifier");
    }

    /**
     * Generate Product Service Field Mapping sheet
     */
    private void generateProductServiceFieldMappingSheet() {
        String sheetName = "FieldMapping_Product";
        log.info("Generating {} sheet", sheetName);

        // Headers (same structure)
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Database Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "API Request Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "API Response Field");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "Field Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "Request Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Response Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 8, "Description");

        // Add Product table example
        int row = 2;
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Product");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "product_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "productId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "Product unique identifier");
    }

    /**
     * Generate Instructions sheet
     */
    private void generateInstructionsSheet() {
        String sheetName = "Instructions";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Sheet Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Purpose");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Instructions");
        
        // Instructions data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "General_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Framework settings");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure framework name, version, and general settings");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Service_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Service and table mapping");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Add your services, tables, and API patterns (direct/proxy)");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "URL_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "API URL configuration");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure base URLs, endpoints, and URL structures");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Database_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Database connection");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure database connection details");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Validation_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Test validation rules");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure expected status codes and validation rules");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Table_Endpoints");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Table-specific endpoints");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure specific endpoints for each table and operation. Leave empty or 'null' to skip testing that operation.");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Constraint_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Database constraints");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure null, unique, and foreign key constraints for testing");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "FieldMapping_Contact");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Contact service field mappings");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Map database fields to API request/response fields for Contact service tables");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "FieldMapping_Auth");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Authentication service field mappings");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Map database fields to API request/response fields for Authentication service tables");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "FieldMapping_Core");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Core service field mappings");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Map database fields to API request/response fields for Core service tables");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "FieldMapping_Order");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Order service field mappings");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Map database fields to API request/response fields for Order service tables");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "FieldMapping_Product");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Product service field mappings");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Map database fields to API request/response fields for Product service tables");
        row++;

        // Add special instructions for null endpoints
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "IMPORTANT");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Null/Empty Endpoints");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "In Table_Endpoints sheet: Leave cell empty, enter 'null', or enter '' to skip testing that operation for that table.");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "EXAMPLE");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Selective Testing");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "ExampleTable has empty PUT and DELETE endpoints, so only POST, PATCH, GET, and GET ALL will be tested.");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "BENEFIT");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Fine-grained Control");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Test only the operations that are actually implemented for each table. Skip operations that don't exist.");
        row++;

        // Add field mapping instructions
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "FIELD MAPPING");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Database vs API Fields");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Use Field_Mapping sheet to map database field names to API request/response field names for proper validation.");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "MAPPING EXAMPLE");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Field Name Differences");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "DB: 'user_id' → API Request: 'userId' → API Response: 'id'. Framework will handle the mapping automatically.");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "MAPPING BENEFIT");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Accurate Validation");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Ensures proper field matching between database, API requests, and API responses for accurate test validation.");
        row++;

        // Add audit field instructions
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AUDIT FIELDS");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Special Field Handling");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Audit fields like createdBy, createdAt are not in POST requests but appear in POST responses. Use Request Operations and Response Operations columns.");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "OPERATION COLUMNS");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Request/Response Operations");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Request Operations: POST,PUT,PATCH,GET,GETALL,DELETE or NONE. Response Operations: POST,PUT,PATCH,GET,GETALL,DELETE or ALL.");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AUDIT EXAMPLE");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Creation Audit Fields");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "createdBy: Request Operations = NONE, Response Operations = POST,GET,GETALL (not in POST request, only in response)");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "MODIFICATION EXAMPLE");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Modification Audit Fields");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "modifiedBy: Request Operations = PUT,PATCH, Response Operations = PUT,PATCH,GET,GETALL (in PUT/PATCH requests and responses)");
        row++;

        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "SERVICE ORGANIZATION");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Separate Sheets per Service");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Each service has its own FieldMapping sheet for better organization and maintenance. Configure fields specific to each service.");
    }
    
    /**
     * Generate configuration template with custom file path
     */
    public void generateConfigurationTemplate(String customFilePath) {
        String originalPath = this.templateFilePath;
        try {
            // Temporarily change the file path
            java.lang.reflect.Field field = this.getClass().getDeclaredField("templateFilePath");
            field.setAccessible(true);
            field.set(this, customFilePath);
            
            generateConfigurationTemplate();
            
        } catch (Exception e) {
            log.error("Error generating custom configuration template: {}", e.getMessage());
        } finally {
            // Restore original path
            try {
                java.lang.reflect.Field field = this.getClass().getDeclaredField("templateFilePath");
                field.setAccessible(true);
                field.set(this, originalPath);
            } catch (Exception e) {
                log.warn("Could not restore original template file path");
            }
        }
    }
}
