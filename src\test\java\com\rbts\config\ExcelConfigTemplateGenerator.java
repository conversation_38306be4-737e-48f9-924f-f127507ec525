package com.rbts.config;

import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * Excel Configuration Template Generator
 * Creates the Excel configuration template with all necessary sheets and sample data
 */
@Slf4j
public class ExcelConfigTemplateGenerator {
    
    private final ExcelUtils excelUtils;
    private final String templateFilePath;
    
    public ExcelConfigTemplateGenerator() {
        this.excelUtils = new ExcelUtils();
        this.templateFilePath = "config/Framework_Configuration.xlsx";
    }
    
    /**
     * Generate complete Excel configuration template
     */
    public void generateConfigurationTemplate() {
        log.info("Generating Excel configuration template: {}", templateFilePath);
        
        try {
            generateGeneralConfigSheet();
            generateServiceConfigSheet();
            generateUrlConfigSheet();
            generateDatabaseConfigSheet();
            generateValidationConfigSheet();
            generateTableEndpointsSheet();
            generateConstraintConfigSheet();
            generateInstructionsSheet();
            
            log.info("✅ Excel configuration template generated successfully: {}", templateFilePath);
            
        } catch (Exception e) {
            log.error("❌ Error generating Excel configuration template: {}", e.getMessage());
            throw new RuntimeException("Failed to generate configuration template", e);
        }
    }
    
    /**
     * Generate General Configuration sheet
     */
    private void generateGeneralConfigSheet() {
        String sheetName = "General_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Configuration Key");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Configuration Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "framework.name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Automated CRUD Testing Framework");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Name of the testing framework");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "framework.version");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "1.0.0");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Version of the framework");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "auto.test.operations");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "post,put,patch,get,delete");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "CRUD operations to test automatically");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "auto.test.types");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "normal,null_constraint,unique_constraint,foreign_key_invalid");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Types of tests to execute");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "auto.schema.detection");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Enable automatic database schema detection");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "auto.defect.generation");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Enable automatic defect ID generation");
    }
    
    /**
     * Generate Service Configuration sheet
     */
    private void generateServiceConfigSheet() {
        String sheetName = "Service_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Service Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Tables (comma-separated)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "API Pattern");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "contact");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "AddressType,ContactType,Address,Contact");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "proxy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Contact management service");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "User,Role,Permission,UserRole,UserPermission");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "proxy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Authentication and authorization service");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "core");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Country,State,City,Currency,Language");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "direct");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Core master data service");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "order");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Order,OrderItem,OrderStatus,Payment");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "proxy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Order management service");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "product");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Product,Category,Brand,ProductCategory");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "direct");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "Product catalog service");
    }
    
    /**
     * Generate URL Configuration sheet
     */
    private void generateUrlConfigSheet() {
        String sheetName = "URL_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "URL Key");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "URL Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "direct.pattern.base_url");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "http://localhost:8071");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Base URL for direct API pattern");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "proxy.pattern.base_url");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "http://localhost:9762");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Base URL for proxy API pattern");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "proxy.pattern.endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "/decrypt");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Proxy endpoint for encrypted requests");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "proxy.pattern.tenant_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "redberyl_redberyltech_com");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Tenant ID for proxy requests");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "direct.pattern.url_structure");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "{base_url}/{service}/api/{table}/{operation}");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "URL structure template for direct pattern");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "api.pattern.direct.bearer_token");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "your_bearer_token_here");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Bearer token for direct API authentication");
    }
    
    /**
     * Generate Database Configuration sheet
     */
    private void generateDatabaseConfigSheet() {
        String sheetName = "Database_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Database Key");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Database Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "JDBC_URL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "**********************************************");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database connection URL");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "JDBC_USER");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "your_username");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database username");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "JDBC_PASSWORD");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "your_password");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database password");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "JDBC_DRIVER");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "org.postgresql.Driver");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "JDBC driver class");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "database.schema");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "public");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database schema name");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "database.connection.timeout");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "30");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Connection timeout in seconds");
    }
    
    /**
     * Generate Validation Configuration sheet
     */
    private void generateValidationConfigSheet() {
        String sheetName = "Validation_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Validation Key");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Expected Values");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Description");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.post");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "201,200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for POST operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.put");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for PUT operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.patch");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for PATCH operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.get");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for GET operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.status_code.delete");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200,204");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for DELETE operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.constraint_violation.expected_status");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "400,422");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Expected status codes for constraint violations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "validation.response.timeout");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "30");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Response timeout in seconds");
    }
    
    /**
     * Generate Table Endpoints sheet
     */
    private void generateTableEndpointsSheet() {
        String sheetName = "Table_Endpoints";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "POST Endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "PUT Endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "PATCH Endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "GET Endpoint");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "DELETE Endpoint");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "/contact/api/AddressType/save");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "/contact/api/AddressType/update");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "/contact/api/AddressType/patch");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "/contact/api/AddressType/getById");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "/contact/api/AddressType/delete");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "/authentication/api/User/save");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "/authentication/api/User/update");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "/authentication/api/User/patch");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "/authentication/api/User/getById");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "/authentication/api/User/delete");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Country");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "/core/api/Country/save");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "/core/api/Country/update");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "/core/api/Country/patch");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "/core/api/Country/getById");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "/core/api/Country/delete");
    }
    
    /**
     * Generate Constraint Configuration sheet
     */
    private void generateConstraintConfigSheet() {
        String sheetName = "Constraint_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Null Constraints (comma-separated)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Unique Constraints (comma-separated)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "Foreign Keys (comma-separated)");
        
        // Sample data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "type,isActive");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "type");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "username,email,firstName,lastName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "username,email");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Address");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "addressLine1,city,state,country");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "addressTypeId,countryId,stateId");
    }
    
    /**
     * Generate Instructions sheet
     */
    private void generateInstructionsSheet() {
        String sheetName = "Instructions";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Sheet Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Purpose");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Instructions");
        
        // Instructions data
        int row = 2;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "General_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Framework settings");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure framework name, version, and general settings");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Service_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Service and table mapping");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Add your services, tables, and API patterns (direct/proxy)");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "URL_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "API URL configuration");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure base URLs, endpoints, and URL structures");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Database_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Database connection");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure database connection details");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Validation_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Test validation rules");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure expected status codes and validation rules");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Table_Endpoints");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Table-specific endpoints");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure specific endpoints for each table and operation");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Constraint_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Database constraints");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Configure null, unique, and foreign key constraints for testing");
    }
    
    /**
     * Generate configuration template with custom file path
     */
    public void generateConfigurationTemplate(String customFilePath) {
        String originalPath = this.templateFilePath;
        try {
            // Temporarily change the file path
            java.lang.reflect.Field field = this.getClass().getDeclaredField("templateFilePath");
            field.setAccessible(true);
            field.set(this, customFilePath);
            
            generateConfigurationTemplate();
            
        } catch (Exception e) {
            log.error("Error generating custom configuration template: {}", e.getMessage());
        } finally {
            // Restore original path
            try {
                java.lang.reflect.Field field = this.getClass().getDeclaredField("templateFilePath");
                field.setAccessible(true);
                field.set(this, originalPath);
            } catch (Exception e) {
                log.warn("Could not restore original template file path");
            }
        }
    }
}
