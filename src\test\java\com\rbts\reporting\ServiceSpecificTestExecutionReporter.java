package com.rbts.reporting;

import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Service-specific test execution reporter that creates separate Excel sheets for each service
 * Each service gets its own sheet with independent test case and defect counters
 */
@Slf4j
public class ServiceSpecificTestExecutionReporter {
    
    private final ExcelUtils excelUtils;
    private final String reportFilePath;
    private final Map<String, Integer> serviceRowCounters;
    private final Map<String, AtomicInteger> serviceTestCaseCounters;
    private final Map<String, AtomicInteger> serviceDefectCounters;
    private final AtomicInteger globalTestCaseCounter;
    private final AtomicInteger globalDefectCounter;
    
    public ServiceSpecificTestExecutionReporter() {
        this.excelUtils = new ExcelUtils();
        this.reportFilePath = "reports/Service_Specific_Test_Execution_Report.xlsx";
        this.serviceRowCounters = new HashMap<>();
        this.serviceTestCaseCounters = new HashMap<>();
        this.serviceDefectCounters = new HashMap<>();
        this.globalTestCaseCounter = new AtomicInteger(1);
        this.globalDefectCounter = new AtomicInteger(1);
        
        // Create reports directory if it doesn't exist
        File reportsDir = new File("reports");
        if (!reportsDir.exists()) {
            reportsDir.mkdirs();
        }
        
        log.info("📊 Service-Specific Test Execution Reporter initialized");
        log.info("📁 Report file: {}", reportFilePath);
    }
    
    /**
     * Report a test case execution result for a specific service and table
     */
    public void reportTestCaseForService(String serviceName, TestCaseResult testResult) {
        try {
            // Extract service and table names from table name if not provided
            if (serviceName == null || serviceName.trim().isEmpty()) {
                serviceName = extractServiceFromTableName(testResult.getTableName());
            }

            // Extract table name for sheet naming
            String tableName = extractTableFromTableName(testResult.getTableName());

            // Create table-specific sheet name
            String sheetName = serviceName + "_" + tableName + "_Test_Results";
            ensureTableSheetExists(sheetName, serviceName, tableName);

            // Generate table-specific test case ID
            String testCaseId = generateTableTestCaseId(serviceName, tableName, testResult.getOperation());

            // Generate defect ID if test failed
            String defectId = "";
            if ("FAIL".equals(testResult.getStatus())) {
                defectId = generateTableDefectId(serviceName, tableName, testResult.getOperation());
            }

            // Get current row for this table
            int currentRow = getNextRowForTable(sheetName);

            // Write test case data with retry mechanism
            writeTableTestCaseDataWithRetry(sheetName, currentRow, testCaseId, testResult, defectId);

            // Apply color coding
            applyTableColorCoding(sheetName, currentRow, testResult.getStatus());

            log.info("📝 Test case reported for {}.{}: {} - {} - {}",
                    serviceName, tableName, testCaseId, testResult.getTableName(), testResult.getStatus());

        } catch (Exception e) {
            log.error("❌ Error reporting test case for service {}: {}", serviceName, e.getMessage());
            // Log the test case to console as fallback
            logTestCaseToConsole(serviceName, testResult);
        }
    }
    
    /**
     * Extract service name from table name (e.g., "Order.Order" -> "Order")
     */
    private String extractServiceFromTableName(String tableName) {
        if (tableName != null && tableName.contains(".")) {
            return tableName.split("\\.")[0];
        }
        return "General";
    }

    /**
     * Extract table name from table name (e.g., "Order.bundle_products" -> "bundle_products")
     */
    private String extractTableFromTableName(String tableName) {
        if (tableName != null && tableName.contains(".")) {
            return tableName.split("\\.")[1];
        }
        return tableName != null ? tableName : "UnknownTable";
    }

    /**
     * Ensure table-specific sheet exists with proper headers
     */
    private void ensureTableSheetExists(String sheetName, String serviceName, String tableName) {
        try {
            // Check if we've already initialized this table sheet
            if (serviceRowCounters.containsKey(sheetName)) {
                return; // Sheet already exists
            }

            log.info("📋 Creating new table sheet: {} for {}.{}", sheetName, serviceName, tableName);

            // Create headers for the table sheet
            excelUtils.setCellData(reportFilePath, sheetName, 1, 1, "TestCaseId");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 2, "TableName");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 3, "TestCase");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 4, "ExpectedResult");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 5, "ActualResult");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 6, "Status");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 7, "DefectId");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 8, "ExecutionTime");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 9, "Operation");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 10, "RequestBody");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 11, "ResponseBody");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 12, "StatusCode");

            // Initialize row counter for this table sheet (start at row 2)
            serviceRowCounters.put(sheetName, 2);

            // Initialize test case and defect counters for this table
            String tableKey = serviceName + "." + tableName;
            serviceTestCaseCounters.put(tableKey, new AtomicInteger(1));
            serviceDefectCounters.put(tableKey, new AtomicInteger(1));

            log.info("✅ Table sheet created successfully: {} for {}.{}", sheetName, serviceName, tableName);

        } catch (Exception e) {
            log.error("❌ Error creating table sheet {} for {}.{}: {}", sheetName, serviceName, tableName, e.getMessage());
        }
    }

    /**
     * Get next available row for a table and increment counter
     */
    private int getNextRowForTable(String sheetName) {
        int currentRow = serviceRowCounters.getOrDefault(sheetName, 2);
        serviceRowCounters.put(sheetName, currentRow + 1);
        return currentRow;
    }

    /**
     * Generate table-specific test case ID
     */
    private String generateTableTestCaseId(String serviceName, String tableName, String operation) {
        String tableKey = serviceName + "." + tableName;
        AtomicInteger tableCounter = serviceTestCaseCounters.get(tableKey);
        if (tableCounter == null) {
            tableCounter = new AtomicInteger(1);
            serviceTestCaseCounters.put(tableKey, tableCounter);
        }

        int caseNumber = tableCounter.getAndIncrement();
        return String.format("TC_%s_%s_%s_%03d", serviceName, tableName, operation.toUpperCase(), caseNumber);
    }

    /**
     * Generate table-specific defect ID
     */
    private String generateTableDefectId(String serviceName, String tableName, String operation) {
        String tableKey = serviceName + "." + tableName;
        AtomicInteger tableDefectCounter = serviceDefectCounters.get(tableKey);
        if (tableDefectCounter == null) {
            tableDefectCounter = new AtomicInteger(1);
            serviceDefectCounters.put(tableKey, tableDefectCounter);
        }

        int defectNumber = tableDefectCounter.getAndIncrement();
        return String.format("D_%s_%s_%s_%03d", serviceName, tableName, operation.toUpperCase(), defectNumber);
    }

    /**
     * Write table test case data with retry mechanism for file locking issues
     */
    private void writeTableTestCaseDataWithRetry(String sheetName, int row, String testCaseId,
                                               TestCaseResult testResult, String defectId) {
        int maxRetries = 3;
        int retryDelay = 1000; // 1 second

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // Write test case data
                excelUtils.setCellData(reportFilePath, sheetName, row, 1, testCaseId);
                excelUtils.setCellData(reportFilePath, sheetName, row, 2, testResult.getTableName());
                excelUtils.setCellData(reportFilePath, sheetName, row, 3, testResult.getTestCase());
                excelUtils.setCellData(reportFilePath, sheetName, row, 4, testResult.getExpectedResult());
                excelUtils.setCellData(reportFilePath, sheetName, row, 5, testResult.getActualResult());
                excelUtils.setCellData(reportFilePath, sheetName, row, 6, testResult.getStatus());
                excelUtils.setCellData(reportFilePath, sheetName, row, 7, defectId);
                excelUtils.setCellData(reportFilePath, sheetName, row, 8, getCurrentTimestamp());
                excelUtils.setCellData(reportFilePath, sheetName, row, 9, testResult.getOperation());
                excelUtils.setCellData(reportFilePath, sheetName, row, 10, testResult.getRequestBody());
                excelUtils.setCellData(reportFilePath, sheetName, row, 11, testResult.getResponseBody());
                excelUtils.setCellData(reportFilePath, sheetName, row, 12, String.valueOf(testResult.getStatusCode()));

                // If we reach here, writing was successful
                return;

            } catch (Exception e) {
                if (attempt < maxRetries) {
                    log.warn("⚠️ Attempt {} failed to write to Excel sheet {} (file may be locked), retrying in {}ms: {}",
                            attempt, sheetName, retryDelay, e.getMessage());
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.error("❌ Failed to write to Excel sheet {} after {} attempts: {}", sheetName, maxRetries, e.getMessage());
                    throw e;
                }
            }
        }
    }

    /**
     * Apply color coding to Excel row based on test status for table sheets
     */
    private void applyTableColorCoding(String sheetName, int row, String status) {
        try {
            // Apply color coding with retry mechanism
            applyColorCodingWithRetry(sheetName, row, status);
        } catch (Exception e) {
            log.warn("⚠️ Could not apply color coding to sheet {} row {} (file may be locked): {}",
                    sheetName, row, e.getMessage());
        }
    }
    
    /**
     * Ensure service sheet exists with proper headers
     */
    private void ensureServiceSheetExists(String sheetName) {
        try {
            // Check if we've already initialized this service
            if (serviceRowCounters.containsKey(sheetName)) {
                return; // Sheet already exists
            }
            
            log.info("📋 Creating new service sheet: {}", sheetName);
            
            // Create headers for the service sheet
            excelUtils.setCellData(reportFilePath, sheetName, 1, 1, "TestCaseId");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 2, "TableName");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 3, "TestCase");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 4, "ExpectedResult");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 5, "ActualResult");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 6, "Status");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 7, "DefectId");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 8, "ExecutionTime");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 9, "Operation");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 10, "RequestBody");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 11, "ResponseBody");
            excelUtils.setCellData(reportFilePath, sheetName, 1, 12, "StatusCode");
            
            // Initialize row counter for this service (start at row 2)
            serviceRowCounters.put(sheetName, 2);
            
            // Initialize test case and defect counters for this service
            String serviceName = sheetName.replace("_Test_Results", "");
            serviceTestCaseCounters.put(serviceName, new AtomicInteger(1));
            serviceDefectCounters.put(serviceName, new AtomicInteger(1));
            
            log.info("✅ Service sheet created successfully: {}", sheetName);
            
        } catch (Exception e) {
            log.error("❌ Error creating service sheet {}: {}", sheetName, e.getMessage());
        }
    }
    
    /**
     * Get next available row for a service and increment counter
     */
    private int getNextRowForService(String serviceName) {
        String sheetName = serviceName + "_Test_Results";
        int currentRow = serviceRowCounters.getOrDefault(sheetName, 2);
        serviceRowCounters.put(sheetName, currentRow + 1);
        return currentRow;
    }
    
    /**
     * Generate service-specific test case ID
     */
    private String generateServiceTestCaseId(String serviceName, String tableName, String operation) {
        AtomicInteger serviceCounter = serviceTestCaseCounters.get(serviceName);
        if (serviceCounter == null) {
            serviceCounter = new AtomicInteger(1);
            serviceTestCaseCounters.put(serviceName, serviceCounter);
        }
        
        int caseNumber = serviceCounter.getAndIncrement();
        String tableShortName = tableName.contains(".") ? tableName.split("\\.")[1] : tableName;
        return String.format("TC_%s_%s_%s_%03d", serviceName, tableShortName, operation.toUpperCase(), caseNumber);
    }
    
    /**
     * Generate service-specific defect ID
     */
    private String generateServiceDefectId(String serviceName, String tableName, String operation) {
        AtomicInteger serviceDefectCounter = serviceDefectCounters.get(serviceName);
        if (serviceDefectCounter == null) {
            serviceDefectCounter = new AtomicInteger(1);
            serviceDefectCounters.put(serviceName, serviceDefectCounter);
        }
        
        int defectNumber = serviceDefectCounter.getAndIncrement();
        String tableShortName = tableName.contains(".") ? tableName.split("\\.")[1] : tableName;
        return String.format("D_%s_%s_%s_%03d", serviceName, tableShortName, operation.toUpperCase(), defectNumber);
    }
    
    /**
     * Write test case data with retry mechanism for file locking issues
     */
    private void writeServiceTestCaseDataWithRetry(String sheetName, int row, String testCaseId, 
                                                  TestCaseResult testResult, String defectId) {
        int maxRetries = 3;
        int retryDelay = 1000; // 1 second
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // Write test case data
                excelUtils.setCellData(reportFilePath, sheetName, row, 1, testCaseId);
                excelUtils.setCellData(reportFilePath, sheetName, row, 2, testResult.getTableName());
                excelUtils.setCellData(reportFilePath, sheetName, row, 3, testResult.getTestCase());
                excelUtils.setCellData(reportFilePath, sheetName, row, 4, testResult.getExpectedResult());
                excelUtils.setCellData(reportFilePath, sheetName, row, 5, testResult.getActualResult());
                excelUtils.setCellData(reportFilePath, sheetName, row, 6, testResult.getStatus());
                excelUtils.setCellData(reportFilePath, sheetName, row, 7, defectId);
                excelUtils.setCellData(reportFilePath, sheetName, row, 8, getCurrentTimestamp());
                excelUtils.setCellData(reportFilePath, sheetName, row, 9, testResult.getOperation());
                excelUtils.setCellData(reportFilePath, sheetName, row, 10, testResult.getRequestBody());
                excelUtils.setCellData(reportFilePath, sheetName, row, 11, testResult.getResponseBody());
                excelUtils.setCellData(reportFilePath, sheetName, row, 12, String.valueOf(testResult.getStatusCode()));
                
                // If we reach here, writing was successful
                return;
                
            } catch (Exception e) {
                if (attempt < maxRetries) {
                    log.warn("⚠️ Attempt {} failed to write to Excel sheet {} (file may be locked), retrying in {}ms: {}", 
                            attempt, sheetName, retryDelay, e.getMessage());
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.error("❌ Failed to write to Excel sheet {} after {} attempts: {}", sheetName, maxRetries, e.getMessage());
                    throw e;
                }
            }
        }
    }
    
    /**
     * Apply color coding to Excel row based on test status
     */
    private void applyServiceColorCoding(String sheetName, int row, String status) {
        try {
            // Apply color coding with retry mechanism
            applyColorCodingWithRetry(sheetName, row, status);
        } catch (Exception e) {
            log.warn("⚠️ Could not apply color coding to sheet {} row {} (file may be locked): {}", 
                    sheetName, row, e.getMessage());
        }
    }
    
    /**
     * Apply color coding with retry mechanism for file locking issues
     */
    private void applyColorCodingWithRetry(String sheetName, int row, String status) {
        int maxRetries = 2;
        int retryDelay = 500; // 0.5 seconds
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if ("PASS".equals(status)) {
                    excelUtils.setCellBackgroundColor(reportFilePath, sheetName, row, 6, "GREEN");
                } else if ("FAIL".equals(status)) {
                    excelUtils.setCellBackgroundColor(reportFilePath, sheetName, row, 6, "RED");
                }
                return; // Success
                
            } catch (Exception e) {
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    throw e;
                }
            }
        }
    }
    
    /**
     * Get current timestamp
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * Log test case to console as fallback when Excel writing fails
     */
    private void logTestCaseToConsole(String serviceName, TestCaseResult testResult) {
        log.info("📋 FALLBACK CONSOLE REPORT FOR SERVICE {}:", serviceName);
        log.info("   Table: {}", testResult.getTableName());
        log.info("   Operation: {}", testResult.getOperation());
        log.info("   Test Case: {}", testResult.getTestCase());
        log.info("   Expected: {}", testResult.getExpectedResult());
        log.info("   Actual: {}", testResult.getActualResult());
        log.info("   Status: {}", testResult.getStatus());
        log.info("   Status Code: {}", testResult.getStatusCode());
        log.info("   Request: {}", testResult.getRequestBody());
        log.info("   Response: {}", testResult.getResponseBody());
    }
    
    /**
     * Get report file path
     */
    public String getReportFilePath() {
        return reportFilePath;
    }
    
    /**
     * Get total test cases reported across all services
     */
    public int getTotalTestCases() {
        return globalTestCaseCounter.get() - 1;
    }
    
    /**
     * Get total defects reported across all services
     */
    public int getTotalDefects() {
        return globalDefectCounter.get() - 1;
    }
    
    /**
     * Get test cases for a specific service
     */
    public int getTestCasesForService(String serviceName) {
        AtomicInteger serviceCounter = serviceTestCaseCounters.get(serviceName);
        return serviceCounter != null ? serviceCounter.get() - 1 : 0;
    }
    
    /**
     * Get defects for a specific service
     */
    public int getDefectsForService(String serviceName) {
        AtomicInteger serviceDefectCounter = serviceDefectCounters.get(serviceName);
        return serviceDefectCounter != null ? serviceDefectCounter.get() - 1 : 0;
    }
    
    /**
     * Get all services that have been tested
     */
    public java.util.Set<String> getTestedServices() {
        return serviceTestCaseCounters.keySet();
    }
    
    /**
     * Report test case with color coding for a specific service
     */
    public void reportTestCaseWithColorCodingForService(String serviceName, TestCaseResult testResult) {
        // Increment global counters
        globalTestCaseCounter.incrementAndGet();
        if ("FAIL".equals(testResult.getStatus())) {
            globalDefectCounter.incrementAndGet();
        }
        
        // Report to service-specific sheet
        reportTestCaseForService(serviceName, testResult);
    }
}
