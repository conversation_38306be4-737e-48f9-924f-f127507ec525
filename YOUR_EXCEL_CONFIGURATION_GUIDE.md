# 🎯 **Your Excel Configuration Setup Guide**

## ✅ **Step 1: Excel Files Created Successfully!**

The following Excel configuration files have been created in your `config/` directory:

- ✅ `Framework_Configuration.xlsx` - **Main configuration file**
- ✅ `Framework_Configuration_Template.xlsx` - Clean template
- ✅ `Framework_Configuration_Sample.xlsx` - Sample with data
- ✅ `Framework_Configuration_Backup.xlsx` - Backup copy

## 📊 **Step 2: Open and Customize Your Configuration**

### **Open the Excel File:**
```
config/Framework_Configuration.xlsx
```

The file contains **8 sheets** with sample data. Here's how to customize it for your application:

### **🏢 Sheet 2: Service_Config (MOST IMPORTANT)**

**Current Sample Data:**
| Service Name | Tables | API Pattern | Description |
|-------------|--------|-------------|-------------|
| contact | AddressType,ContactType,Address,Contact | proxy | Contact management service |
| authentication | User,Role,Permission,UserRole,UserPermission | proxy | Authentication service |
| core | Country,State,City,Currency,Language | direct | Core master data service |

**👉 Replace with YOUR services and tables:**
| Service Name | Tables | API Pattern | Description |
|-------------|--------|-------------|-------------|
| **your_service_1** | **Table1,Table2,Table3** | **direct** | **Your service description** |
| **your_service_2** | **Table4,Table5,Table6** | **proxy** | **Your service description** |
| **your_service_3** | **Table7,Table8** | **direct** | **Your service description** |

### **🌐 Sheet 3: URL_Config**

**Update these URLs with your actual API endpoints:**
| URL Key | URL Value | Description |
|---------|-----------|-------------|
| direct.pattern.base_url | **http://your-api-server:port** | Your direct API URL |
| proxy.pattern.base_url | **http://your-proxy-server:port** | Your proxy API URL |
| proxy.pattern.tenant_id | **your_actual_tenant_id** | Your tenant ID |
| api.pattern.direct.bearer_token | **your_actual_bearer_token** | Your bearer token |

### **🗄️ Sheet 4: Database_Config**

**Update with your database details:**
| Database Key | Database Value | Description |
|-------------|---------------|-------------|
| JDBC_URL | ************************************************ | Your database URL |
| JDBC_USER | **your_actual_username** | Your database username |
| JDBC_PASSWORD | **your_actual_password** | Your database password |

### **🔗 Sheet 6: Table_Endpoints (Optional)**

**Add specific endpoints for your tables if they differ from the default pattern:**
| Table Name | POST Endpoint | PUT Endpoint | GET Endpoint | DELETE Endpoint |
|-----------|---------------|--------------|--------------|-----------------|
| **YourTable1** | **/your/api/YourTable1/save** | **/your/api/YourTable1/update** | **/your/api/YourTable1/getById** | **/your/api/YourTable1/delete** |

### **🔒 Sheet 7: Constraint_Config (Optional)**

**Add constraint information for better testing:**
| Table Name | Null Constraints | Unique Constraints | Foreign Keys |
|-----------|-----------------|-------------------|--------------|
| **YourTable1** | **field1,field2** | **field1** | **foreignKeyField** |

## 🚀 **Step 3: Test Your Configuration**

After updating the Excel file, test it:

```bash
mvn test -Dtest=ExcelConfigurationTest
```

**Expected Output:**
```
=== EXCEL CONFIGURATION TEST SUMMARY ===
✅ Excel template generation - PASSED
✅ Configuration loading - PASSED
✅ Service and table configuration - PASSED
✅ Table endpoint configuration - PASSED
✅ Validation configuration - PASSED
=== ALL EXCEL CONFIGURATION TESTS PASSED ===

=== EXCEL CONFIGURATION SUMMARY ===
General Config Items: 6
Configured Services: X (your number of services)
Total Tables: X (your number of tables)
URL Configurations: 6
Database Configurations: 6
Validation Configurations: 7
```

## 🎯 **Step 4: Run Automated Tests with Your Configuration**

Once your Excel configuration is ready:

```bash
mvn test -Dtest=AutomatedCrudTest
```

The framework will automatically:
- ✅ Read your services and tables from Excel
- ✅ Use your API URLs and patterns
- ✅ Test all your configured tables
- ✅ Generate comprehensive documentation

## 📋 **Quick Configuration Checklist**

### **Minimum Required Updates:**
- [ ] **Service_Config**: Add your actual services and tables
- [ ] **URL_Config**: Update API URLs (direct.pattern.base_url, proxy.pattern.base_url)
- [ ] **Database_Config**: Update database connection details

### **Optional Updates:**
- [ ] **Table_Endpoints**: Add specific endpoints if needed
- [ ] **Constraint_Config**: Add constraint information for better testing
- [ ] **Validation_Config**: Adjust expected status codes if needed

## 🎯 **Example: Adding Your Service**

### **1. In Service_Config Sheet:**
```
Service Name: order_management
Tables: Order,OrderItem,Payment,Shipment
API Pattern: proxy
Description: Order processing and management
```

### **2. Save Excel File**

### **3. Run Test:**
```bash
mvn test -Dtest=ExcelConfigurationTest
```

### **4. Result:**
Framework automatically detects and configures:
- ✅ 4 new tables (Order, OrderItem, Payment, Shipment)
- ✅ Proxy pattern for all tables
- ✅ Default endpoints for all operations
- ✅ Ready for comprehensive CRUD testing

## 🔧 **Advanced Configuration**

### **Multiple Environments:**
Create separate Excel files:
- `Framework_Configuration_DEV.xlsx`
- `Framework_Configuration_QA.xlsx`
- `Framework_Configuration_PROD.xlsx`

### **Custom Validation Rules:**
In Validation_Config sheet, add:
```
validation.custom.timeout: 60
validation.custom.retry_count: 3
```

### **Pattern-Specific Settings:**
```
direct.pattern.timeout: 30
proxy.pattern.timeout: 60
```

## 🎉 **Benefits You Get**

### **🔄 Dynamic Configuration:**
- Change services/tables without code changes
- Add new APIs instantly
- Update URLs on the fly

### **👥 Team Collaboration:**
- Non-technical team members can update configuration
- Easy to review and approve changes
- Share configuration across teams

### **📊 Visual Management:**
- See all configuration in organized sheets
- Easy to understand relationships
- Clear documentation of what's being tested

## 🆘 **Troubleshooting**

### **If Excel file doesn't open:**
- Make sure you have Excel or compatible software
- Try opening with Google Sheets or LibreOffice

### **If configuration loading fails:**
- Check Excel file path: `config/Framework_Configuration.xlsx`
- Verify sheet names match exactly
- Ensure no empty rows between data

### **If tests fail:**
- Verify database connection details
- Check API URLs are accessible
- Ensure table names match your database

## 🎯 **Next Steps**

1. **Open Excel file**: `config/Framework_Configuration.xlsx`
2. **Update Service_Config** with your services and tables
3. **Update URL_Config** with your API endpoints
4. **Update Database_Config** with your database details
5. **Test configuration**: `mvn test -Dtest=ExcelConfigurationTest`
6. **Run automated tests**: `mvn test -Dtest=AutomatedCrudTest`

**Your framework is now 100% Excel-driven and ready for comprehensive CRUD testing!** 🚀📊

## 📞 **Support**

If you need help:
1. Check the **Instructions** sheet in the Excel file
2. Review the sample data in other sheets
3. Run `ExcelConfigurationTest` to validate your changes
4. Use the backup files if you need to start over

**Happy Testing!** 🎉
