package com.rbts.tests;

import com.rbts.reporting.FilteredServiceSpecificReporter;
import com.rbts.reporting.TestCaseResult;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;

/**
 * Test class to demonstrate filtered service-specific Excel reporting functionality
 * Shows how each service gets its own sheet, but only displays results for tables that are actually being tested
 * 
 * Example: If you test Order service with only bundle_products table, 
 * the Order_Service_Test_Results sheet will only show bundle_products results, not Order table results
 */
@Slf4j
public class FilteredServiceSpecificReportTest {
    
    private FilteredServiceSpecificReporter filteredReporter;
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up Filtered Service-Specific Excel Report Test");
        
        // Create reports directory if it doesn't exist
        File reportsDir = new File("reports");
        if (!reportsDir.exists()) {
            reportsDir.mkdirs();
            log.info("📁 Created reports directory");
        }
        
        // Delete existing report file to start fresh
        File existingReport = new File("reports/Filtered_Service_Test_Execution_Report.xlsx");
        if (existingReport.exists()) {
            existingReport.delete();
            log.info("🗑️ Deleted existing filtered service-specific report file");
        }
        
        // Initialize filtered service-specific test reporter
        filteredReporter = new FilteredServiceSpecificReporter();
        
        log.info("✅ Filtered Service-Specific Excel Report Test setup completed");
        log.info("📊 Report will be generated at: {}", filteredReporter.getReportFilePath());
    }
    
    /**
     * Test Order service - ONLY bundle_products table (no Order table tests)
     * This demonstrates that Order_Service_Test_Results sheet will only show bundle_products results
     */
    @Test(description = "Test Order service with only bundle_products table")
    public void testOrderServiceOnlyBundleProducts() {
        log.info("🧪 Testing Order service - ONLY bundle_products table");
        log.info("📋 This will create Order_Service_Test_Results sheet with ONLY bundle_products results");
        
        // Bundle product creation success
        TestCaseResult bundleProductSuccess = TestCaseResult.builder()
                .tableName("Order.bundle_products")
                .operation("POST")
                .testCase("Create Bundle Product - Gaming Bundle")
                .expectedResult("Status: 201, Bundle product created successfully")
                .actualResult("Status: 201, Bundle product created with ID: 67890")
                .status("PASS")
                .requestBody("{\"bundleName\": \"Gaming Bundle\", \"products\": [{\"productId\": 1, \"quantity\": 1}], \"discountPercentage\": 15}")
                .responseBody("{\"id\": 67890, \"bundleName\": \"Gaming Bundle\", \"totalPrice\": 149.99}")
                .statusCode(201)
                .build();
        
        filteredReporter.reportTestCaseWithColorCodingForService("Order", bundleProductSuccess);
        
        // Bundle product validation error
        TestCaseResult bundleProductValidation = TestCaseResult.builder()
                .tableName("Order.bundle_products")
                .operation("POST")
                .testCase("Bundle Product Validation - Empty Product List")
                .expectedResult("Status: 400, Bundle must contain at least one product")
                .actualResult("Status: 400, Products list cannot be empty")
                .status("PASS")
                .requestBody("{\"bundleName\": \"Empty Bundle\", \"products\": [], \"discountPercentage\": 10}")
                .responseBody("{\"error\": \"Validation failed\", \"message\": \"Products list cannot be empty\"}")
                .statusCode(400)
                .build();
        
        filteredReporter.reportTestCaseWithColorCodingForService("Order", bundleProductValidation);
        
        // Bundle product failure case
        TestCaseResult bundleProductFailure = TestCaseResult.builder()
                .tableName("Order.bundle_products")
                .operation("PUT")
                .testCase("Update Bundle Product - Invalid Discount Percentage")
                .expectedResult("Status: 400, Discount percentage must be between 0 and 100")
                .actualResult("Status: 500, Internal server error")
                .status("FAIL")
                .requestBody("{\"id\": 1, \"bundleName\": \"Updated Bundle\", \"discountPercentage\": 150}")
                .responseBody("{\"error\": \"Internal server error\"}")
                .statusCode(500)
                .errorMessage("Expected 400 but got 500")
                .build();
        
        filteredReporter.reportTestCaseWithColorCodingForService("Order", bundleProductFailure);
        
        log.info("✅ Order service bundle_products tests completed");
        log.info("📊 Order_Service_Test_Results sheet now contains ONLY bundle_products results (3 test cases)");
    }
    
    /**
     * Test Authentication service - ONLY User table (no UserProfile table tests)
     * This demonstrates that Authentication_Service_Test_Results sheet will only show User results
     */
    @Test(description = "Test Authentication service with only User table")
    public void testAuthenticationServiceOnlyUser() {
        log.info("🧪 Testing Authentication service - ONLY User table");
        log.info("📋 This will create Authentication_Service_Test_Results sheet with ONLY User results");
        
        // User creation success
        TestCaseResult userSuccess = TestCaseResult.builder()
                .tableName("Authentication.User")
                .operation("POST")
                .testCase("Create User - Valid Email and Password")
                .expectedResult("Status: 201, User created successfully")
                .actualResult("Status: 201, User created with ID: 456")
                .status("PASS")
                .requestBody("{\"email\": \"<EMAIL>\", \"firstName\": \"John\", \"password\": \"SecurePass123!\"}")
                .responseBody("{\"id\": 456, \"email\": \"<EMAIL>\", \"firstName\": \"John\"}")
                .statusCode(201)
                .build();
        
        filteredReporter.reportTestCaseWithColorCodingForService("Authentication", userSuccess);
        
        // User unique constraint violation
        TestCaseResult userUnique = TestCaseResult.builder()
                .tableName("Authentication.User")
                .operation("POST")
                .testCase("User Constraint Violation - Duplicate Email")
                .expectedResult("Status: 409, Email already exists")
                .actualResult("Status: 409, User <NAME_EMAIL> already exists")
                .status("PASS")
                .requestBody("{\"email\": \"<EMAIL>\", \"firstName\": \"John2\"}")
                .responseBody("{\"error\": \"Duplicate entry\", \"message\": \"User <NAME_EMAIL> already exists\"}")
                .statusCode(409)
                .build();
        
        filteredReporter.reportTestCaseWithColorCodingForService("Authentication", userUnique);
        
        log.info("✅ Authentication service User tests completed");
        log.info("📊 Authentication_Service_Test_Results sheet now contains ONLY User results (2 test cases)");
    }
    
    /**
     * Test Core service - ONLY State table (no Country table tests)
     * This demonstrates that Core_Service_Test_Results sheet will only show State results
     */
    @Test(description = "Test Core service with only State table")
    public void testCoreServiceOnlyState() {
        log.info("🧪 Testing Core service - ONLY State table");
        log.info("📋 This will create Core_Service_Test_Results sheet with ONLY State results");
        
        // State creation success
        TestCaseResult stateSuccess = TestCaseResult.builder()
                .tableName("Core.State")
                .operation("POST")
                .testCase("Create State - Texas")
                .expectedResult("Status: 201, State created successfully")
                .actualResult("Status: 201, State created with ID: 789")
                .status("PASS")
                .requestBody("{\"stateShortName\": \"TX\", \"stateName\": \"Texas\", \"countryId\": 1}")
                .responseBody("{\"id\": 789, \"stateShortName\": \"TX\", \"stateName\": \"Texas\"}")
                .statusCode(201)
                .build();
        
        filteredReporter.reportTestCaseWithColorCodingForService("Core", stateSuccess);
        
        log.info("✅ Core service State tests completed");
        log.info("📊 Core_Service_Test_Results sheet now contains ONLY State results (1 test case)");
    }
    
    /**
     * Test Product service - ONLY Product table (no Category table tests)
     * This demonstrates that Product_Service_Test_Results sheet will only show Product results
     */
    @Test(description = "Test Product service with only Product table")
    public void testProductServiceOnlyProduct() {
        log.info("🧪 Testing Product service - ONLY Product table");
        log.info("📋 This will create Product_Service_Test_Results sheet with ONLY Product results");
        
        // Product GET success
        TestCaseResult productGet = TestCaseResult.builder()
                .tableName("Product.Product")
                .operation("GET")
                .testCase("Get Product by ID - Wireless Headphones")
                .expectedResult("Status: 200, Product details returned")
                .actualResult("Status: 200, Product found with complete details")
                .status("PASS")
                .requestBody("")
                .responseBody("{\"id\": 1, \"name\": \"Wireless Headphones\", \"price\": 99.99}")
                .statusCode(200)
                .build();
        
        filteredReporter.reportTestCaseWithColorCodingForService("Product", productGet);
        
        // Product validation error
        TestCaseResult productValidation = TestCaseResult.builder()
                .tableName("Product.Product")
                .operation("POST")
                .testCase("Product Validation - Negative Price")
                .expectedResult("Status: 400, Price must be greater than 0")
                .actualResult("Status: 422, Invalid price value")
                .status("FAIL")
                .requestBody("{\"name\": \"Test Product\", \"price\": -10.50}")
                .responseBody("{\"error\": \"Validation error\", \"message\": \"Invalid price value\"}")
                .statusCode(422)
                .errorMessage("Expected 400 but got 422")
                .build();
        
        filteredReporter.reportTestCaseWithColorCodingForService("Product", productValidation);
        
        log.info("✅ Product service Product tests completed");
        log.info("📊 Product_Service_Test_Results sheet now contains ONLY Product results (2 test cases)");
    }
    
    /**
     * Filtered service-specific Excel reporting test summary
     */
    @Test(description = "Filtered service-specific Excel reporting test summary", 
          dependsOnMethods = {"testOrderServiceOnlyBundleProducts", "testAuthenticationServiceOnlyUser", 
                             "testCoreServiceOnlyState", "testProductServiceOnlyProduct"})
    public void filteredServiceSpecificExcelReportingTestSummary() {
        log.info("📊 FILTERED SERVICE-SPECIFIC EXCEL REPORTING TEST SUMMARY");
        log.info("=========================================================");
        
        int totalTestCases = filteredReporter.getTotalTestCases();
        int totalDefects = filteredReporter.getTotalDefects();
        String reportFile = filteredReporter.getReportFilePath();
        
        log.info("📋 Total Test Cases Reported: {}", totalTestCases);
        log.info("🐛 Total Defects Generated: {}", totalDefects);
        log.info("📁 Excel Report File: {}", reportFile);
        
        // Check if report file exists
        File reportFileObj = new File(reportFile);
        if (reportFileObj.exists()) {
            log.info("✅ Filtered service-specific Excel report file created successfully");
            log.info("📊 File size: {} bytes", reportFileObj.length());
        } else {
            log.error("❌ Filtered service-specific Excel report file not found!");
        }
        
        log.info("");
        log.info("🏢 SERVICES TESTED AND THEIR FILTERED RESULTS:");
        for (String service : filteredReporter.getTestedServices()) {
            int serviceTestCases = filteredReporter.getTestCasesForService(service);
            int serviceDefects = filteredReporter.getDefectsForService(service);
            log.info("   📋 {}: {} test cases, {} defects", service, serviceTestCases, serviceDefects);
        }
        
        log.info("");
        log.info("📊 FILTERED SERVICE-SPECIFIC EXCEL SHEETS CREATED:");
        log.info("1. 📦 Order_Service_Test_Results - ONLY bundle_products table results (3 test cases, 1 defect)");
        log.info("2. 👤 Authentication_Service_Test_Results - ONLY User table results (2 test cases, 0 defects)");
        log.info("3. 🌐 Core_Service_Test_Results - ONLY State table results (1 test case, 0 defects)");
        log.info("4. 📦 Product_Service_Test_Results - ONLY Product table results (2 test cases, 1 defect)");
        
        log.info("");
        log.info("🎯 FILTERED SERVICE-SPECIFIC FEATURES DEMONSTRATED:");
        log.info("✅ One Excel sheet per service");
        log.info("✅ Each sheet shows ONLY the tables that were actually tested");
        log.info("✅ No empty rows or irrelevant table results");
        log.info("✅ Service-specific test case IDs with table info");
        log.info("✅ Service-specific defect IDs with table info");
        log.info("✅ Independent counters for each service");
        log.info("✅ Color coding within each service sheet");
        log.info("✅ Complete test data recording per service");
        
        log.info("");
        log.info("📋 EXCEL REPORT STRUCTURE:");
        log.info("File: Filtered_Service_Test_Execution_Report.xlsx");
        log.info("├── Order_Service_Test_Results");
        log.info("│   ├── TC_Order_bundle_products_POST_001 (PASS)");
        log.info("│   ├── TC_Order_bundle_products_POST_002 (PASS)");
        log.info("│   └── TC_Order_bundle_products_PUT_003 (FAIL) - D_Order_bundle_products_PUT_001");
        log.info("├── Authentication_Service_Test_Results");
        log.info("│   ├── TC_Authentication_User_POST_001 (PASS)");
        log.info("│   └── TC_Authentication_User_POST_002 (PASS)");
        log.info("├── Core_Service_Test_Results");
        log.info("│   └── TC_Core_State_POST_001 (PASS)");
        log.info("└── Product_Service_Test_Results");
        log.info("    ├── TC_Product_Product_GET_001 (PASS)");
        log.info("    └── TC_Product_Product_POST_002 (FAIL) - D_Product_Product_POST_001");
        
        log.info("");
        log.info("🚀 BENEFITS OF FILTERED SERVICE-SPECIFIC REPORTING:");
        log.info("1. 🎯 Clean service sheets with only relevant table results");
        log.info("2. 📊 Easy service-specific analysis without clutter");
        log.info("3. 🔍 Focus on tables you're actually testing");
        log.info("4. 📋 Service teams see only their tested tables");
        log.info("5. 🎛️ No empty or irrelevant rows in Excel sheets");
        log.info("6. 📈 Accurate service-specific metrics");
        log.info("7. 🔄 Dynamic - only creates content for tested tables");
        
        log.info("");
        log.info("🎯 PERFECT SOLUTION FOR YOUR BUNDLE PRODUCTS TESTING:");
        log.info("✅ When you test Order service with bundle_products table:");
        log.info("   📊 Order_Service_Test_Results sheet shows ONLY bundle_products results");
        log.info("   🔢 Test case IDs: TC_Order_bundle_products_POST_001, TC_Order_bundle_products_PUT_002");
        log.info("   🐛 Defect IDs: D_Order_bundle_products_PUT_001");
        log.info("   📋 No Order table results cluttering the sheet");
        log.info("   🎯 Clean, focused reporting for bundle_products testing");
        
        log.info("");
        log.info("📋 HOW TO VIEW FILTERED SERVICE-SPECIFIC RESULTS:");
        log.info("1. Open Excel file: {}", reportFile);
        log.info("2. Click on service-specific sheet tabs (e.g., Order_Service_Test_Results)");
        log.info("3. Each sheet contains only the tables you actually tested");
        log.info("4. Review color-coded results (Green=PASS, Red=FAIL)");
        log.info("5. Check service-specific defect IDs for failures");
        
        log.info("");
        log.info("✅ FILTERED SERVICE-SPECIFIC EXCEL REPORTING TEST COMPLETED SUCCESSFULLY!");
        log.info("📊 {} services tested with {} total test cases and {} defects!", 
                filteredReporter.getTestedServices().size(), totalTestCases, totalDefects);
        log.info("🎯 Each service sheet shows ONLY the tables that were actually tested!");
    }
}
