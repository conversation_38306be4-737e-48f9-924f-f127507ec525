package com.rbts.tests;

import com.rbts.config.ExcelConfigManager;
import com.rbts.config.ExcelConfigTemplateGenerator;
import com.rbts.config.FieldMapping;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

/**
 * Complete framework test including field mapping functionality
 * Tests all features: getAll column, null endpoint skipping, constraint validation, and field mapping
 */
@Slf4j
public class CompleteFrameworkWithFieldMappingTest {
    
    private ExcelConfigManager configManager;
    private ExcelConfigTemplateGenerator templateGenerator;
    
    @BeforeClass
    public void setUp() {
        templateGenerator = new ExcelConfigTemplateGenerator();
        log.info("Complete Framework with Field Mapping Test initialized");
    }
    
    /**
     * Test complete framework functionality including field mapping
     */
    @Test(description = "Test complete framework with field mapping", priority = 1)
    public void testCompleteFrameworkWithFieldMapping() {
        log.info("Testing complete framework functionality with field mapping");
        
        try {
            // Generate Excel template
            templateGenerator.generateConfigurationTemplate();
            
            // Initialize config manager
            configManager = ExcelConfigManager.getInstance();
            
            log.info("=== TESTING ALL FRAMEWORK FEATURES ===");
            
            // 1. Test getAll column functionality
            testGetAllColumnFunctionality();
            
            // 2. Test null endpoint skipping
            testNullEndpointSkipping();
            
            // 3. Test constraint validation status codes
            testConstraintValidationStatusCodes();
            
            // 4. Test service-based foreign key validation
            testServiceBasedForeignKeyValidation();
            
            // 5. Test field mapping functionality
            testFieldMappingFunctionality();
            
            // 6. Test integrated field mapping with operations
            testIntegratedFieldMappingWithOperations();
            
            // 7. Test complete configuration summary
            testCompleteConfigurationSummary();
            
            log.info("=== ALL FRAMEWORK FEATURES WITH FIELD MAPPING WORKING PERFECTLY ===");
            
        } catch (Exception e) {
            log.error("❌ Error testing complete framework with field mapping: {}", e.getMessage());
            Assert.fail("Complete framework with field mapping test failed: " + e.getMessage());
        }
    }
    
    private void testGetAllColumnFunctionality() {
        log.info("🔍 Testing getAll column functionality...");
        
        // Test that getAll is included in operations
        String operations = configManager.getGeneralConfig("auto.test.operations");
        Assert.assertTrue(operations.contains("getall"), "Operations should include getall");
        
        // Test getAll validation status code
        String getAllStatus = configManager.getValidationConfig("validation.status_code.getall");
        Assert.assertEquals(getAllStatus, "200", "getAll status code should be 200");
        
        // Test getAll endpoint for a table
        String getAllEndpoint = configManager.getTableEndpoint("AddressType", "getall");
        Assert.assertNotNull(getAllEndpoint, "getAll endpoint should be available");
        Assert.assertTrue(getAllEndpoint.contains("getAll"), "getAll endpoint should contain 'getAll'");
        
        log.info("✅ getAll column functionality working correctly");
    }
    
    private void testNullEndpointSkipping() {
        log.info("🔍 Testing null endpoint skipping...");
        
        // Test ExampleTable which has null endpoints
        List<String> operationsToTest = configManager.getOperationsToTestForTable("ExampleTable");
        
        // Should include operations with endpoints
        Assert.assertTrue(operationsToTest.contains("post"), "POST should be tested");
        Assert.assertTrue(operationsToTest.contains("get"), "GET should be tested");
        Assert.assertTrue(operationsToTest.contains("getall"), "GET ALL should be tested");
        
        // Should skip operations with null/empty endpoints
        Assert.assertFalse(operationsToTest.contains("put"), "PUT should be skipped (empty endpoint)");
        Assert.assertFalse(operationsToTest.contains("patch"), "PATCH should be skipped (null endpoint)");
        Assert.assertFalse(operationsToTest.contains("delete"), "DELETE should be skipped (empty endpoint)");
        
        log.info("✅ Null endpoint skipping working correctly");
        log.info("   Operations to test for ExampleTable: {}", operationsToTest);
    }
    
    private void testConstraintValidationStatusCodes() {
        log.info("🔍 Testing constraint validation status codes...");
        
        // Test specific constraint status codes
        String uniqueStatus = configManager.getValidationConfig("validation.unique_constraint.expected_status");
        Assert.assertEquals(uniqueStatus, "701", "Unique constraint should return 701");
        
        String nullStatus = configManager.getValidationConfig("validation.null_constraint.expected_status");
        Assert.assertEquals(nullStatus, "700", "Null constraint should return 700");
        
        String foreignKeySameStatus = configManager.getValidationConfig("validation.foreign_key_same_service.expected_status");
        Assert.assertEquals(foreignKeySameStatus, "404", "Foreign key same service should return 404");
        
        String foreignKeyOtherStatus = configManager.getValidationConfig("validation.foreign_key_other_service.expected_status");
        Assert.assertEquals(foreignKeyOtherStatus, "702", "Foreign key other service should return 702");
        
        log.info("✅ Constraint validation status codes working correctly");
        log.info("   Unique: {}, Null: {}, FK Same: {}, FK Other: {}", 
                uniqueStatus, nullStatus, foreignKeySameStatus, foreignKeyOtherStatus);
    }
    
    private void testServiceBasedForeignKeyValidation() {
        log.info("🔍 Testing service-based foreign key validation...");
        
        // Test same service foreign key (Order -> OrderStatus)
        String sameServiceStatus = configManager.getConstraintViolationStatusCodeForTable("foreign_key_invalid", "Order", "OrderStatus");
        Assert.assertEquals(sameServiceStatus, "404", "Same service FK should return 404");
        
        // Test different service foreign key (Order -> User)
        String differentServiceStatus = configManager.getConstraintViolationStatusCodeForTable("foreign_key_invalid", "Order", "User");
        Assert.assertEquals(differentServiceStatus, "702", "Different service FK should return 702");
        
        log.info("✅ Service-based foreign key validation working correctly");
        log.info("   Order -> OrderStatus (same service): {}", sameServiceStatus);
        log.info("   Order -> User (different service): {}", differentServiceStatus);
    }
    
    private void testFieldMappingFunctionality() {
        log.info("🔍 Testing field mapping functionality...");
        
        // Test User table field mappings
        FieldMapping userIdMapping = configManager.getFieldMapping("User", "user_id");
        Assert.assertNotNull(userIdMapping, "user_id field mapping should exist");
        Assert.assertEquals(userIdMapping.getApiRequestField(), "userId", "API request field should be userId");
        Assert.assertEquals(userIdMapping.getApiResponseField(), "id", "API response field should be id");
        Assert.assertTrue(userIdMapping.isPrimaryKey(), "user_id should be identified as primary key");
        
        // Test Order table field mappings
        FieldMapping orderUserIdMapping = configManager.getFieldMapping("Order", "user_id");
        Assert.assertNotNull(orderUserIdMapping, "Order.user_id field mapping should exist");
        Assert.assertEquals(orderUserIdMapping.getApiRequestField(), "customerId", "API request field should be customerId");
        Assert.assertEquals(orderUserIdMapping.getApiResponseField(), "userId", "API response field should be userId");
        Assert.assertTrue(orderUserIdMapping.isForeignKey(), "Order.user_id should be identified as foreign key");
        
        // Test email field mapping
        FieldMapping emailMapping = configManager.getFieldMapping("User", "email_address");
        Assert.assertNotNull(emailMapping, "email_address field mapping should exist");
        Assert.assertTrue(emailMapping.isEmailField(), "email_address should be identified as email field");
        
        log.info("✅ Field mapping functionality working correctly");
    }
    
    private void testIntegratedFieldMappingWithOperations() {
        log.info("🔍 Testing integrated field mapping with operations...");
        
        // Test API request field name retrieval
        String userIdRequestField = configManager.getApiRequestFieldName("User", "user_id");
        Assert.assertEquals(userIdRequestField, "userId", "API request field name should be userId");
        
        // Test API response field name retrieval
        String userIdResponseField = configManager.getApiResponseFieldName("User", "user_id");
        Assert.assertEquals(userIdResponseField, "id", "API response field name should be id");
        
        // Test database field name retrieval from API request field
        String dbFieldFromRequest = configManager.getDatabaseFieldNameFromRequest("User", "userId");
        Assert.assertEquals(dbFieldFromRequest, "user_id", "Database field name should be user_id");
        
        // Test database field name retrieval from API response field
        String dbFieldFromResponse = configManager.getDatabaseFieldNameFromResponse("User", "id");
        Assert.assertEquals(dbFieldFromResponse, "user_id", "Database field name should be user_id");
        
        // Test primary key fields retrieval
        List<String> userPrimaryKeys = configManager.getPrimaryKeyFields("User");
        Assert.assertTrue(userPrimaryKeys.contains("user_id"), "User primary keys should contain user_id");
        
        // Test foreign key fields retrieval
        List<String> orderForeignKeys = configManager.getForeignKeyFields("Order");
        Assert.assertTrue(orderForeignKeys.contains("user_id"), "Order foreign keys should contain user_id");
        
        log.info("✅ Integrated field mapping with operations working correctly");
    }
    
    private void testCompleteConfigurationSummary() {
        log.info("🔍 Testing complete configuration summary...");
        
        // Print complete configuration summary
        configManager.printConfigurationSummary();
        
        // Verify configuration counts
        Assert.assertTrue(configManager.getAllConfiguredTables().size() > 0, "Should have configured tables");
        Assert.assertTrue(configManager.getServiceNames().size() > 0, "Should have configured services");
        
        // Verify field mapping configurations
        Map<String, FieldMapping> userMappings = configManager.getFieldMappingsForTable("User");
        Assert.assertTrue(userMappings.size() > 0, "Should have User field mappings");
        
        Map<String, FieldMapping> orderMappings = configManager.getFieldMappingsForTable("Order");
        Assert.assertTrue(orderMappings.size() > 0, "Should have Order field mappings");
        
        log.info("✅ Complete configuration summary working correctly");
    }
    
    /**
     * Final demonstration and summary
     */
    @Test(description = "Complete framework demonstration with field mapping", dependsOnMethods = "testCompleteFrameworkWithFieldMapping", priority = 2)
    public void completeFrameworkDemonstrationWithFieldMapping() {
        log.info("=== COMPLETE FRAMEWORK WITH FIELD MAPPING TEST SUMMARY ===");
        log.info("✅ getAll column functionality - PASSED");
        log.info("✅ Null endpoint skipping - PASSED");
        log.info("✅ Constraint validation status codes - PASSED");
        log.info("✅ Service-based foreign key validation - PASSED");
        log.info("✅ Field mapping functionality - PASSED");
        log.info("✅ Integrated field mapping with operations - PASSED");
        log.info("✅ Complete configuration summary - PASSED");
        log.info("=== ALL COMPLETE FRAMEWORK TESTS WITH FIELD MAPPING PASSED ===");
        
        log.info("");
        log.info("🎉 COMPLETE FRAMEWORK FEATURES SUMMARY:");
        log.info("");
        
        log.info("📊 1. ENHANCED TABLE ENDPOINTS:");
        log.info("   ✅ Added getAll column to Table_Endpoints sheet");
        log.info("   ✅ Support for 6 CRUD operations: POST, PUT, PATCH, GET, GET ALL, DELETE");
        log.info("   ✅ Null/empty endpoint skipping for fine-grained control");
        log.info("");
        
        log.info("🎯 2. SMART OPERATION SKIPPING:");
        log.info("   ✅ Leave endpoint cell empty → Skip testing that operation");
        log.info("   ✅ Enter 'null' → Skip testing that operation");
        log.info("   ✅ Enter endpoint → Test that operation");
        log.info("   ✅ No table configuration → Test all operations (defaults)");
        log.info("");
        
        log.info("🔴 3. CONSTRAINT VALIDATION STATUS CODES:");
        log.info("   ✅ Unique Constraint Violation: 701");
        log.info("   ✅ Null Constraint Violation: 700");
        log.info("   ✅ Foreign Key Same Service: 404");
        log.info("   ✅ Foreign Key Other Service: 702");
        log.info("");
        
        log.info("🏗️ 4. SERVICE-AWARE VALIDATION:");
        log.info("   ✅ Automatic service detection for each table");
        log.info("   ✅ Smart foreign key validation based on service relationship");
        log.info("   ✅ Different status codes for same vs different service FK violations");
        log.info("");
        
        log.info("🗺️ 5. FIELD MAPPING FUNCTIONALITY:");
        log.info("   ✅ Database field names mapped to API request field names");
        log.info("   ✅ Database field names mapped to API response field names");
        log.info("   ✅ Field type identification (PRIMARY_KEY, FOREIGN_KEY, EMAIL, etc.)");
        log.info("   ✅ Automatic fallback to original field names when mapping not found");
        log.info("   ✅ Easy Excel-based field mapping configuration");
        log.info("");
        
        log.info("📋 6. EXCEL-BASED CONFIGURATION:");
        log.info("   ✅ Easy configuration through Excel sheets");
        log.info("   ✅ No code changes required for configuration updates");
        log.info("   ✅ Visual and intuitive configuration management");
        log.info("   ✅ Comprehensive validation rules and endpoint management");
        log.info("   ✅ Field mapping for accurate database-API field matching");
        log.info("");
        
        log.info("🚀 7. REAL-WORLD FIELD MAPPING EXAMPLES:");
        log.info("");
        log.info("   📝 Example 1 - User Table Field Mapping:");
        log.info("      Database: user_id → API Request: userId → API Response: id");
        log.info("      Database: email_address → API Request: email → API Response: emailAddress");
        log.info("      Database: created_date → API Request: createdDate → API Response: createdAt");
        log.info("");
        
        log.info("   📝 Example 2 - Order Table Field Mapping:");
        log.info("      Database: order_id → API Request: orderId → API Response: id");
        log.info("      Database: user_id → API Request: customerId → API Response: userId");
        log.info("      Database: order_total → API Request: totalAmount → API Response: total");
        log.info("");
        
        log.info("🎯 NEXT STEPS:");
        log.info("1. Open config/Framework_Configuration.xlsx");
        log.info("2. Configure your actual endpoints and validation rules");
        log.info("3. Set null/empty endpoints for operations you don't want to test");
        log.info("4. Configure field mappings in Field_Mapping sheet");
        log.info("5. Run automated tests with your configuration");
        log.info("6. Framework will automatically handle all the smart validation and field mapping!");
        
        Assert.assertTrue(true, "All complete framework tests with field mapping passed successfully");
    }
}
