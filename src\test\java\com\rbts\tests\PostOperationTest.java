package com.rbts.tests;

import com.rbts.crud.CrudTestExecutor;
import com.rbts.crud.TestResult;
import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

/**
 * POST Operation Test Class
 * Demonstrates how to use the CRUD Testing Framework for POST operations
 */
@Slf4j
public class PostOperationTest {
    
    private CrudTestExecutor crudTestExecutor;
    private ExcelUtils excelUtils;
    
    // Excel file configuration
    private static final String EXCEL_FILE_PATH = "data/test_data.xlsx";
    private static final String SHEET_NAME = "POST_Tests";
    
    // Column indices in Excel (1-based)
    private static final int URL_COLUMN = 1;
    private static final int REQUEST_BODY_COLUMN = 2;
    private static final int EXPECTED_RESULT_COLUMN = 3;
    private static final int ACTUAL_RESULT_COLUMN = 4;
    private static final int STATUS_COLUMN = 5;
    
    @BeforeClass
    public void setUp() {
        crudTestExecutor = new CrudTestExecutor();
        excelUtils = new ExcelUtils();
        log.info("POST Operation Test setup completed");
    }
    
    /**
     * Data provider for normal POST tests
     */
    @DataProvider(name = "postTestData")
    public Object[][] getPostTestData() {
        int rowCount = excelUtils.getRowCount(EXCEL_FILE_PATH, SHEET_NAME);
        Object[][] data = new Object[rowCount - 1][2]; // Exclude header row
        
        for (int i = 2; i <= rowCount; i++) { // Start from row 2 (skip header)
            String entity = excelUtils.getCellData(EXCEL_FILE_PATH, SHEET_NAME, i, 6); // Entity column
            data[i - 2] = new Object[]{entity, i};
        }
        
        return data;
    }
    
    /**
     * Test normal POST operations
     */
    @Test(dataProvider = "postTestData", description = "Test POST operations with database validation")
    public void testPostOperation(String entity, int rowNum) {
        log.info("Starting POST test for entity: {} at row: {}", entity, rowNum);
        
        TestResult result = crudTestExecutor.executePostTest(
            entity, 
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            rowNum,
            URL_COLUMN,
            REQUEST_BODY_COLUMN,
            EXPECTED_RESULT_COLUMN,
            ACTUAL_RESULT_COLUMN,
            STATUS_COLUMN
        );
        
        log.info("POST test completed: {}", result.getSummary());
        
        // Assert test passed
        Assert.assertTrue(result.isTestPassed(), 
            String.format("POST test failed for entity %s: %s", entity, result.getComparisonMessage()));
        
        // Log detailed result if test failed
        if (!result.isTestPassed()) {
            log.error("POST test failed details:\n{}", result.getDetailedResult());
        }
    }
    
    /**
     * Test POST operations for User entity specifically
     */
    @Test(description = "Test POST operation for User entity")
    public void testPostUser() {
        String entity = "user";
        String requestBody = "{\n" +
            "  \"username\": \"testuser123\",\n" +
            "  \"email\": \"<EMAIL>\",\n" +
            "  \"firstName\": \"Test\",\n" +
            "  \"lastName\": \"User\",\n" +
            "  \"phone\": \"+1234567890\"\n" +
            "}";
        
        TestResult result = crudTestExecutor.executePostTest(
            entity, 
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            2, // Assuming row 2 for user test
            URL_COLUMN,
            REQUEST_BODY_COLUMN,
            EXPECTED_RESULT_COLUMN,
            ACTUAL_RESULT_COLUMN,
            STATUS_COLUMN
        );
        
        log.info("User POST test result: {}", result.getSummary());
        Assert.assertTrue(result.isTestPassed(), "User POST test should pass");
    }
    
    /**
     * Test POST operations for Product entity specifically
     */
    @Test(description = "Test POST operation for Product entity")
    public void testPostProduct() {
        String entity = "product";
        String requestBody = "{\n" +
            "  \"product_name\": \"Test Product\",\n" +
            "  \"product_code\": \"TP001\",\n" +
            "  \"price\": 99.99,\n" +
            "  \"description\": \"Test product description\",\n" +
            "  \"country_id\": 1\n" +
            "}";
        
        TestResult result = crudTestExecutor.executePostTest(
            entity, 
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            3, // Assuming row 3 for product test
            URL_COLUMN,
            REQUEST_BODY_COLUMN,
            EXPECTED_RESULT_COLUMN,
            ACTUAL_RESULT_COLUMN,
            STATUS_COLUMN
        );
        
        log.info("Product POST test result: {}", result.getSummary());
        Assert.assertTrue(result.isTestPassed(), "Product POST test should pass");
    }
    
    /**
     * Test null constraint violation for User entity
     */
    @Test(description = "Test POST null constraint violation for User entity")
    public void testPostUserNullConstraintViolation() {
        String entity = "user";
        String baseRequestBody = "{\n" +
            "  \"username\": \"testuser456\",\n" +
            "  \"email\": \"<EMAIL>\",\n" +
            "  \"firstName\": \"Test\",\n" +
            "  \"lastName\": \"User\"\n" +
            "}";
        
        TestResult result = crudTestExecutor.executePostNullConstraintTest(
            entity, 
            baseRequestBody,
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            4, // Assuming row 4 for null constraint test
            STATUS_COLUMN
        );
        
        log.info("User null constraint test result: {}", result.getSummary());
        Assert.assertTrue(result.isTestPassed(), 
            "Null constraint violation should be correctly rejected by API");
    }
    
    /**
     * Test unique constraint violation for User entity
     */
    @Test(description = "Test POST unique constraint violation for User entity")
    public void testPostUserUniqueConstraintViolation() {
        String entity = "user";
        String baseRequestBody = "{\n" +
            "  \"username\": \"existinguser\",\n" +
            "  \"email\": \"<EMAIL>\",\n" +
            "  \"firstName\": \"Existing\",\n" +
            "  \"lastName\": \"User\"\n" +
            "}";
        
        TestResult result = crudTestExecutor.executePostUniqueConstraintTest(
            entity, 
            baseRequestBody,
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            5, // Assuming row 5 for unique constraint test
            STATUS_COLUMN
        );
        
        log.info("User unique constraint test result: {}", result.getSummary());
        Assert.assertTrue(result.isTestPassed(), 
            "Unique constraint violation should be correctly rejected by API");
    }
    
    /**
     * Test multiple entities in sequence
     */
    @Test(description = "Test POST operations for multiple entities")
    public void testPostMultipleEntities() {
        String[] entities = {"user", "country", "product", "order"};
        
        for (String entity : entities) {
            log.info("Testing POST operation for entity: {}", entity);
            
            // This would typically read from Excel, but for demo purposes using hardcoded row
            int rowNum = 2; // You would determine this based on your Excel structure
            
            TestResult result = crudTestExecutor.executePostTest(
                entity, 
                EXCEL_FILE_PATH, 
                SHEET_NAME, 
                rowNum,
                URL_COLUMN,
                REQUEST_BODY_COLUMN,
                EXPECTED_RESULT_COLUMN,
                ACTUAL_RESULT_COLUMN,
                STATUS_COLUMN
            );
            
            log.info("Entity {} POST test result: {}", entity, result.getSummary());
            
            // Soft assertion - log failures but continue with other entities
            if (!result.isTestPassed()) {
                log.error("POST test failed for entity {}: {}", entity, result.getComparisonMessage());
            }
        }
    }
}
