package com.rbts.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;

/**
 * Sample Data Generator for both API patterns
 * Generates sample request bodies for direct and proxy patterns
 */
@Slf4j
public class SampleDataGenerator {
    
    private final ObjectMapper objectMapper;
    
    public SampleDataGenerator() {
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Generate sample request body for field_config entity (Direct Pattern)
     * Example from your requirement
     */
    public String generateFieldConfigRequestBody() {
        try {
            ObjectNode requestBody = objectMapper.createObjectNode();
            
            // Create fieldType object
            ObjectNode fieldType = objectMapper.createObjectNode();
            fieldType.put("id", 18);
            fieldType.put("fieldTypeName", "otp");
            fieldType.put("fieldTypeDesc", "One-time password input");
            fieldType.put("displayName", "OTP");
            fieldType.put("helpText", "Enter one-time password");
            fieldType.put("isActive", false);
            
            // Create configType object
            ObjectNode configType = objectMapper.createObjectNode();
            configType.put("createdBy", "anonymousUser");
            configType.put("createdAt", "2025-05-09T10:13:14.085725");
            configType.put("modifiedBy", "anonymousUser");
            configType.put("modifiedAt", "2025-05-09T10:13:14.085725");
            configType.put("id", 101);
            configType.put("configTypeName", "string1");
            configType.put("configTypeDesc", "string");
            configType.put("displayName", "string");
            configType.put("additionalInfo", "string");
            configType.put("disclaimerText", "string");
            configType.put("placeholderText", "string");
            configType.put("isActive", true);
            
            // Add to main request body
            requestBody.set("fieldType", fieldType);
            requestBody.set("configType", configType);
            requestBody.put("configName", "12");
            requestBody.put("isActive", true);
            requestBody.put("valueType", "string2");
            
            return objectMapper.writeValueAsString(requestBody);
            
        } catch (Exception e) {
            log.error("Error generating field config request body: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Generate sample request body for user_permission entity (Proxy Pattern)
     * This will be the payload part of the proxy request
     */
    public String generateUserPermissionPayload() {
        try {
            ObjectNode payload = objectMapper.createObjectNode();
            
            // Create userId object
            ObjectNode userId = objectMapper.createObjectNode();
            userId.put("id", 10002);
            
            payload.set("userId", userId);
            payload.put("permissionKey", "ACCGR12");
            payload.put("isActive", true);
            
            return objectMapper.writeValueAsString(payload);
            
        } catch (Exception e) {
            log.error("Error generating user permission payload: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Generate complete proxy request body (as it would be sent to /decrypt endpoint)
     * Example from your requirement
     */
    public String generateCompleteProxyRequestBody() {
        try {
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("endpoint", "/authentication/api/user/user-permissions/save");
            requestBody.put("type", "post");
            requestBody.put("tenantId", "redberyl_redberyltech_com");
            requestBody.putNull("auth");
            
            // Create payload
            ObjectNode payload = objectMapper.createObjectNode();
            ObjectNode userId = objectMapper.createObjectNode();
            userId.put("id", 10002);
            
            payload.set("userId", userId);
            payload.put("permissionKey", "ACCGR12");
            payload.put("isActive", true);
            
            requestBody.set("payload", payload);
            
            return objectMapper.writeValueAsString(requestBody);
            
        } catch (Exception e) {
            log.error("Error generating complete proxy request body: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Generate sample field config with dynamic values
     */
    public String generateDynamicFieldConfigRequestBody(String configName, boolean isActive) {
        try {
            ObjectNode requestBody = objectMapper.createObjectNode();
            
            // Create fieldType object with dynamic values
            ObjectNode fieldType = objectMapper.createObjectNode();
            fieldType.put("id", 18 + (int)(Math.random() * 100));
            fieldType.put("fieldTypeName", "dynamic_field_" + System.currentTimeMillis());
            fieldType.put("fieldTypeDesc", "Dynamic field description");
            fieldType.put("displayName", "Dynamic Field");
            fieldType.put("helpText", "Enter dynamic field value");
            fieldType.put("isActive", isActive);
            
            // Create configType object with dynamic values
            ObjectNode configType = objectMapper.createObjectNode();
            configType.put("createdBy", "testUser");
            configType.put("createdAt", java.time.Instant.now().toString());
            configType.put("modifiedBy", "testUser");
            configType.put("modifiedAt", java.time.Instant.now().toString());
            configType.put("id", 101 + (int)(Math.random() * 100));
            configType.put("configTypeName", "dynamic_config_" + System.currentTimeMillis());
            configType.put("configTypeDesc", "Dynamic config description");
            configType.put("displayName", "Dynamic Config");
            configType.put("additionalInfo", "Additional info");
            configType.put("disclaimerText", "Disclaimer text");
            configType.put("placeholderText", "Placeholder text");
            configType.put("isActive", true);
            
            // Add to main request body
            requestBody.set("fieldType", fieldType);
            requestBody.set("configType", configType);
            requestBody.put("configName", configName);
            requestBody.put("isActive", isActive);
            requestBody.put("valueType", "string");
            
            return objectMapper.writeValueAsString(requestBody);
            
        } catch (Exception e) {
            log.error("Error generating dynamic field config request body: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Generate sample user permission payload with dynamic values
     */
    public String generateDynamicUserPermissionPayload(int userId, String permissionKey) {
        try {
            ObjectNode payload = objectMapper.createObjectNode();
            
            // Create userId object
            ObjectNode userIdObj = objectMapper.createObjectNode();
            userIdObj.put("id", userId);
            
            payload.set("userId", userIdObj);
            payload.put("permissionKey", permissionKey);
            payload.put("isActive", true);
            
            return objectMapper.writeValueAsString(payload);
            
        } catch (Exception e) {
            log.error("Error generating dynamic user permission payload: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Create test data for Excel file with both patterns
     */
    public void createMixedPatternTestData(String filePath, String sheetName) {
        ExcelUtils excelUtils = new ExcelUtils();
        
        // Create header row
        excelUtils.setCellData(filePath, sheetName, 1, 1, "URL");
        excelUtils.setCellData(filePath, sheetName, 1, 2, "Request Body");
        excelUtils.setCellData(filePath, sheetName, 1, 3, "Expected Result");
        excelUtils.setCellData(filePath, sheetName, 1, 4, "Actual Result");
        excelUtils.setCellData(filePath, sheetName, 1, 5, "Status");
        excelUtils.setCellData(filePath, sheetName, 1, 6, "Entity");
        excelUtils.setCellData(filePath, sheetName, 1, 7, "Pattern");
        
        int rowNum = 2;
        
        // Direct pattern test data (field_config)
        String fieldConfigBody = generateDynamicFieldConfigRequestBody("test_config_" + System.currentTimeMillis(), true);
        excelUtils.setCellData(filePath, sheetName, rowNum, 1, "/api/field-configs");
        excelUtils.setCellData(filePath, sheetName, rowNum, 2, fieldConfigBody);
        excelUtils.setCellData(filePath, sheetName, rowNum, 3, ""); // Expected result - to be filled
        excelUtils.setCellData(filePath, sheetName, rowNum, 4, ""); // Actual result - will be filled by test
        excelUtils.setCellData(filePath, sheetName, rowNum, 5, ""); // Status - will be filled by test
        excelUtils.setCellData(filePath, sheetName, rowNum, 6, "field_config");
        excelUtils.setCellData(filePath, sheetName, rowNum, 7, "direct");
        rowNum++;
        
        // Proxy pattern test data (user_permission)
        String userPermissionPayload = generateDynamicUserPermissionPayload(10002, "TEST_PERM_" + System.currentTimeMillis());
        excelUtils.setCellData(filePath, sheetName, rowNum, 1, "/decrypt");
        excelUtils.setCellData(filePath, sheetName, rowNum, 2, userPermissionPayload);
        excelUtils.setCellData(filePath, sheetName, rowNum, 3, ""); // Expected result - to be filled
        excelUtils.setCellData(filePath, sheetName, rowNum, 4, ""); // Actual result - will be filled by test
        excelUtils.setCellData(filePath, sheetName, rowNum, 5, ""); // Status - will be filled by test
        excelUtils.setCellData(filePath, sheetName, rowNum, 6, "user_permission");
        excelUtils.setCellData(filePath, sheetName, rowNum, 7, "proxy");
        
        log.info("Mixed pattern test data created in Excel file: {} sheet: {}", filePath, sheetName);
    }
    
    /**
     * Pretty print JSON for debugging
     */
    public String prettyPrintJson(String jsonString) {
        try {
            Object json = objectMapper.readValue(jsonString, Object.class);
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(json);
        } catch (Exception e) {
            log.warn("Could not pretty print JSON: {}", e.getMessage());
            return jsonString;
        }
    }
}
