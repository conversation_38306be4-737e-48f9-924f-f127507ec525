package com.rbts.config;

import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * Generator for service-specific comprehensive configuration sheets
 * Creates one sheet per service containing all configurations
 */
@Slf4j
public class ServiceSpecificConfigTemplateGenerator {
    
    private final ExcelUtils excelUtils;
    private final String templateFilePath;
    
    public ServiceSpecificConfigTemplateGenerator() {
        this.excelUtils = new ExcelUtils();
        this.templateFilePath = "config/Service_Specific_Configuration.xlsx";
    }
    
    /**
     * Generate service-specific configuration template
     */
    public void generateServiceSpecificConfigurationTemplate() {
        log.info("🚀 Generating Service-Specific Configuration Template: {}", templateFilePath);
        
        // Create config directory if it doesn't exist
        File configDir = new File("config");
        if (!configDir.exists()) {
            configDir.mkdirs();
        }
        
        try {
            // Generate common configuration sheets (shared across services)
            generateGeneralConfigSheet();
            generateStatusCodeConfigSheet();
            
            // Generate service-specific comprehensive sheets
            generateContactServiceConfigSheet();
            generateAuthenticationServiceConfigSheet();
            generateCoreServiceConfigSheet();
            generateOrderServiceConfigSheet();
            generateProductServiceConfigSheet();
            
            // Generate instructions sheet
            generateInstructionsSheet();
            
            log.info("✅ Service-Specific Configuration Template generated successfully: {}", templateFilePath);
            
        } catch (Exception e) {
            log.error("❌ Error generating service-specific configuration template: {}", e.getMessage());
            throw new RuntimeException("Failed to generate service-specific configuration template", e);
        }
    }
    
    /**
     * Generate General Config sheet (common for all services)
     */
    private void generateGeneralConfigSheet() {
        String sheetName = "General_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Configuration Key");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Configuration Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Description");
        
        // Configuration data
        int row = 2;
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "framework.name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Service-Specific CRUD Testing Framework");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Name of the testing framework");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "framework.version");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "2.0.0");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Version of the framework");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "base.url.direct");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "http://localhost:8071");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Base URL for direct API pattern");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "base.url.proxy");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "http://localhost:9762");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Base URL for proxy API pattern");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "database.url");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "**********************************************");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database connection URL");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "database.username");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "your_username");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database username");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "database.password");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "your_password");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "Database password");
    }
    
    /**
     * Generate Status Code Config sheet (common for all services)
     */
    private void generateStatusCodeConfigSheet() {
        String sheetName = "Status_Code_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Operation");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Success Status Codes");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Null Constraint Status Code");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "Unique Constraint Status Code");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "FK Same Service Status Code");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "FK Different Service Status Code");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Validation Error Status Code");
        
        // Status code data
        int row = 2;
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "POST");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "201,200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "700");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "701");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "702");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "400");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "PUT");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "700");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "701");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "702");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "400");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "700");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "701");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "702");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "400");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "GET");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "400");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "DELETE");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "200,204");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "404");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "400");
    }
    
    /**
     * Generate Contact Service comprehensive configuration sheet
     */
    private void generateContactServiceConfigSheet() {
        String sheetName = "Contact_Service_Config";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Field Name (DB)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Field Name (API Request)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "Field Name (API Response)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "Field Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "Request Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Response Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 8, "FK Same Service");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 9, "FK Different Service");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 10, "API Endpoints");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 11, "Enable Testing");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 12, "Operations to Test");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 13, "Constraint Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 14, "Expected Error Message");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 15, "Test Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 16, "Comments");
        
        // Sample data for AddressType table
        int row = 2;
        
        // AddressType - Primary Key
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "address_type_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "addressTypeId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "POST:/contact/api/AddressType/save,GET:/contact/api/AddressType/{id},PUT:/contact/api/AddressType/update");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "post,get,put");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Primary key field");
        row++;
        
        // AddressType - Type Name
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "type_name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "typeName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "STRING");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "null_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "Address type name cannot be null");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "null");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Required field - test null constraint");
        row++;
        
        // AddressType - Type Name Unique Constraint
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "type_name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "typeName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "STRING");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "unique_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "Address type name 'Home' already exists");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "Home");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Unique field - test duplicate constraint");
        row++;
        
        // AddressType - Success Message
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "AddressType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "success");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "Address type created successfully");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "valid_data");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Success message validation");
        row++;
        
        // ContactType table
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "ContactType");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "contact_type_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "contactTypeId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "POST:/contact/api/ContactType/save,GET:/contact/api/ContactType/{id}");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "false");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Disabled for this test cycle");
    }

    /**
     * Generate Authentication Service comprehensive configuration sheet
     */
    private void generateAuthenticationServiceConfigSheet() {
        String sheetName = "Authentication_Service_Config";
        log.info("Generating {} sheet", sheetName);

        // Headers (same as Contact service)
        generateServiceSheetHeaders(sheetName);

        int row = 2;

        // User table - Primary Key
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "user_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "userId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "POST:/authentication/api/User/save,GET:/authentication/api/User/{id},PUT:/authentication/api/User/update");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "post,get,put");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "User primary key");
        row++;

        // User - Email field with null constraint
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "email_address");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "email");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "emailAddress");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "EMAIL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "null_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "Email address is required");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "null");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Email cannot be null");
        row++;

        // User - Email field with unique constraint
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "User");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "email_address");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "email");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "emailAddress");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "EMAIL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "unique_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "Email address '{testValue}' is already registered");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "<EMAIL>");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Email must be unique");
        row++;

        // UserProfile - Foreign Key Same Service
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "UserProfile");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "user_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "userId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "userId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "FOREIGN_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "authentication");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "POST:/authentication/api/UserProfile/save");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "post");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "foreign_key_same_service");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "User with ID {testValue} not found");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "999");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "FK to User table in same service");
        row++;

        // UserProfile - Foreign Key Different Service
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "UserProfile");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "country_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "countryId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "countryId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "FOREIGN_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "core");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "foreign_key_different_service");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "Country service is unavailable or country ID {testValue} not found");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "999");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "FK to Country table in core service");
    }

    /**
     * Generate Core Service comprehensive configuration sheet
     */
    private void generateCoreServiceConfigSheet() {
        String sheetName = "Core_Service_Config";
        log.info("Generating {} sheet", sheetName);

        generateServiceSheetHeaders(sheetName);

        int row = 2;

        // State table with stateShortName constraints
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "State");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "state_short_name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "stateShortName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "shortName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "STRING");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "POST:/core/api/State/save,GET:/core/api/State/{id}");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "post,get");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "null_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "State short name cannot be null");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "null");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "State short name is required");
        row++;

        // State - Unique constraint
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "State");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "state_short_name");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "stateShortName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "shortName");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "STRING");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "unique_constraint");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "State short name '{testValue}' already exists");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "CA");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "State short name must be unique");
        row++;

        // Country table
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Country");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "country_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "countryId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "POST:/core/api/Country/save,GET:/core/api/Country/{id}");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "false");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Service disabled for this test cycle");
    }

    /**
     * Generate Order Service comprehensive configuration sheet
     */
    private void generateOrderServiceConfigSheet() {
        String sheetName = "Order_Service_Config";
        log.info("Generating {} sheet", sheetName);

        generateServiceSheetHeaders(sheetName);

        int row = 2;

        // Order table
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Order");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "order_id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "orderId");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "POST:/order/api/Order/save,GET:/order/api/Order/{id},PUT:/order/api/Order/update,DELETE:/order/api/Order/{id}");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "post,get,put,delete");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "success");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "Order created successfully with ID {orderId}");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "valid_data");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Complete CRUD operations enabled");
    }

    /**
     * Generate Product Service comprehensive configuration sheet
     */
    private void generateProductServiceConfigSheet() {
        String sheetName = "Product_Service_Config";
        log.info("Generating {} sheet", sheetName);

        generateServiceSheetHeaders(sheetName);

        int row = 2;

        // Product table with price validation
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Product");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "price");
        excelUtils.setCellData(templateFilePath, sheetName, row, 3, "price");
        excelUtils.setCellData(templateFilePath, sheetName, row, 4, "price");
        excelUtils.setCellData(templateFilePath, sheetName, row, 5, "DECIMAL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(templateFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(templateFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(templateFilePath, sheetName, row, 10, "POST:/product/api/Product/save,GET:/product/api/Product/{id}");
        excelUtils.setCellData(templateFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(templateFilePath, sheetName, row, 12, "post,get");
        excelUtils.setCellData(templateFilePath, sheetName, row, 13, "validation_error");
        excelUtils.setCellData(templateFilePath, sheetName, row, 14, "Product price must be greater than 0");
        excelUtils.setCellData(templateFilePath, sheetName, row, 15, "-10.50");
        excelUtils.setCellData(templateFilePath, sheetName, row, 16, "Price validation - must be positive");
    }

    /**
     * Generate common headers for service sheets
     */
    private void generateServiceSheetHeaders(String sheetName) {
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Field Name (DB)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 3, "Field Name (API Request)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 4, "Field Name (API Response)");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 5, "Field Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 6, "Request Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 7, "Response Operations");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 8, "FK Same Service");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 9, "FK Different Service");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 10, "API Endpoints");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 11, "Enable Testing");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 12, "Operations to Test");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 13, "Constraint Type");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 14, "Expected Error Message");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 15, "Test Value");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 16, "Comments");
    }

    /**
     * Generate Instructions sheet
     */
    private void generateInstructionsSheet() {
        String sheetName = "Instructions";
        log.info("Generating {} sheet", sheetName);
        
        // Headers
        excelUtils.setCellData(templateFilePath, sheetName, 1, 1, "Section");
        excelUtils.setCellData(templateFilePath, sheetName, 1, 2, "Instructions");
        
        int row = 2;
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Overview");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Service-Specific Configuration: One comprehensive sheet per service containing all configurations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "General_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Common framework settings, database connection, and base URLs");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Status_Code_Config");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "HTTP status codes for different operations and constraint violations (shared across all services)");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Service Sheets");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Each service has one comprehensive sheet: Contact_Service_Config, Authentication_Service_Config, etc.");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Field Configuration");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Configure database fields, API request/response field names, field types, and operations");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "API Endpoints");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Configure API endpoints in format: POST:/service/api/Table/save,GET:/service/api/Table/{id}");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Test Control");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Enable/disable testing per table and specify operations to test");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Error Messages");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "Configure expected error messages for constraint violations and success messages");
        row++;
        
        excelUtils.setCellData(templateFilePath, sheetName, row, 1, "Benefits");
        excelUtils.setCellData(templateFilePath, sheetName, row, 2, "One sheet per service = easier maintenance, better organization, scalable as services increase");
    }
}
