package com.rbts.config;

import com.rbts.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * Generator for Order Service specific configuration
 * Creates comprehensive configuration for Order service only
 */
@Slf4j
public class OrderServiceConfigGenerator {

    private final ExcelUtils excelUtils;
    private final String configFilePath;

    public OrderServiceConfigGenerator() {
        this.excelUtils = new ExcelUtils();
        this.configFilePath = "config/Order_Service_Only_Configuration.xlsx";
    }

    /**
     * Generate Order service only configuration
     */
    public void generateOrderServiceConfiguration() {
        log.info("🚀 Generating Order Service Only Configuration: {}", configFilePath);

        // Create config directory if it doesn't exist
        File configDir = new File("config");
        if (!configDir.exists()) {
            configDir.mkdirs();
        }

        try {
            // Generate common configuration sheets
            generateGeneralConfigSheet();
            generateStatusCodeConfigSheet();

            // Generate Order service specific configuration
            generateOrderServiceConfigSheet();

            // Generate instructions
            generateInstructionsSheet();

            log.info("✅ Order Service Configuration generated successfully: {}", configFilePath);

        } catch (Exception e) {
            log.error("❌ Error generating Order service configuration: {}", e.getMessage());
            throw new RuntimeException("Failed to generate Order service configuration", e);
        }
    }

    /**
     * Generate General Config sheet
     */
    private void generateGeneralConfigSheet() {
        String sheetName = "General_Config";
        log.info("Generating {} sheet", sheetName);

        // Headers
        excelUtils.setCellData(configFilePath, sheetName, 1, 1, "Configuration Key");
        excelUtils.setCellData(configFilePath, sheetName, 1, 2, "Configuration Value");
        excelUtils.setCellData(configFilePath, sheetName, 1, 3, "Description");

        // Configuration data
        int row = 2;
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "framework.name");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "Order Service CRUD Testing Framework");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "Framework for testing Order service only");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "framework.version");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "1.0.0");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "Version of the Order service testing framework");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "base.url.direct");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "http://localhost:8080");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "Base URL for Order service API");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "order.service.token");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "order-service-bearer-token");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "Bearer token for Order service authentication");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "database.url");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "*****************************************");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "Order service database connection URL");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "database.username");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "order_user");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "Order service database username");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "database.password");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "order_password");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "Order service database password");
    }

    /**
     * Generate Status Code Config sheet
     */
    private void generateStatusCodeConfigSheet() {
        String sheetName = "Status_Code_Config";
        log.info("Generating {} sheet", sheetName);

        // Headers
        excelUtils.setCellData(configFilePath, sheetName, 1, 1, "Operation");
        excelUtils.setCellData(configFilePath, sheetName, 1, 2, "Success Status Codes");
        excelUtils.setCellData(configFilePath, sheetName, 1, 3, "Null Constraint Status Code");
        excelUtils.setCellData(configFilePath, sheetName, 1, 4, "Unique Constraint Status Code");
        excelUtils.setCellData(configFilePath, sheetName, 1, 5, "FK Same Service Status Code");
        excelUtils.setCellData(configFilePath, sheetName, 1, 6, "FK Different Service Status Code");
        excelUtils.setCellData(configFilePath, sheetName, 1, 7, "Validation Error Status Code");

        // Status code data for Order service
        int row = 2;
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "POST");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "201");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "400");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "409");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "502");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "400");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "PUT");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "400");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "409");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "502");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "400");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "GET");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "200");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "400");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "DELETE");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "204");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "404");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "400");
    }

    /**
     * Generate Order Service comprehensive configuration sheet
     */
    private void generateOrderServiceConfigSheet() {
        String sheetName = "Order_Service_Config";
        log.info("Generating {} sheet", sheetName);

        // Headers
        excelUtils.setCellData(configFilePath, sheetName, 1, 1, "Table Name");
        excelUtils.setCellData(configFilePath, sheetName, 1, 2, "Field Name (DB)");
        excelUtils.setCellData(configFilePath, sheetName, 1, 3, "Field Name (API Request)");
        excelUtils.setCellData(configFilePath, sheetName, 1, 4, "Field Name (API Response)");
        excelUtils.setCellData(configFilePath, sheetName, 1, 5, "Field Type");
        excelUtils.setCellData(configFilePath, sheetName, 1, 6, "Request Operations");
        excelUtils.setCellData(configFilePath, sheetName, 1, 7, "Response Operations");
        excelUtils.setCellData(configFilePath, sheetName, 1, 8, "FK Same Service");
        excelUtils.setCellData(configFilePath, sheetName, 1, 9, "FK Different Service");
        excelUtils.setCellData(configFilePath, sheetName, 1, 10, "API Endpoints");
        excelUtils.setCellData(configFilePath, sheetName, 1, 11, "Enable Testing");
        excelUtils.setCellData(configFilePath, sheetName, 1, 12, "Operations to Test");
        excelUtils.setCellData(configFilePath, sheetName, 1, 13, "Constraint Type");
        excelUtils.setCellData(configFilePath, sheetName, 1, 14, "Expected Error Message");
        excelUtils.setCellData(configFilePath, sheetName, 1, 15, "Test Value");
        excelUtils.setCellData(configFilePath, sheetName, 1, 16, "Comments");

        int row = 2;

        // Order table - Primary Key
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Order");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "order_id");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "orderId");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "PUT,PATCH,DELETE");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "POST:/api/orders,GET:/api/orders/{id},PUT:/api/orders/{id},DELETE:/api/orders/{id}");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "post,get,put,delete");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "Order primary key - auto-generated");
        row++;

        // Order - Customer ID (Foreign Key to Customer service)
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Order");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "customer_id");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "customerId");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "customerId");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "FOREIGN_KEY");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "customer");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "null_constraint");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "Customer ID is required");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "null");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "Customer ID cannot be null");
        row++;

        // Order - Customer ID (Foreign Key Different Service)
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Order");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "customer_id");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "customerId");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "customerId");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "FOREIGN_KEY");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "customer");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "foreign_key_different_service");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "Customer service unavailable or customer ID {testValue} not found");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "999");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "FK to Customer service");
        row++;

        // Order - Order Date
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Order");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "order_date");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "orderDate");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "orderDate");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "TIMESTAMP");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "null_constraint");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "Order date is required");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "null");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "Order date cannot be null");
        row++;

        // Order - Order Total (Calculated field)
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Order");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "order_total");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "total");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "DECIMAL");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "NONE");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "validation_error");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "Order total must be greater than 0");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "-10.50");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "Calculated field - system generated");
        row++;

        // Order - Order Status
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Order");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "order_status");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "status");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "status");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "STRING");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "POST,PATCH");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "validation_error");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "Invalid order status. Valid values: PENDING, CONFIRMED, SHIPPED, DELIVERED, CANCELLED");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "INVALID_STATUS");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "Order status validation");
        row++;

        // Order - Success Message
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Order");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "success");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "Order created successfully with ID {orderId}");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "valid_data");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "Success message for order creation");
        row++;

        // OrderItem table - Primary Key
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "OrderItem");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "order_item_id");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "orderItemId");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "id");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "PRIMARY_KEY");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "PUT,PATCH,DELETE");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "POST:/api/order-items,GET:/api/order-items/{id}");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "post,get");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "Order item primary key");
        row++;

        // OrderItem - Order ID (Foreign Key Same Service)
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "OrderItem");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "order_id");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "orderId");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "orderId");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "FOREIGN_KEY");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "order");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "foreign_key_same_service");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "Order with ID {testValue} not found");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "999");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "FK to Order table in same service");
        row++;

        // OrderItem - Product ID (Foreign Key Different Service)
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "OrderItem");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "product_id");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "productId");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "productId");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "FOREIGN_KEY");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "product");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "foreign_key_different_service");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "Product service unavailable or product ID {testValue} not found");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "999");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "FK to Product service");
        row++;

        // OrderItem - Quantity
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "OrderItem");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "quantity");
        excelUtils.setCellData(configFilePath, sheetName, row, 3, "quantity");
        excelUtils.setCellData(configFilePath, sheetName, row, 4, "quantity");
        excelUtils.setCellData(configFilePath, sheetName, row, 5, "INTEGER");
        excelUtils.setCellData(configFilePath, sheetName, row, 6, "POST,PUT,PATCH");
        excelUtils.setCellData(configFilePath, sheetName, row, 7, "ALL");
        excelUtils.setCellData(configFilePath, sheetName, row, 8, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 9, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 10, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 11, "true");
        excelUtils.setCellData(configFilePath, sheetName, row, 12, "");
        excelUtils.setCellData(configFilePath, sheetName, row, 13, "validation_error");
        excelUtils.setCellData(configFilePath, sheetName, row, 14, "Quantity must be greater than 0");
        excelUtils.setCellData(configFilePath, sheetName, row, 15, "0");
        excelUtils.setCellData(configFilePath, sheetName, row, 16, "Quantity validation");
    }

    /**
     * Generate Instructions sheet
     */
    private void generateInstructionsSheet() {
        String sheetName = "Instructions";
        log.info("Generating {} sheet", sheetName);

        // Headers
        excelUtils.setCellData(configFilePath, sheetName, 1, 1, "Section");
        excelUtils.setCellData(configFilePath, sheetName, 1, 2, "Instructions");

        int row = 2;
        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Overview");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "Order Service Only Configuration: Complete testing setup for Order service");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "General_Config");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "Order service specific settings: API URL, authentication, database connection");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Status_Code_Config");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "HTTP status codes for Order service operations and constraint violations");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Order_Service_Config");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "Complete Order service configuration: Order and OrderItem tables with all constraints");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "API Endpoints");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "Order API endpoints: POST:/api/orders, GET:/api/orders/{id}, PUT:/api/orders/{id}, DELETE:/api/orders/{id}");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Test Execution");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "Run OrderServiceOnlyTest class to test Order service with this configuration");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Constraints Tested");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "Null constraints, FK constraints (same/different service), validation errors, success messages");
        row++;

        excelUtils.setCellData(configFilePath, sheetName, row, 1, "Customization");
        excelUtils.setCellData(configFilePath, sheetName, row, 2, "Update API endpoints, error messages, and test values to match your Order service implementation");
    }
}