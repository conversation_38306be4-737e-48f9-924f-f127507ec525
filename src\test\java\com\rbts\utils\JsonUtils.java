package com.rbts.utils;

import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Iterator;

/**
 * JSON utility class for comparing JSON objects
 */
@Slf4j
public class JsonUtils {
    
    /**
     * Compare two JSON objects for equality
     */
    public boolean compareJsonObjects(JSONObject expected, JSONObject actual) {
        try {
            return compareJsonObjectsRecursive(expected, actual, "");
        } catch (Exception e) {
            log.error("Error comparing JSON objects: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Recursively compare JSON objects
     */
    private boolean compareJsonObjectsRecursive(JSONObject expected, JSONObject actual, String path) {
        // Check if both objects have the same keys
        if (expected.length() != actual.length()) {
            log.warn("JSON objects have different number of keys at path '{}': expected {}, actual {}", 
                    path, expected.length(), actual.length());
            return false;
        }
        
        // Iterate through expected keys
        Iterator<String> keys = expected.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            String currentPath = path.isEmpty() ? key : path + "." + key;
            
            if (!actual.has(key)) {
                log.warn("Missing key '{}' in actual JSON at path '{}'", key, currentPath);
                return false;
            }
            
            Object expectedValue = expected.get(key);
            Object actualValue = actual.get(key);
            
            if (!compareValues(expectedValue, actualValue, currentPath)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Compare individual values
     */
    private boolean compareValues(Object expected, Object actual, String path) {
        if (expected == null && actual == null) {
            return true;
        }
        
        if (expected == null || actual == null) {
            log.warn("Null mismatch at path '{}': expected {}, actual {}", path, expected, actual);
            return false;
        }
        
        // Handle JSONObject comparison
        if (expected instanceof JSONObject && actual instanceof JSONObject) {
            return compareJsonObjectsRecursive((JSONObject) expected, (JSONObject) actual, path);
        }
        
        // Handle different types
        if (!expected.getClass().equals(actual.getClass())) {
            // Try to convert and compare as strings for flexibility
            String expectedStr = expected.toString();
            String actualStr = actual.toString();
            
            if (!expectedStr.equals(actualStr)) {
                log.warn("Value mismatch at path '{}': expected '{}' ({}), actual '{}' ({})", 
                        path, expectedStr, expected.getClass().getSimpleName(), 
                        actualStr, actual.getClass().getSimpleName());
                return false;
            }
        } else {
            // Same types, direct comparison
            if (!expected.equals(actual)) {
                log.warn("Value mismatch at path '{}': expected '{}', actual '{}'", path, expected, actual);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Compare JSON objects with tolerance for specific fields
     */
    public boolean compareJsonObjectsWithTolerance(JSONObject expected, JSONObject actual, String... ignoreFields) {
        try {
            // Create copies to avoid modifying original objects
            JSONObject expectedCopy = new JSONObject(expected.toString());
            JSONObject actualCopy = new JSONObject(actual.toString());
            
            // Remove ignored fields
            for (String field : ignoreFields) {
                expectedCopy.remove(field);
                actualCopy.remove(field);
            }
            
            return compareJsonObjects(expectedCopy, actualCopy);
            
        } catch (Exception e) {
            log.error("Error comparing JSON objects with tolerance: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Get differences between two JSON objects
     */
    public String getJsonDifferences(JSONObject expected, JSONObject actual) {
        StringBuilder differences = new StringBuilder();
        
        try {
            findDifferences(expected, actual, "", differences);
        } catch (Exception e) {
            differences.append("Error finding differences: ").append(e.getMessage());
        }
        
        return differences.toString();
    }
    
    /**
     * Find and collect differences between JSON objects
     */
    private void findDifferences(JSONObject expected, JSONObject actual, String path, StringBuilder differences) {
        // Check for missing keys in actual
        Iterator<String> expectedKeys = expected.keys();
        while (expectedKeys.hasNext()) {
            String key = expectedKeys.next();
            String currentPath = path.isEmpty() ? key : path + "." + key;
            
            if (!actual.has(key)) {
                differences.append("Missing key: ").append(currentPath).append("\n");
                continue;
            }
            
            Object expectedValue = expected.get(key);
            Object actualValue = actual.get(key);
            
            if (expectedValue instanceof JSONObject && actualValue instanceof JSONObject) {
                findDifferences((JSONObject) expectedValue, (JSONObject) actualValue, currentPath, differences);
            } else if (!compareValues(expectedValue, actualValue, currentPath)) {
                differences.append("Value difference at ").append(currentPath)
                          .append(": expected '").append(expectedValue)
                          .append("', actual '").append(actualValue).append("'\n");
            }
        }
        
        // Check for extra keys in actual
        Iterator<String> actualKeys = actual.keys();
        while (actualKeys.hasNext()) {
            String key = actualKeys.next();
            if (!expected.has(key)) {
                String currentPath = path.isEmpty() ? key : path + "." + key;
                differences.append("Extra key: ").append(currentPath).append("\n");
            }
        }
    }
    
    /**
     * Normalize JSON object for comparison (sort keys, handle null values)
     */
    public JSONObject normalizeJsonObject(JSONObject jsonObject) {
        try {
            // Convert to string and back to normalize structure
            return new JSONObject(jsonObject.toString());
        } catch (Exception e) {
            log.error("Error normalizing JSON object: {}", e.getMessage());
            return jsonObject;
        }
    }
    
    /**
     * Check if JSON object contains all required fields
     */
    public boolean hasRequiredFields(JSONObject jsonObject, String... requiredFields) {
        for (String field : requiredFields) {
            if (!jsonObject.has(field)) {
                log.warn("Missing required field: {}", field);
                return false;
            }
        }
        return true;
    }
    
    /**
     * Extract value from JSON object by path (dot notation)
     */
    public Object getValueByPath(JSONObject jsonObject, String path) {
        try {
            String[] parts = path.split("\\.");
            Object current = jsonObject;
            
            for (String part : parts) {
                if (current instanceof JSONObject) {
                    current = ((JSONObject) current).get(part);
                } else {
                    return null;
                }
            }
            
            return current;
        } catch (Exception e) {
            log.error("Error extracting value by path '{}': {}", path, e.getMessage());
            return null;
        }
    }
    
    /**
     * Pretty print JSON object
     */
    public String prettyPrint(JSONObject jsonObject) {
        try {
            return jsonObject.toString(2);
        } catch (Exception e) {
            log.error("Error pretty printing JSON: {}", e.getMessage());
            return jsonObject.toString();
        }
    }
}
