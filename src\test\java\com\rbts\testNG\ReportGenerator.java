package com.rbts.testNG;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.reporter.ExtentHtmlReporter;
import org.testng.ITestContext;
import org.testng.ITestResult;
import org.testng.TestListenerAdapter;

public class ReportGenerator extends TestListenerAdapter {
    private ExtentHtmlReporter htmlReporter;
    private ExtentReports extentReports;

    private ExtentTest extentTest;

    public ReportGenerator() {

        String reportPath = System.getProperty("user.dir") +"/test_report.html";
        htmlReporter = new ExtentHtmlReporter(reportPath);
        extentReports = new ExtentReports();
        extentReports.attachReporter(htmlReporter);
    }

//    public ReportGenerator(String companyreport) {
//        String reportPath = System.getProperty("user.dir") +"/"+companyreport+".html";
//        htmlReporter = new ExtentHtmlReporter(reportPath);
//        extentReports = new ExtentReports();
//        extentReports.attachReporter(htmlReporter);
//
//    }



    @Override
    public void onStart(ITestContext testContext) {
        extentTest = extentReports.createTest(testContext.getName());
    }

    @Override
    public void onTestSuccess(ITestResult tr) {
        extentTest.log(Status.PASS, "Test passed");
    }

    @Override
    public void onTestFailure(ITestResult tr) {
        extentTest.log(Status.FAIL, "Test failed");
    }

    @Override
    public void onTestSkipped(ITestResult tr) {
        extentTest.log(Status.SKIP, "Test skipped");
    }

    @Override
    public void onFinish(ITestContext testContext) {
        extentReports.flush();
    }
}

