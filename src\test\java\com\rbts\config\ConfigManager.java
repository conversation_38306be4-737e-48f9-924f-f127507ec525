package com.rbts.config;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * Configuration Manager for CRUD Testing Framework
 * Handles all configuration properties including API endpoints, database mappings, and constraints
 */
@Slf4j
public class ConfigManager {
    
    private static ConfigManager instance;
    private Properties properties;
    private static final String CONFIG_FILE = "config.properties";
    
    private ConfigManager() {
        loadProperties();
    }
    
    public static ConfigManager getInstance() {
        if (instance == null) {
            synchronized (ConfigManager.class) {
                if (instance == null) {
                    instance = new ConfigManager();
                }
            }
        }
        return instance;
    }
    
    private void loadProperties() {
        properties = new Properties();
        try (InputStream input = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (input == null) {
                throw new RuntimeException("Unable to find " + CONFIG_FILE);
            }
            properties.load(input);
            log.info("Configuration loaded successfully from {}", CONFIG_FILE);
        } catch (IOException ex) {
            log.error("Error loading configuration: {}", ex.getMessage());
            throw new RuntimeException("Failed to load configuration", ex);
        }
    }
    
    // Database Configuration
    public String getJdbcUrl() {
        return properties.getProperty("JDBC_URL");
    }
    
    public String getJdbcUser() {
        return properties.getProperty("JDBC_USER");
    }
    
    public String getJdbcPassword() {
        return properties.getProperty("JDBC_PASSWORD");
    }
    
    public int getJdbcConnectionTimeout() {
        return Integer.parseInt(properties.getProperty("JDBC_CONNECTION_TIMEOUT", "10"));
    }
    
    // Base URL Configuration
    public String getBaseUrl() {
        return properties.getProperty("baseURI_Qa");
    }
    
    // API Endpoint Configuration
    public String getApiEndpoint(String operation, String entity) {
        String key = String.format("api.%s.%s", operation.toLowerCase(), entity.toLowerCase());
        return properties.getProperty(key);
    }
    
    // Database Table Mappings
    public String getTableName(String entity) {
        String key = String.format("table.%s", entity.toLowerCase());
        return properties.getProperty(key);
    }
    
    // Primary Key Mappings
    public String getPrimaryKey(String entity) {
        String key = String.format("primarykey.%s", entity.toLowerCase());
        return properties.getProperty(key);
    }
    
    // Foreign Key Mappings
    public Map<String, String> getForeignKeys(String entity) {
        Map<String, String> foreignKeys = new HashMap<>();
        String prefix = String.format("foreignkey.%s.", entity.toLowerCase());
        
        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith(prefix)) {
                String fieldName = key.substring(prefix.length());
                String referencedTable = properties.getProperty(key);
                foreignKeys.put(fieldName, referencedTable);
            }
        }
        return foreignKeys;
    }
    
    // Null Constraint Configuration
    public List<String> getNullConstraintFields(String entity) {
        String key = String.format("null.constraint.%s", entity.toLowerCase());
        String value = properties.getProperty(key);
        if (value != null && !value.trim().isEmpty()) {
            return Arrays.asList(value.split(","));
        }
        return new ArrayList<>();
    }
    
    // Unique Constraint Configuration
    public List<String> getUniqueConstraintFields(String entity) {
        String key = String.format("unique.constraint.%s", entity.toLowerCase());
        String value = properties.getProperty(key);
        if (value != null && !value.trim().isEmpty()) {
            return Arrays.asList(value.split(","));
        }
        return new ArrayList<>();
    }
    
    // Defect Configuration
    public String getDefectTableName() {
        return properties.getProperty("defect.table.name", "defects");
    }
    
    public String getDefectIdPrefix() {
        return properties.getProperty("defect.id.prefix", "D_");
    }
    
    // Generic property getter
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    // Validation methods
    public boolean isValidEntity(String entity) {
        return getTableName(entity) != null;
    }
    
    public boolean isValidOperation(String operation, String entity) {
        return getApiEndpoint(operation, entity) != null;
    }
    
    // Get all entities configured
    public Set<String> getConfiguredEntities() {
        Set<String> entities = new HashSet<>();
        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith("table.")) {
                entities.add(key.substring("table.".length()));
            }
        }
        return entities;
    }
    
    // Get all operations for an entity
    public Set<String> getConfiguredOperations(String entity) {
        Set<String> operations = new HashSet<>();
        String entityLower = entity.toLowerCase();
        
        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith("api.") && key.endsWith("." + entityLower)) {
                String[] parts = key.split("\\.");
                if (parts.length >= 2) {
                    operations.add(parts[1]);
                }
            }
        }
        return operations;
    }
}
