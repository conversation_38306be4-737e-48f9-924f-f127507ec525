package com.rbts.config;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * Configuration Manager for CRUD Testing Framework
 * Handles all configuration properties including API endpoints, database mappings, and constraints
 */
@Slf4j
public class ConfigManager {
    
    private static ConfigManager instance;
    private Properties properties;
    private static final String CONFIG_FILE = "config.properties";
    
    private ConfigManager() {
        loadProperties();
    }
    
    public static ConfigManager getInstance() {
        if (instance == null) {
            synchronized (ConfigManager.class) {
                if (instance == null) {
                    instance = new ConfigManager();
                }
            }
        }
        return instance;
    }
    
    private void loadProperties() {
        properties = new Properties();
        try (InputStream input = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (input == null) {
                throw new RuntimeException("Unable to find " + CONFIG_FILE);
            }
            properties.load(input);
            log.info("Configuration loaded successfully from {}", CONFIG_FILE);
        } catch (IOException ex) {
            log.error("Error loading configuration: {}", ex.getMessage());
            throw new RuntimeException("Failed to load configuration", ex);
        }
    }
    
    // Database Configuration
    public String getJdbcUrl() {
        return properties.getProperty("JDBC_URL");
    }
    
    public String getJdbcUser() {
        return properties.getProperty("JDBC_USER");
    }
    
    public String getJdbcPassword() {
        return properties.getProperty("JDBC_PASSWORD");
    }
    
    public int getJdbcConnectionTimeout() {
        return Integer.parseInt(properties.getProperty("JDBC_CONNECTION_TIMEOUT", "10"));
    }
    
    // Base URL Configuration
    public String getBaseUrl() {
        return properties.getProperty("baseURI_Qa");
    }
    
    // API Endpoint Configuration
    public String getApiEndpoint(String operation, String entity) {
        String key = String.format("api.%s.%s", operation.toLowerCase(), entity.toLowerCase());
        return properties.getProperty(key);
    }
    
    // Database Table Mappings
    public String getTableName(String entity) {
        String key = String.format("table.%s", entity.toLowerCase());
        return properties.getProperty(key);
    }
    
    // Primary Key Mappings
    public String getPrimaryKey(String entity) {
        String key = String.format("primarykey.%s", entity.toLowerCase());
        return properties.getProperty(key);
    }
    
    // Foreign Key Mappings
    public Map<String, String> getForeignKeys(String entity) {
        Map<String, String> foreignKeys = new HashMap<>();
        String prefix = String.format("foreignkey.%s.", entity.toLowerCase());
        
        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith(prefix)) {
                String fieldName = key.substring(prefix.length());
                String referencedTable = properties.getProperty(key);
                foreignKeys.put(fieldName, referencedTable);
            }
        }
        return foreignKeys;
    }
    
    // Null Constraint Configuration
    public List<String> getNullConstraintFields(String entity) {
        String key = String.format("null.constraint.%s", entity.toLowerCase());
        String value = properties.getProperty(key);
        if (value != null && !value.trim().isEmpty()) {
            return Arrays.asList(value.split(","));
        }
        return new ArrayList<>();
    }
    
    // Unique Constraint Configuration
    public List<String> getUniqueConstraintFields(String entity) {
        String key = String.format("unique.constraint.%s", entity.toLowerCase());
        String value = properties.getProperty(key);
        if (value != null && !value.trim().isEmpty()) {
            return Arrays.asList(value.split(","));
        }
        return new ArrayList<>();
    }
    
    // Defect Configuration
    public String getDefectTableName() {
        return properties.getProperty("defect.table.name", "defects");
    }
    
    public String getDefectIdPrefix() {
        return properties.getProperty("defect.id.prefix", "D_");
    }
    
    // Generic property getter
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    // Validation methods
    public boolean isValidEntity(String entity) {
        return getTableName(entity) != null;
    }
    
    public boolean isValidOperation(String operation, String entity) {
        return getApiEndpoint(operation, entity) != null;
    }
    
    // Get all entities configured
    public Set<String> getConfiguredEntities() {
        Set<String> entities = new HashSet<>();
        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith("table.")) {
                entities.add(key.substring("table.".length()));
            }
        }
        return entities;
    }

    // Get all property names
    public Set<String> getPropertyNames() {
        return properties.stringPropertyNames();
    }
    
    // Get all operations for an entity
    public Set<String> getConfiguredOperations(String entity) {
        Set<String> operations = new HashSet<>();
        String entityLower = entity.toLowerCase();

        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith("api.") && key.endsWith("." + entityLower)) {
                String[] parts = key.split("\\.");
                if (parts.length >= 2) {
                    operations.add(parts[1]);
                }
            }
        }
        return operations;
    }

    // API Pattern Configuration Methods

    /**
     * Get API pattern for entity (direct or proxy)
     */
    public String getEntityPattern(String entity) {
        String key = String.format("entity.pattern.%s", entity.toLowerCase());
        return properties.getProperty(key, "direct"); // Default to direct
    }

    /**
     * Check if entity uses direct API pattern
     */
    public boolean isDirectPattern(String entity) {
        return "direct".equals(getEntityPattern(entity));
    }

    /**
     * Check if entity uses proxy API pattern
     */
    public boolean isProxyPattern(String entity) {
        return "proxy".equals(getEntityPattern(entity));
    }

    /**
     * Get direct pattern base URL
     */
    public String getDirectBaseUrl() {
        return properties.getProperty("api.pattern.direct.base_url");
    }

    /**
     * Get proxy pattern base URL
     */
    public String getProxyBaseUrl() {
        return properties.getProperty("api.pattern.proxy.base_url");
    }

    /**
     * Get proxy pattern endpoint
     */
    public String getProxyEndpoint() {
        return properties.getProperty("api.pattern.proxy.endpoint", "/decrypt");
    }

    /**
     * Get tenant ID for proxy pattern
     */
    public String getTenantId() {
        return properties.getProperty("api.pattern.proxy.tenant_id");
    }

    /**
     * Get bearer token for direct pattern
     */
    public String getBearerToken() {
        return properties.getProperty("api.pattern.direct.bearer_token");
    }

    /**
     * Get authentication type for direct pattern
     */
    public String getAuthType() {
        return properties.getProperty("api.pattern.direct.auth_type", "bearer");
    }

    /**
     * Get headers for direct pattern
     */
    public Map<String, String> getDirectHeaders() {
        Map<String, String> headers = new HashMap<>();
        String prefix = "api.pattern.direct.headers.";

        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith(prefix)) {
                String headerName = key.substring(prefix.length());
                String headerValue = properties.getProperty(key);
                headers.put(headerName, headerValue);
            }
        }
        return headers;
    }

    /**
     * Get endpoint for direct pattern
     */
    public String getDirectEndpoint(String operation, String entity) {
        String key = String.format("api.direct.%s.%s", operation.toLowerCase(), entity.toLowerCase());
        return properties.getProperty(key);
    }

    /**
     * Get internal endpoint for proxy pattern
     */
    public String getProxyInternalEndpoint(String operation, String entity) {
        String key = String.format("api.proxy.%s.%s", operation.toLowerCase(), entity.toLowerCase());
        return properties.getProperty(key);
    }

    // Excel Column Configuration Methods

    /**
     * Get Excel column index for URL
     */
    public int getUrlColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.url", "1"));
    }

    /**
     * Get Excel column index for request body
     */
    public int getRequestBodyColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.request_body", "2"));
    }

    /**
     * Get Excel column index for expected result
     */
    public int getExpectedResultColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.expected_result", "3"));
    }

    /**
     * Get Excel column index for actual result
     */
    public int getActualResultColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.actual_result", "4"));
    }

    /**
     * Get Excel column index for status
     */
    public int getStatusColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.status", "5"));
    }

    /**
     * Get Excel column index for entity
     */
    public int getEntityColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.entity", "6"));
    }

    /**
     * Get Excel column index for pattern
     */
    public int getPatternColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.pattern", "7"));
    }

    /**
     * Get Excel column index for operation
     */
    public int getOperationColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.operation", "8"));
    }

    /**
     * Get Excel column index for table name
     */
    public int getTableNameColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.table_name", "9"));
    }

    /**
     * Get pattern detection method
     */
    public String getPatternDetectionMethod() {
        return properties.getProperty("pattern.detection.method", "auto");
    }

    /**
     * Get default pattern
     */
    public String getDefaultPattern() {
        return properties.getProperty("default.pattern", "direct");
    }

    /**
     * Detect pattern from URL (if URL contains /decrypt, it's proxy pattern)
     */
    public String detectPatternFromUrl(String url) {
        if (url != null && url.contains("/decrypt")) {
            return "proxy";
        }
        return "direct";
    }

    /**
     * Determine pattern based on detection method and available data
     */
    public String determinePattern(String entityPattern, String urlPattern, String excelPattern) {
        String detectionMethod = getPatternDetectionMethod();

        switch (detectionMethod.toLowerCase()) {
            case "auto":
                // Priority: Excel pattern > URL detection > Entity config > Default
                if (excelPattern != null && !excelPattern.trim().isEmpty()) {
                    return excelPattern.toLowerCase();
                }
                if (urlPattern != null && !urlPattern.trim().isEmpty()) {
                    return urlPattern.toLowerCase();
                }
                if (entityPattern != null && !entityPattern.trim().isEmpty()) {
                    return entityPattern.toLowerCase();
                }
                return getDefaultPattern();

            case "config":
                return entityPattern != null ? entityPattern : getDefaultPattern();

            case "url":
                return urlPattern != null ? urlPattern : getDefaultPattern();

            default:
                return getDefaultPattern();
        }
    }
}
