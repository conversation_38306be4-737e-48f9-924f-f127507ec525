# 🚀 Fully Automated CRUD Testing Framework

## 🎯 Zero Test Case Writing Required!

This framework provides **100% automated CRUD testing** with **ZERO test case writing**. Just configure your tables in `config.properties` and run!

## ✨ Key Features

### 🔄 **Fully Automated**
- **Zero test case writing** - Just configure table names
- **Automatic schema detection** from database
- **Automatic request body generation** for all operations
- **Automatic URL generation** for both patterns
- **Automatic validation** for all test scenarios

### 🏗️ **Service-Wise Organization**
```properties
# Just add your services and tables here!
service.tables.contact=AddressType,ContactType,Address,Contact
service.tables.authentication=User,Role,Permission,UserRole,UserPermission
service.tables.core=Country,State,City,Currency,Language
```

### 🔍 **Comprehensive Testing**
- ✅ **Normal CRUD Operations** (POST, PUT, PATCH, GET, DELETE)
- ✅ **Status Code Validation** for all operations
- ✅ **Request/Response Body Matching**
- ✅ **Null Constraint Validation** with status code checks
- ✅ **Unique Constraint Validation** with status code checks
- ✅ **Foreign Key Validation** with invalid foreign key tests
- ✅ **Database Validation** with automatic ID extraction

### 🔀 **Dual Pattern Support**
- **Direct Pattern**: `http://localhost:8071/contact/api/AddressType/save`
- **Proxy Pattern**: `http://localhost:9762/decrypt` with payload wrapper

## 🛠️ Setup (One-Time Configuration)

### 1. Configure Services and Tables
```properties
# =============================================================================
# SERVICES AND TABLES - JUST ADD YOUR TABLES HERE!
# =============================================================================

# Contact Service Tables
service.tables.contact=AddressType,ContactType,Address,Contact

# Authentication Service Tables  
service.tables.authentication=User,Role,Permission,UserRole,UserPermission

# Core Service Tables
service.tables.core=Country,State,City,Currency,Language

# Order Service Tables
service.tables.order=Order,OrderItem,OrderStatus,Payment

# Product Service Tables
service.tables.product=Product,Category,Brand,ProductCategory
```

### 2. Configure Patterns per Service
```properties
# Service-wise Pattern Assignment
service.pattern.contact=proxy
service.pattern.authentication=proxy
service.pattern.core=direct
service.pattern.order=proxy
service.pattern.product=direct
```

### 3. Configure URL Structures
```properties
# Direct Pattern URL Structure
direct.pattern.base_url=http://localhost:8071
direct.pattern.url_structure={base_url}/{service}/api/{table}/{operation}

# Proxy Pattern URL Structure
proxy.pattern.base_url=http://localhost:9762
proxy.pattern.endpoint=/decrypt
proxy.internal.endpoint_structure=/{service}/api/{table}/{operation}
```

### 4. Configure Database Connection
```properties
JDBC_URL=**********************************************
JDBC_USER=your_username
JDBC_PASSWORD=your_password
```

## 🚀 Usage (Zero Code Required!)

### Run All Tests for All Tables
```java
@Test
public void executeAllAutomatedCrudTests() {
    AutomatedCrudTestEngine testEngine = new AutomatedCrudTestEngine();
    Map<String, List<TestResult>> results = testEngine.executeAllAutomatedTests();
    // That's it! All tables tested automatically!
}
```

### Or Just Run the Pre-built Test Class
```bash
mvn test -Dtest=AutomatedCrudTest
```

## 🔍 What Gets Tested Automatically

### For Each Table, Each Operation:

#### 📝 **POST Operation Tests**
1. **Normal POST**: Generate valid request body → POST → Validate status code → Extract ID → Query database → Compare
2. **Null Constraint Violation**: Generate request with null required field → POST → Expect 400/422
3. **Unique Constraint Violation**: Generate request with duplicate unique field → POST → Expect 400/422
4. **Invalid Foreign Key**: Generate request with non-existent foreign key → POST → Expect 400/422

#### ✏️ **PUT Operation Tests**
1. **Normal PUT**: Generate valid request body → PUT → Validate status code → Query database → Compare
2. **Null Constraint Violation**: Generate request with null required field → PUT → Expect 400/422
3. **Unique Constraint Violation**: Generate request with duplicate unique field → PUT → Expect 400/422
4. **Invalid Foreign Key**: Generate request with non-existent foreign key → PUT → Expect 400/422

#### 🔧 **PATCH Operation Tests**
1. **Normal PATCH**: Generate request body (no foreign keys) → PATCH → Validate status code
2. **Null Constraint Violation**: Generate request with null required field → PATCH → Expect 400/422

#### 📖 **GET Operation Tests**
1. **GET by ID**: Generate URL with existing ID → GET → Validate status code → Validate response
2. **GET All**: Generate URL → GET → Validate status code → Validate response

#### 🗑️ **DELETE Operation Tests**
1. **DELETE by ID**: Generate URL with existing ID → DELETE → Validate status code

## 🤖 Automatic Request Body Generation

### Direct Pattern Example (AddressType)
```json
{
    "type": "Sample Text_1704123456789",
    "description": "Sample description",
    "isActive": true,
    "createdBy": "testUser"
}
```

### Proxy Pattern Example (UserPermission)
```json
{
    "endpoint": "/authentication/api/UserPermission/save",
    "payload": {
        "userId": {"id": 123},
        "permissionKey": "SAMPLE_PERM_1704123456789",
        "isActive": true
    },
    "type": "post",
    "tenantId": "redberyl_redberyltech_com",
    "auth": null
}
```

## 🔍 Automatic Schema Detection

The framework automatically detects:
- **Column names and data types**
- **Primary keys**
- **Foreign key relationships**
- **Unique constraints**
- **Not-null constraints**

## 📊 Automatic Validation

### Status Code Validation
```properties
validation.status_code.post=201,200
validation.status_code.put=200
validation.status_code.patch=200
validation.status_code.get=200
validation.status_code.delete=200,204
validation.constraint_violation.expected_status=400,422
```

### Database Validation
- Automatic ID extraction from response
- Query database with extracted ID
- Convert database result to JSON
- Handle foreign key resolution automatically

## 📈 Automatic Reporting

### Console Output
```
=== AUTOMATED CRUD TESTING SUMMARY ===
Total Tests Executed: 156
Passed: 142 (91%)
Failed: 14 (9%)
Tables Tested: 12

=== Results for contact.AddressType ===
✅ PASSED: AddressType post normal
✅ PASSED: AddressType post null_constraint
❌ FAILED: AddressType post unique_constraint - Unique constraint not enforced
   🐛 Defect ID: D_addresstype_post_unique_constraint_001
```

### Automatic Defect Generation
- **Unique defect IDs**: `D_tablename_operation_testtype_001`
- **Detailed defect records** in database
- **Automatic defect tracking** for failed tests

## 🎯 Benefits

### 🚀 **Zero Effort Testing**
- Add table name to config → Get full CRUD testing
- No test case writing required
- No maintenance of test data

### 📈 **Comprehensive Coverage**
- All operations tested automatically
- All constraint types validated
- Both API patterns supported

### 🔍 **Intelligent Testing**
- Schema-aware request generation
- Pattern-aware URL generation
- Constraint-aware validation

### 📊 **Detailed Reporting**
- Comprehensive test results
- Automatic defect tracking
- Service-wise and operation-wise reporting

## 🔧 Advanced Configuration

### Custom Data Generation
```properties
sample.data.string=Custom Sample Text
sample.data.integer=999
sample.data.boolean=false
```

### Custom Validation Rules
```properties
validation.status_code.post=201
validation.constraint_violation.expected_status=400
```

### Foreign Key Handling
```properties
foreign.key.sample.strategy=existing_random
foreign.key.fallback.value=1
```

## 🎉 Getting Started

1. **Clone the framework**
2. **Update config.properties** with your services and tables
3. **Update database connection** settings
4. **Run the test**: `mvn test -Dtest=AutomatedCrudTest`
5. **Review results** in console and defect database

That's it! **Zero test case writing required!** 🚀

## 📝 Example Configuration for Your Application

```properties
# Your Services and Tables
service.tables.contact=AddressType,ContactType,Address,Contact
service.tables.authentication=User,Role,Permission,UserRole,UserPermission

# Your URL Patterns
direct.pattern.base_url=http://localhost:8071
proxy.pattern.base_url=http://localhost:9762
proxy.pattern.tenant_id=your_tenant_id

# Your Database
JDBC_URL=****************************************
JDBC_USER=your_user
JDBC_PASSWORD=your_password
```

**Add your tables → Run test → Get comprehensive CRUD testing!** 🎯
