package com.rbts.crud;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * Test Result class for CRUD Testing Framework
 * Holds the result of a CRUD operation test execution
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TestResult {
    
    // Test identification
    private String entity;
    private String operation;
    private String testType;
    
    // Request details
    private String requestBody;
    private String endpoint;
    private String fullUrl;
    
    // Response details
    private int statusCode;
    private String responseBody;
    private String extractedId;
    
    // Database validation
    private String actualResult;
    private String expectedResult;
    
    // Test result
    private boolean testPassed;
    private String comparisonMessage;
    private List<String> differences;
    
    // Defect tracking
    private String defectId;
    
    // Execution details
    private long executionTimeMs;
    private String errorMessage;
    
    public TestResult(String entity, String operation, String testType) {
        this.entity = entity;
        this.operation = operation;
        this.testType = testType;
        this.differences = new ArrayList<>();
    }
    
    /**
     * Add a difference to the differences list
     */
    public void addDifference(String difference) {
        if (this.differences == null) {
            this.differences = new ArrayList<>();
        }
        this.differences.add(difference);
    }
    
    /**
     * Check if test has any differences
     */
    public boolean hasDifferences() {
        return differences != null && !differences.isEmpty();
    }
    
    /**
     * Get formatted summary of the test result
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("Test: %s %s (%s) - ", entity, operation, testType));
        summary.append(testPassed ? "PASSED" : "FAILED");
        
        if (statusCode > 0) {
            summary.append(String.format(" [Status: %d]", statusCode));
        }
        
        if (defectId != null) {
            summary.append(String.format(" [Defect: %s]", defectId));
        }
        
        return summary.toString();
    }
    
    /**
     * Get detailed result information
     */
    public String getDetailedResult() {
        StringBuilder details = new StringBuilder();
        details.append("=== Test Result Details ===\n");
        details.append(String.format("Entity: %s\n", entity));
        details.append(String.format("Operation: %s\n", operation));
        details.append(String.format("Test Type: %s\n", testType));
        details.append(String.format("Status: %s\n", testPassed ? "PASSED" : "FAILED"));
        details.append(String.format("HTTP Status Code: %d\n", statusCode));
        
        if (extractedId != null) {
            details.append(String.format("Extracted ID: %s\n", extractedId));
        }
        
        if (comparisonMessage != null) {
            details.append(String.format("Comparison Message: %s\n", comparisonMessage));
        }
        
        if (hasDifferences()) {
            details.append("Differences:\n");
            for (String difference : differences) {
                details.append(String.format("  - %s\n", difference));
            }
        }
        
        if (defectId != null) {
            details.append(String.format("Defect ID: %s\n", defectId));
        }
        
        if (executionTimeMs > 0) {
            details.append(String.format("Execution Time: %d ms\n", executionTimeMs));
        }
        
        if (errorMessage != null) {
            details.append(String.format("Error: %s\n", errorMessage));
        }
        
        return details.toString();
    }
    
    /**
     * Check if the test was successful (passed and no defects)
     */
    public boolean isSuccessful() {
        return testPassed && defectId == null;
    }
    
    /**
     * Check if the test failed due to API error
     */
    public boolean isApiError() {
        return statusCode >= 500;
    }
    
    /**
     * Check if the test failed due to client error
     */
    public boolean isClientError() {
        return statusCode >= 400 && statusCode < 500;
    }
    
    /**
     * Check if the API response was successful
     */
    public boolean isApiResponseSuccessful() {
        return statusCode >= 200 && statusCode < 300;
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}
