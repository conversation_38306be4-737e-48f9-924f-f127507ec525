package com.rbts.core;

import com.rbts.config.ErrorMessageValidation;
import com.rbts.config.ExcelConfigManager;
import com.rbts.config.FieldMapping;
import com.rbts.reporting.TestCaseResult;
import com.rbts.reporting.TestExecutionReporter;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;

/**
 * Specialized tester for error message validation
 * Tests specific error messages for constraint violations
 */
@Slf4j
public class ErrorMessageTester {
    
    private final ExcelConfigManager configManager;
    private final TestExecutionReporter testReporter;
    
    public ErrorMessageTester() {
        this.configManager = ExcelConfigManager.getInstance();
        this.testReporter = new TestExecutionReporter();
    }
    
    /**
     * Test error message for specific constraint violation
     */
    public TestCaseResult testConstraintErrorMessage(String serviceName, String tableName, 
                                                   String fieldName, String constraintType) {
        log.info("🔍 Testing error message for {}.{}.{} - {}", serviceName, tableName, fieldName, constraintType);
        
        try {
            // Get error message validation configuration
            ErrorMessageValidation validation = configManager.getErrorMessageValidation(
                    serviceName, tableName, fieldName, constraintType);
            
            if (validation == null) {
                return TestCaseResult.fail(tableName, "POST", "Error message validation", 
                        "Error message validation configured", "No validation found", 
                        "No error message validation configured for " + constraintType);
            }
            
            // Generate request body with constraint violation
            JSONObject requestBody = generateConstraintViolationRequestBody(tableName, fieldName, validation);
            log.info("📝 Generated constraint violation request: {}", requestBody.toString());
            
            // Get API endpoint
            String endpoint = configManager.getTableEndpoint(tableName, "post");
            if (endpoint == null || endpoint.trim().isEmpty()) {
                return TestCaseResult.fail(tableName, "POST", "Error message validation", 
                        "Valid endpoint", "No endpoint configured", "Endpoint not configured");
            }
            
            // Execute API call
            Response apiResponse = executeApiCall("POST", endpoint, requestBody.toString());
            log.info("📡 API Response - Status: {}, Body: {}", apiResponse.getStatusCode(), apiResponse.getBody().asString());
            
            // Extract error message from response
            String actualErrorMessage = extractErrorMessage(apiResponse.getBody().asString());
            int actualStatusCode = apiResponse.getStatusCode();
            
            // Validate status code
            boolean statusCodeValid = validation.validateStatusCode(actualStatusCode);
            
            // Validate error message
            boolean messageValid = validation.validateMessage(actualErrorMessage);
            
            // Create test result
            String testCase = String.format("Error Message Validation - %s for %s", constraintType, fieldName);
            String expectedResult = String.format("Status: %d, Message: %s", 
                    validation.getExpectedStatusCode(), validation.getExpectedErrorMessage());
            String actualResult = String.format("Status: %d, Message: %s", actualStatusCode, actualErrorMessage);
            
            boolean overallValid = statusCodeValid && messageValid;
            String status = overallValid ? "PASS" : "FAIL";
            String errorMessage = overallValid ? "" : 
                    String.format("Status code valid: %s, Message valid: %s", statusCodeValid, messageValid);
            
            TestCaseResult result = TestCaseResult.builder()
                    .tableName(tableName)
                    .operation("POST")
                    .testCase(testCase)
                    .expectedResult(expectedResult)
                    .actualResult(actualResult)
                    .status(status)
                    .requestBody(requestBody.toString())
                    .responseBody(apiResponse.getBody().asString())
                    .statusCode(actualStatusCode)
                    .errorMessage(errorMessage)
                    .build();
            
            // Report result with color coding
            testReporter.reportTestCaseWithColorCoding(result);
            
            return result;
            
        } catch (Exception e) {
            log.error("❌ Error testing constraint error message: {}", e.getMessage());
            return TestCaseResult.fail(tableName, "POST", "Error message validation", 
                    "Successful validation", "Exception: " + e.getMessage(), e.getMessage());
        }
    }
    
    /**
     * Test all error message validations for a table
     */
    public void testAllErrorMessagesForTable(String serviceName, String tableName) {
        log.info("🎯 Testing all error messages for {}.{}", serviceName, tableName);
        
        List<ErrorMessageValidation> validations = configManager.getErrorMessageValidationsForTable(serviceName, tableName);
        
        if (validations.isEmpty()) {
            log.warn("⚠️ No error message validations configured for {}.{}", serviceName, tableName);
            return;
        }
        
        for (ErrorMessageValidation validation : validations) {
            testConstraintErrorMessage(serviceName, tableName, validation.getFieldName(), validation.getConstraintType());
        }
    }
    
    /**
     * Generate request body with constraint violation
     */
    private JSONObject generateConstraintViolationRequestBody(String tableName, String fieldName, 
                                                             ErrorMessageValidation validation) {
        JSONObject requestBody = new JSONObject();
        
        try {
            // Get base request body structure
            List<String> requestFields = configManager.getFieldsPresentInRequest(tableName, "POST");
            
            for (String databaseField : requestFields) {
                FieldMapping mapping = configManager.getFieldMapping(tableName, databaseField);
                if (mapping != null) {
                    String apiRequestField = mapping.getApiRequestField();
                    
                    // Check if this is the field we want to violate constraint for
                    if (fieldName != null && !fieldName.isEmpty() && 
                        (databaseField.equals(fieldName) || apiRequestField.equals(fieldName))) {
                        
                        // Use the test value from validation configuration
                        Object testValue = validation.getTestValueAsObject();
                        requestBody.put(apiRequestField, testValue);
                        
                    } else {
                        // Generate valid value for other fields
                        Object fieldValue = generateValidFieldValue(mapping);
                        requestBody.put(apiRequestField, fieldValue);
                    }
                }
            }
            
            // Handle special constraint types
            if (validation.isUniqueConstraint() && fieldName != null) {
                // For unique constraint, use a value that already exists
                requestBody.put(fieldName, validation.getTestValueAsObject());
            }
            
        } catch (Exception e) {
            log.error("Error generating constraint violation request body: {}", e.getMessage());
            // Add minimal request body
            try {
                requestBody.put("name", "Test Value");
                if (fieldName != null && !fieldName.isEmpty()) {
                    requestBody.put(fieldName, validation.getTestValueAsObject());
                }
            } catch (Exception ex) {
                log.error("Error adding minimal request body: {}", ex.getMessage());
            }
        }
        
        return requestBody;
    }
    
    /**
     * Generate valid field value for non-constraint fields
     */
    private Object generateValidFieldValue(FieldMapping mapping) {
        String fieldType = mapping.getFieldType().toUpperCase();
        String fieldName = mapping.getDatabaseField();
        
        switch (fieldType) {
            case "EMAIL":
                return "<EMAIL>";
            case "STRING":
                return "Valid " + fieldName;
            case "INTEGER":
                return 1;
            case "DECIMAL":
                return 10.50;
            case "BOOLEAN":
                return true;
            case "TIMESTAMP":
                return "2024-01-01T10:00:00Z";
            default:
                return "Valid value";
        }
    }
    
    /**
     * Execute API call using RestAssured
     */
    private Response executeApiCall(String method, String endpoint, String requestBody) {
        log.info("📡 Executing {} call to: {}", method, endpoint);
        
        return RestAssured.given()
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + configManager.getGeneralConfig("api.pattern.direct.bearer_token"))
                .body(requestBody)
                .when()
                .post(endpoint)
                .then()
                .extract()
                .response();
    }
    
    /**
     * Extract error message from API response
     */
    private String extractErrorMessage(String responseBody) {
        try {
            JSONObject response = new JSONObject(responseBody);
            
            // Try common error message fields
            if (response.has("message")) {
                return response.getString("message");
            } else if (response.has("error")) {
                Object error = response.get("error");
                if (error instanceof String) {
                    return (String) error;
                } else if (error instanceof JSONObject) {
                    JSONObject errorObj = (JSONObject) error;
                    if (errorObj.has("message")) {
                        return errorObj.getString("message");
                    }
                }
            } else if (response.has("errorMessage")) {
                return response.getString("errorMessage");
            } else if (response.has("details")) {
                return response.getString("details");
            }
            
            // If no specific error field found, return the whole response
            return responseBody;
            
        } catch (Exception e) {
            log.error("Error extracting error message from response: {}", e.getMessage());
            return responseBody;
        }
    }
    
    /**
     * Test success message validation
     */
    public TestCaseResult testSuccessMessage(String serviceName, String tableName) {
        log.info("✅ Testing success message for {}.{}", serviceName, tableName);
        
        try {
            // Get success message validation
            ErrorMessageValidation validation = configManager.getErrorMessageValidation(
                    serviceName, tableName, "", "success");
            
            if (validation == null) {
                return TestCaseResult.pass(tableName, "POST", "Success message validation", 
                        "No specific validation", "No validation configured");
            }
            
            // Generate valid request body
            JSONObject requestBody = generateValidRequestBody(tableName);
            
            // Get API endpoint
            String endpoint = configManager.getTableEndpoint(tableName, "post");
            
            // Execute API call
            Response apiResponse = executeApiCall("POST", endpoint, requestBody.toString());
            
            // Extract success message
            String actualMessage = extractSuccessMessage(apiResponse.getBody().asString());
            int actualStatusCode = apiResponse.getStatusCode();
            
            // Validate
            boolean statusCodeValid = validation.validateStatusCode(actualStatusCode);
            boolean messageValid = validation.validateMessage(actualMessage);
            
            boolean overallValid = statusCodeValid && messageValid;
            String status = overallValid ? "PASS" : "FAIL";
            
            TestCaseResult result = TestCaseResult.builder()
                    .tableName(tableName)
                    .operation("POST")
                    .testCase("Success Message Validation")
                    .expectedResult(String.format("Status: %d, Message: %s", 
                            validation.getExpectedStatusCode(), validation.getExpectedErrorMessage()))
                    .actualResult(String.format("Status: %d, Message: %s", actualStatusCode, actualMessage))
                    .status(status)
                    .requestBody(requestBody.toString())
                    .responseBody(apiResponse.getBody().asString())
                    .statusCode(actualStatusCode)
                    .build();
            
            testReporter.reportTestCaseWithColorCoding(result);
            
            return result;
            
        } catch (Exception e) {
            log.error("❌ Error testing success message: {}", e.getMessage());
            return TestCaseResult.fail(tableName, "POST", "Success message validation", 
                    "Successful validation", "Exception: " + e.getMessage(), e.getMessage());
        }
    }
    
    /**
     * Generate valid request body for success testing
     */
    private JSONObject generateValidRequestBody(String tableName) {
        JSONObject requestBody = new JSONObject();
        
        try {
            List<String> requestFields = configManager.getFieldsPresentInRequest(tableName, "POST");
            
            for (String databaseField : requestFields) {
                FieldMapping mapping = configManager.getFieldMapping(tableName, databaseField);
                if (mapping != null) {
                    String apiRequestField = mapping.getApiRequestField();
                    Object fieldValue = generateValidFieldValue(mapping);
                    requestBody.put(apiRequestField, fieldValue);
                }
            }
        } catch (Exception e) {
            log.error("Error generating valid request body: {}", e.getMessage());
            try {
                requestBody.put("name", "Valid Test Data");
                requestBody.put("description", "Valid description for testing");
            } catch (Exception ex) {
                log.error("Error adding default valid fields: {}", ex.getMessage());
            }
        }
        
        return requestBody;
    }
    
    /**
     * Extract success message from API response
     */
    private String extractSuccessMessage(String responseBody) {
        try {
            JSONObject response = new JSONObject(responseBody);
            
            // Try common success message fields
            if (response.has("message")) {
                return response.getString("message");
            } else if (response.has("successMessage")) {
                return response.getString("successMessage");
            } else if (response.has("status")) {
                return response.getString("status");
            }
            
            return "Success"; // Default success message
            
        } catch (Exception e) {
            log.error("Error extracting success message: {}", e.getMessage());
            return "Success";
        }
    }
    
    /**
     * Get test reporter instance
     */
    public TestExecutionReporter getTestReporter() {
        return testReporter;
    }
}
