package com.rbts.core;

import com.rbts.config.ExcelConfigManager;
import com.rbts.config.FieldMapping;
import com.rbts.database.DatabaseManager;
import com.rbts.reporting.TestExecutionReporter;
import com.rbts.reporting.TestCaseResult;
import com.rbts.utils.JsonUtils;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Real-time API tester with database validation and Excel reporting
 */
@Slf4j
public class RealTimeApiTester {
    
    private final ExcelConfigManager configManager;
    private final DatabaseManager databaseManager;
    private final TestExecutionReporter testReporter;
    private final JsonUtils jsonUtils;
    
    public RealTimeApiTester() {
        this.configManager = ExcelConfigManager.getInstance();
        this.databaseManager = new DatabaseManager();
        this.testReporter = new TestExecutionReporter();
        this.jsonUtils = new JsonUtils();
    }
    
    /**
     * Execute complete POST API test with database validation
     */
    public TestCaseResult executePostApiTest(String tableName) {
        log.info("🚀 Executing POST API test for table: {}", tableName);
        
        try {
            // Step 1: Generate request body
            JSONObject requestBody = generateRequestBody(tableName);
            log.info("📝 Generated request body: {}", requestBody.toString());
            
            // Step 2: Get API endpoint
            String endpoint = configManager.getTableEndpoint(tableName, "post");
            if (endpoint == null || endpoint.trim().isEmpty()) {
                return TestCaseResult.fail(tableName, "POST", "API endpoint test", 
                        "Valid endpoint", "No endpoint configured", "Endpoint not configured");
            }
            
            // Step 3: Execute API call
            Response apiResponse = executeApiCall("POST", endpoint, requestBody.toString());
            log.info("📡 API Response - Status: {}, Body: {}", apiResponse.getStatusCode(), apiResponse.getBody().asString());
            
            // Step 4: Validate status code
            TestCaseResult statusCodeResult = validateStatusCode(tableName, "POST", apiResponse.getStatusCode(), apiResponse.getBody().asString());
            if (statusCodeResult.isFailed()) {
                return statusCodeResult;
            }
            
            // Step 5: Extract ID from response
            String createdId = extractIdFromResponse(apiResponse.getBody().asString());
            if (createdId == null) {
                return TestCaseResult.fail(tableName, "POST", "ID extraction", 
                        "Valid ID in response", "No ID found", "Could not extract ID from response");
            }
            
            // Step 6: Query database and build expected result
            JSONObject expectedResult = buildExpectedResultFromDatabase(tableName, createdId);
            log.info("🗄️ Expected result from database: {}", expectedResult.toString());
            
            // Step 7: Build actual result from API response
            JSONObject actualResult = buildActualResultFromApiResponse(tableName, apiResponse.getBody().asString());
            log.info("📡 Actual result from API: {}", actualResult.toString());
            
            // Step 8: Compare expected vs actual
            boolean isMatch = compareResults(expectedResult, actualResult);
            
            // Step 9: Create test result
            TestCaseResult result = TestCaseResult.builder()
                    .tableName(tableName)
                    .operation("POST")
                    .testCase("Create record and validate with database")
                    .expectedResult(expectedResult.toString())
                    .actualResult(actualResult.toString())
                    .status(isMatch ? "PASS" : "FAIL")
                    .requestBody(requestBody.toString())
                    .responseBody(apiResponse.getBody().asString())
                    .statusCode(apiResponse.getStatusCode())
                    .build();
            
            // Step 10: Report result with color coding
            testReporter.reportTestCaseWithColorCoding(result);
            
            return result;
            
        } catch (Exception e) {
            log.error("❌ Error executing POST API test for {}: {}", tableName, e.getMessage());
            return TestCaseResult.fail(tableName, "POST", "API test execution", 
                    "Successful execution", "Exception: " + e.getMessage(), e.getMessage());
        }
    }
    
    /**
     * Generate request body based on table schema and field mappings
     */
    private JSONObject generateRequestBody(String tableName) {
        JSONObject requestBody = new JSONObject();

        try {
            // Get fields that should be present in POST request
            List<String> requestFields = configManager.getFieldsPresentInRequest(tableName, "POST");

            for (String databaseField : requestFields) {
                FieldMapping mapping = configManager.getFieldMapping(tableName, databaseField);
                if (mapping != null) {
                    String apiRequestField = mapping.getApiRequestField();

                    if (mapping.isForeignKey()) {
                        // Handle foreign key based on service relationship
                        Object fkValue = generateForeignKeyValue(mapping);
                        requestBody.put(apiRequestField, fkValue);
                    } else if (mapping.isAuditField()) {
                        // Skip audit fields in POST request
                        continue;
                    } else {
                        // Generate regular field value
                        Object fieldValue = generateFieldValue(mapping);
                        requestBody.put(apiRequestField, fieldValue);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error generating request body for {}: {}", tableName, e.getMessage());
            // Add default fields for testing
            try {
                requestBody.put("name", "Sample " + tableName);
                requestBody.put("description", "Sample description for " + tableName);
            } catch (Exception ex) {
                log.error("Error adding default fields: {}", ex.getMessage());
            }
        }

        return requestBody;
    }
    
    /**
     * Generate foreign key value based on service relationship
     */
    private Object generateForeignKeyValue(FieldMapping mapping) {
        try {
            if (mapping.isForeignKeyFromSameService()) {
                // Same service - return full payload
                JSONObject fullPayload = new JSONObject();
                fullPayload.put("id", 1);
                fullPayload.put("name", "Sample " + mapping.getSameServiceName());
                fullPayload.put("description", "Sample description");
                return fullPayload;
            } else if (mapping.isForeignKeyFromDifferentService()) {
                // Different service - return ID only
                return 1;
            } else {
                // Regular foreign key - return ID
                return 1;
            }
        } catch (Exception e) {
            log.error("Error generating foreign key value: {}", e.getMessage());
            return 1; // Default to ID only
        }
    }
    
    /**
     * Generate field value based on field type
     */
    private Object generateFieldValue(FieldMapping mapping) {
        String fieldType = mapping.getFieldType().toUpperCase();
        String fieldName = mapping.getDatabaseField();
        
        switch (fieldType) {
            case "EMAIL":
                return "<EMAIL>";
            case "STRING":
                return "Sample " + fieldName;
            case "INTEGER":
                return 100;
            case "DECIMAL":
                return 99.99;
            case "BOOLEAN":
                return true;
            case "TIMESTAMP":
                return "2024-01-01T10:00:00Z";
            default:
                return "Sample value";
        }
    }
    
    /**
     * Execute API call using RestAssured
     */
    private Response executeApiCall(String method, String endpoint, String requestBody) {
        log.info("📡 Executing {} call to: {}", method, endpoint);
        
        return RestAssured.given()
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + configManager.getGeneralConfig("api.pattern.direct.bearer_token"))
                .body(requestBody)
                .when()
                .post(endpoint)
                .then()
                .extract()
                .response();
    }
    
    /**
     * Validate status code based on operation and constraint type
     */
    private TestCaseResult validateStatusCode(String tableName, String operation, int actualStatusCode, String responseBody) {
        String expectedStatusCodes = configManager.getValidationConfig("validation.status_code." + operation.toLowerCase());
        
        if (expectedStatusCodes != null) {
            String[] validCodes = expectedStatusCodes.split(",");
            for (String code : validCodes) {
                if (String.valueOf(actualStatusCode).equals(code.trim())) {
                    return TestCaseResult.pass(tableName, operation, "Status code validation", 
                            "Status code: " + expectedStatusCodes, "Status code: " + actualStatusCode);
                }
            }
        }
        
        return TestCaseResult.fail(tableName, operation, "Status code validation", 
                "Status code: " + expectedStatusCodes, "Status code: " + actualStatusCode, 
                "Unexpected status code received");
    }
    
    /**
     * Extract ID from API response
     */
    private String extractIdFromResponse(String responseBody) {
        try {
            JSONObject response = new JSONObject(responseBody);
            if (response.has("id")) {
                return response.get("id").toString();
            } else if (response.has("data") && response.getJSONObject("data").has("id")) {
                return response.getJSONObject("data").get("id").toString();
            }
        } catch (Exception e) {
            log.error("Error extracting ID from response: {}", e.getMessage());
        }
        return null;
    }
    
    /**
     * Build expected result from database query
     */
    private JSONObject buildExpectedResultFromDatabase(String tableName, String id) {
        JSONObject expectedResult = new JSONObject();

        try {
            // Query database for the record
            Map<String, Object> dbRecord = databaseManager.getRecordById(tableName, id);

            // Convert database fields to API response fields
            Map<String, FieldMapping> fieldMappings = configManager.getFieldMappingsForTable(tableName);

            for (Map.Entry<String, Object> entry : dbRecord.entrySet()) {
                String dbField = entry.getKey();
                Object dbValue = entry.getValue();

                FieldMapping mapping = fieldMappings.get(dbField);
                if (mapping != null) {
                    String apiResponseField = mapping.getApiResponseField();

                    if (mapping.isForeignKey()) {
                        // Handle foreign key based on service relationship
                        Object fkValue = buildForeignKeyValueFromDatabase(mapping, dbValue);
                        expectedResult.put(apiResponseField, fkValue);
                    } else {
                        expectedResult.put(apiResponseField, dbValue);
                    }
                } else {
                    // No mapping found, use original field name
                    expectedResult.put(dbField, dbValue);
                }
            }

        } catch (Exception e) {
            log.error("Error building expected result from database: {}", e.getMessage());
            // Add default expected result for testing
            try {
                expectedResult.put("id", Integer.parseInt(id));
                expectedResult.put("name", "Sample " + tableName);
                expectedResult.put("description", "Sample description");
            } catch (Exception ex) {
                log.error("Error adding default expected result: {}", ex.getMessage());
            }
        }

        return expectedResult;
    }
    
    /**
     * Build foreign key value from database based on service relationship
     */
    private Object buildForeignKeyValueFromDatabase(FieldMapping mapping, Object fkId) {
        try {
            if (mapping.isForeignKeyFromSameService()) {
                // Same service - query and return full payload
                try {
                    String referencedTable = inferReferencedTableName(mapping.getDatabaseField());
                    Map<String, Object> fkRecord = databaseManager.getRecordById(referencedTable, fkId.toString());
                    return new JSONObject(fkRecord);
                } catch (Exception e) {
                    log.error("Error querying same service FK: {}", e.getMessage());
                    return fkId;
                }
            } else {
                // Different service - return ID only
                return fkId;
            }
        } catch (Exception e) {
            log.error("Error building foreign key value from database: {}", e.getMessage());
            return fkId;
        }
    }
    
    /**
     * Infer referenced table name from foreign key field name
     */
    private String inferReferencedTableName(String fkFieldName) {
        // Remove _id suffix and convert to table name
        if (fkFieldName.endsWith("_id")) {
            String tableName = fkFieldName.substring(0, fkFieldName.length() - 3);
            // Convert snake_case to PascalCase
            return toPascalCase(tableName);
        }
        return fkFieldName;
    }
    
    /**
     * Convert snake_case to PascalCase
     */
    private String toPascalCase(String snakeCase) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;
        
        for (char c : snakeCase.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        
        return result.toString();
    }
    
    /**
     * Build actual result from API response
     */
    private JSONObject buildActualResultFromApiResponse(String tableName, String responseBody) {
        try {
            JSONObject response = new JSONObject(responseBody);
            
            // If response has a data wrapper, extract it
            if (response.has("data")) {
                return response.getJSONObject("data");
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("Error building actual result from API response: {}", e.getMessage());
            return new JSONObject();
        }
    }
    
    /**
     * Compare expected vs actual results
     */
    private boolean compareResults(JSONObject expected, JSONObject actual) {
        try {
            return jsonUtils.compareJsonObjects(expected, actual);
        } catch (Exception e) {
            log.error("Error comparing results: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Get test reporter instance
     */
    public TestExecutionReporter getTestReporter() {
        return testReporter;
    }
}
