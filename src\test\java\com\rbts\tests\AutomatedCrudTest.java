package com.rbts.tests;

import com.rbts.automation.AutomatedCrudTestEngine;
import com.rbts.crud.TestResult;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

/**
 * Automated CRUD Test Class
 * Zero test case writing - fully automated testing
 * Just configure tables in config.properties and run!
 */
@Slf4j
public class AutomatedCrudTest {
    
    private AutomatedCrudTestEngine testEngine;
    
    @BeforeClass
    public void setUp() {
        testEngine = new AutomatedCrudTestEngine();
        log.info("Automated CRUD Test Engine initialized");

        // Generate test documentation before running tests
        log.info("Generating test documentation...");
        testEngine.generateTestDocumentation("data/CRUD_Test_Documentation_" +
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx");
    }
    
    /**
     * Execute all automated tests for all configured tables
     * This single test method tests ALL tables, ALL operations, ALL test types
     * No need to write individual test methods!
     */
    @Test(description = "Execute automated CRUD tests for all configured tables")
    public void executeAllAutomatedCrudTests() {
        log.info("Starting comprehensive automated CRUD testing");
        
        // Execute all tests
        Map<String, List<TestResult>> allResults = testEngine.executeAllAutomatedTests();
        
        // Summary reporting
        int totalTests = 0;
        int passedTests = 0;
        int failedTests = 0;
        
        for (Map.Entry<String, List<TestResult>> entry : allResults.entrySet()) {
            String tableKey = entry.getKey();
            List<TestResult> results = entry.getValue();
            
            log.info("=== Results for {} ===", tableKey);
            
            for (TestResult result : results) {
                totalTests++;
                if (result.isTestPassed()) {
                    passedTests++;
                    log.info("✅ PASSED: {} {} {}", result.getEntity(), result.getOperation(), result.getTestType());
                } else {
                    failedTests++;
                    log.error("❌ FAILED: {} {} {} - {}", 
                            result.getEntity(), result.getOperation(), result.getTestType(), 
                            result.getComparisonMessage());
                    
                    if (result.getDefectId() != null) {
                        log.error("   🐛 Defect ID: {}", result.getDefectId());
                    }
                }
            }
        }
        
        // Final summary
        log.info("=== AUTOMATED CRUD TESTING SUMMARY ===");
        log.info("Total Tests Executed: {}", totalTests);
        log.info("Passed: {} ({}%)", passedTests, totalTests > 0 ? (passedTests * 100 / totalTests) : 0);
        log.info("Failed: {} ({}%)", failedTests, totalTests > 0 ? (failedTests * 100 / totalTests) : 0);
        log.info("Tables Tested: {}", allResults.size());
        
        // Assert that we executed some tests
        Assert.assertTrue(totalTests > 0, "No tests were executed. Check your configuration.");
        
        // For demo purposes, we'll pass the test even if some individual tests fail
        // In a real scenario, you might want to fail if critical tests fail
        log.info("Automated CRUD testing completed successfully");
    }
    
    /**
     * Test specific service - Contact Service
     */
    @Test(description = "Test Contact Service tables", dependsOnMethods = "executeAllAutomatedCrudTests")
    public void testContactService() {
        log.info("Testing Contact Service specifically");
        
        String[] contactTables = {"AddressType", "ContactType", "Address", "Contact"};
        
        for (String table : contactTables) {
            List<TestResult> results = testEngine.executeAutomatedTestsForTable("contact", table);
            
            log.info("Contact Service - Table {}: {} tests executed", table, results.size());
            
            for (TestResult result : results) {
                if (!result.isTestPassed()) {
                    log.warn("Failed test in Contact Service: {} {} {} - {}", 
                            result.getEntity(), result.getOperation(), result.getTestType(), 
                            result.getComparisonMessage());
                }
            }
        }
    }
    
    /**
     * Test specific service - Authentication Service
     */
    @Test(description = "Test Authentication Service tables", dependsOnMethods = "executeAllAutomatedCrudTests")
    public void testAuthenticationService() {
        log.info("Testing Authentication Service specifically");
        
        String[] authTables = {"User", "Role", "Permission", "UserRole", "UserPermission"};
        
        for (String table : authTables) {
            List<TestResult> results = testEngine.executeAutomatedTestsForTable("authentication", table);
            
            log.info("Authentication Service - Table {}: {} tests executed", table, results.size());
            
            for (TestResult result : results) {
                if (!result.isTestPassed()) {
                    log.warn("Failed test in Authentication Service: {} {} {} - {}", 
                            result.getEntity(), result.getOperation(), result.getTestType(), 
                            result.getComparisonMessage());
                }
            }
        }
    }
    
    /**
     * Test specific operation across all tables - POST operations
     */
    @Test(description = "Test POST operations across all tables", dependsOnMethods = "executeAllAutomatedCrudTests")
    public void testPostOperationsAcrossAllTables() {
        log.info("Testing POST operations across all configured tables");
        
        Map<String, List<TestResult>> allResults = testEngine.executeAllAutomatedTests();
        
        int postTests = 0;
        int postPassed = 0;
        
        for (List<TestResult> results : allResults.values()) {
            for (TestResult result : results) {
                if ("post".equalsIgnoreCase(result.getOperation())) {
                    postTests++;
                    if (result.isTestPassed()) {
                        postPassed++;
                    }
                }
            }
        }
        
        log.info("POST Operations Summary: {}/{} passed", postPassed, postTests);
        
        // Assert that at least 70% of POST operations pass (configurable threshold)
        if (postTests > 0) {
            double passRate = (double) postPassed / postTests;
            Assert.assertTrue(passRate >= 0.7, 
                String.format("POST operations pass rate too low: %.2f%% (expected >= 70%%)", passRate * 100));
        }
    }
    
    /**
     * Test constraint validations across all tables
     */
    @Test(description = "Test constraint validations across all tables", dependsOnMethods = "executeAllAutomatedCrudTests")
    public void testConstraintValidationsAcrossAllTables() {
        log.info("Testing constraint validations across all configured tables");
        
        Map<String, List<TestResult>> allResults = testEngine.executeAllAutomatedTests();
        
        int constraintTests = 0;
        int constraintPassed = 0;
        
        for (List<TestResult> results : allResults.values()) {
            for (TestResult result : results) {
                String testType = result.getTestType();
                if ("null_constraint".equals(testType) || "unique_constraint".equals(testType) || 
                    "foreign_key_invalid".equals(testType)) {
                    constraintTests++;
                    if (result.isTestPassed()) {
                        constraintPassed++;
                    } else {
                        log.warn("Constraint validation failed: {} {} {} - {}", 
                                result.getEntity(), result.getOperation(), result.getTestType(), 
                                result.getComparisonMessage());
                    }
                }
            }
        }
        
        log.info("Constraint Validation Summary: {}/{} passed", constraintPassed, constraintTests);
        
        // Log constraint validation issues for review
        if (constraintTests > 0) {
            double passRate = (double) constraintPassed / constraintTests;
            log.info("Constraint validation pass rate: {:.2f}%", passRate * 100);
            
            if (passRate < 0.8) {
                log.warn("Low constraint validation pass rate detected. Review API constraint enforcement.");
            }
        }
    }
    
    /**
     * Generate test execution report
     */
    @Test(description = "Generate comprehensive test report", dependsOnMethods = {"executeAllAutomatedCrudTests", "testPostOperationsAcrossAllTables", "testConstraintValidationsAcrossAllTables"})
    public void generateTestReport() {
        log.info("Generating comprehensive test execution report");
        
        Map<String, List<TestResult>> allResults = testEngine.executeAllAutomatedTests();
        
        StringBuilder report = new StringBuilder();
        report.append("\n=== AUTOMATED CRUD TESTING COMPREHENSIVE REPORT ===\n");
        
        for (Map.Entry<String, List<TestResult>> entry : allResults.entrySet()) {
            String tableKey = entry.getKey();
            List<TestResult> results = entry.getValue();
            
            report.append(String.format("\n--- %s ---\n", tableKey));
            
            Map<String, Integer> operationCounts = new java.util.HashMap<>();
            Map<String, Integer> testTypeCounts = new java.util.HashMap<>();
            int tablePassed = 0;
            
            for (TestResult result : results) {
                // Count by operation
                operationCounts.merge(result.getOperation(), 1, Integer::sum);
                
                // Count by test type
                testTypeCounts.merge(result.getTestType(), 1, Integer::sum);
                
                if (result.isTestPassed()) {
                    tablePassed++;
                }
            }
            
            report.append(String.format("Total Tests: %d, Passed: %d, Failed: %d\n", 
                    results.size(), tablePassed, results.size() - tablePassed));
            
            report.append("Operations Tested: ").append(operationCounts.keySet()).append("\n");
            report.append("Test Types Executed: ").append(testTypeCounts.keySet()).append("\n");
        }
        
        log.info(report.toString());
        
        // This test always passes as it's just reporting
        Assert.assertTrue(true, "Test report generated successfully");
    }
}
