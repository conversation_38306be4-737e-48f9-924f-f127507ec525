package com.rbts.tests;

import com.rbts.config.ExcelConfigManager;
import com.rbts.config.ExcelConfigTemplateGenerator;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.util.List;
import java.util.Set;

/**
 * Excel Configuration Test
 * Tests the Excel-based configuration system
 */
@Slf4j
public class ExcelConfigurationTest {
    
    private ExcelConfigTemplateGenerator templateGenerator;
    private ExcelConfigManager configManager;
    private final String configFilePath = "config/Framework_Configuration.xlsx";
    
    @BeforeClass
    public void setUp() {
        templateGenerator = new ExcelConfigTemplateGenerator();
        log.info("Excel Configuration Test initialized");
    }
    
    /**
     * Generate Excel configuration template
     */
    @Test(description = "Generate Excel configuration template", priority = 1)
    public void generateExcelConfigurationTemplate() {
        log.info("Generating Excel configuration template");
        
        try {
            // Generate the template
            templateGenerator.generateConfigurationTemplate();
            
            // Verify file was created
            File configFile = new File(configFilePath);
            Assert.assertTrue(configFile.exists(), "Configuration file should be created: " + configFilePath);
            Assert.assertTrue(configFile.length() > 0, "Configuration file should not be empty");
            
            log.info("✅ Excel configuration template generated successfully: {}", configFilePath);
            log.info("📊 File size: {} bytes", configFile.length());
            
        } catch (Exception e) {
            log.error("❌ Error generating Excel configuration template: {}", e.getMessage());
            Assert.fail("Excel configuration template generation failed: " + e.getMessage());
        }
    }
    
    /**
     * Test Excel configuration loading
     */
    @Test(description = "Test Excel configuration loading", dependsOnMethods = "generateExcelConfigurationTemplate", priority = 2)
    public void testExcelConfigurationLoading() {
        log.info("Testing Excel configuration loading");
        
        try {
            // Initialize config manager (this will load from Excel)
            configManager = ExcelConfigManager.getInstance();
            
            // Test general configuration
            String frameworkName = configManager.getGeneralConfig("framework.name");
            String frameworkVersion = configManager.getGeneralConfig("framework.version");
            
            Assert.assertNotNull(frameworkName, "Framework name should be loaded");
            Assert.assertNotNull(frameworkVersion, "Framework version should be loaded");
            
            log.info("✅ General configuration loaded: {} v{}", frameworkName, frameworkVersion);
            
            // Test service configuration
            Set<String> services = configManager.getConfiguredServices();
            Assert.assertFalse(services.isEmpty(), "Services should be configured");
            
            log.info("✅ Loaded {} services: {}", services.size(), services);
            
            // Test URL configuration
            String directBaseUrl = configManager.getUrlConfig("direct.pattern.base_url");
            String proxyBaseUrl = configManager.getUrlConfig("proxy.pattern.base_url");
            
            Assert.assertNotNull(directBaseUrl, "Direct base URL should be configured");
            Assert.assertNotNull(proxyBaseUrl, "Proxy base URL should be configured");
            
            log.info("✅ URL configuration loaded: Direct={}, Proxy={}", directBaseUrl, proxyBaseUrl);
            
            // Test database configuration
            String jdbcUrl = configManager.getDatabaseConfig("JDBC_URL");
            String jdbcUser = configManager.getDatabaseConfig("JDBC_USER");
            
            Assert.assertNotNull(jdbcUrl, "JDBC URL should be configured");
            Assert.assertNotNull(jdbcUser, "JDBC User should be configured");
            
            log.info("✅ Database configuration loaded: URL={}, User={}", jdbcUrl, jdbcUser);
            
        } catch (Exception e) {
            log.error("❌ Error testing Excel configuration loading: {}", e.getMessage());
            Assert.fail("Excel configuration loading test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test service and table configuration
     */
    @Test(description = "Test service and table configuration", dependsOnMethods = "testExcelConfigurationLoading", priority = 3)
    public void testServiceAndTableConfiguration() {
        log.info("Testing service and table configuration");
        
        try {
            // Test service configuration
            Set<String> services = configManager.getConfiguredServices();
            
            for (String service : services) {
                List<String> tables = configManager.getTablesForService(service);
                String pattern = configManager.getServicePattern(service);
                
                Assert.assertFalse(tables.isEmpty(), "Service " + service + " should have tables configured");
                Assert.assertNotNull(pattern, "Service " + service + " should have pattern configured");
                
                log.info("✅ Service {}: {} tables, pattern: {}", service, tables.size(), pattern);
                log.info("   Tables: {}", tables);
            }
            
            // Test all configured tables
            Set<String> allTables = configManager.getAllConfiguredTables();
            Assert.assertFalse(allTables.isEmpty(), "Should have tables configured");
            
            log.info("✅ Total configured tables: {}", allTables.size());
            log.info("   All tables: {}", allTables);
            
        } catch (Exception e) {
            log.error("❌ Error testing service and table configuration: {}", e.getMessage());
            Assert.fail("Service and table configuration test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test table endpoint configuration
     */
    @Test(description = "Test table endpoint configuration", dependsOnMethods = "testServiceAndTableConfiguration", priority = 4)
    public void testTableEndpointConfiguration() {
        log.info("Testing table endpoint configuration");
        
        try {
            Set<String> allTables = configManager.getAllConfiguredTables();
            String[] operations = {"post", "put", "patch", "get", "delete"};
            
            for (String table : allTables) {
                for (String operation : operations) {
                    String endpoint = configManager.getTableEndpoint(table, operation);
                    
                    Assert.assertNotNull(endpoint, 
                        String.format("Endpoint should be configured for table %s operation %s", table, operation));
                    Assert.assertTrue(endpoint.startsWith("/"), 
                        "Endpoint should start with /");
                    
                    log.debug("Endpoint for {} {}: {}", table, operation, endpoint);
                }
                
                log.info("✅ Endpoints configured for table: {}", table);
            }
            
        } catch (Exception e) {
            log.error("❌ Error testing table endpoint configuration: {}", e.getMessage());
            Assert.fail("Table endpoint configuration test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test validation configuration
     */
    @Test(description = "Test validation configuration", dependsOnMethods = "testTableEndpointConfiguration", priority = 5)
    public void testValidationConfiguration() {
        log.info("Testing validation configuration");
        
        try {
            // Test status code validations
            String[] operations = {"post", "put", "patch", "get", "delete"};
            
            for (String operation : operations) {
                String statusCodes = configManager.getValidationConfig("validation.status_code." + operation);
                Assert.assertNotNull(statusCodes, "Status codes should be configured for " + operation);
                Assert.assertFalse(statusCodes.trim().isEmpty(), "Status codes should not be empty for " + operation);
                
                log.info("✅ Status codes for {}: {}", operation, statusCodes);
            }
            
            // Test constraint violation validation
            String constraintStatus = configManager.getValidationConfig("validation.constraint_violation.expected_status");
            Assert.assertNotNull(constraintStatus, "Constraint violation status codes should be configured");
            
            log.info("✅ Constraint violation status codes: {}", constraintStatus);
            
        } catch (Exception e) {
            log.error("❌ Error testing validation configuration: {}", e.getMessage());
            Assert.fail("Validation configuration test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test configuration reload functionality
     */
    @Test(description = "Test configuration reload", dependsOnMethods = "testValidationConfiguration", priority = 6)
    public void testConfigurationReload() {
        log.info("Testing configuration reload functionality");
        
        try {
            // Get initial configuration
            String initialFrameworkName = configManager.getGeneralConfig("framework.name");
            int initialServiceCount = configManager.getConfiguredServices().size();
            
            // Reload configuration
            configManager.reloadConfigurations();
            
            // Verify configuration is still loaded
            String reloadedFrameworkName = configManager.getGeneralConfig("framework.name");
            int reloadedServiceCount = configManager.getConfiguredServices().size();
            
            Assert.assertEquals(reloadedFrameworkName, initialFrameworkName, "Framework name should remain same after reload");
            Assert.assertEquals(reloadedServiceCount, initialServiceCount, "Service count should remain same after reload");
            
            log.info("✅ Configuration reload successful");
            
        } catch (Exception e) {
            log.error("❌ Error testing configuration reload: {}", e.getMessage());
            Assert.fail("Configuration reload test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test configuration summary
     */
    @Test(description = "Test configuration summary", dependsOnMethods = "testConfigurationReload", priority = 7)
    public void testConfigurationSummary() {
        log.info("Testing configuration summary");
        
        try {
            // Print configuration summary
            configManager.printConfigurationSummary();
            
            // Verify we have meaningful configuration
            Assert.assertTrue(configManager.getConfiguredServices().size() > 0, "Should have services configured");
            Assert.assertTrue(configManager.getAllConfiguredTables().size() > 0, "Should have tables configured");
            
            log.info("✅ Configuration summary generated successfully");
            
        } catch (Exception e) {
            log.error("❌ Error testing configuration summary: {}", e.getMessage());
            Assert.fail("Configuration summary test failed: " + e.getMessage());
        }
    }
    
    /**
     * Generate additional configuration templates
     */
    @Test(description = "Generate additional configuration templates", dependsOnMethods = "testConfigurationSummary", priority = 8)
    public void generateAdditionalTemplates() {
        log.info("Generating additional configuration templates");
        
        try {
            String[] templateNames = {
                "config/Framework_Configuration_Template.xlsx",
                "config/Framework_Configuration_Sample.xlsx",
                "config/Framework_Configuration_Backup.xlsx"
            };
            
            for (String templateName : templateNames) {
                templateGenerator.generateConfigurationTemplate(templateName);
                
                File templateFile = new File(templateName);
                Assert.assertTrue(templateFile.exists(), "Template should be created: " + templateName);
                
                log.info("✅ Generated template: {}", templateName);
            }
            
        } catch (Exception e) {
            log.error("❌ Error generating additional templates: {}", e.getMessage());
            Assert.fail("Additional template generation failed: " + e.getMessage());
        }
    }
    
    /**
     * Test summary
     */
    @Test(description = "Excel configuration test summary", dependsOnMethods = "generateAdditionalTemplates", priority = 9)
    public void testSummary() {
        log.info("=== EXCEL CONFIGURATION TEST SUMMARY ===");
        log.info("✅ Excel template generation - PASSED");
        log.info("✅ Configuration loading - PASSED");
        log.info("✅ Service and table configuration - PASSED");
        log.info("✅ Table endpoint configuration - PASSED");
        log.info("✅ Validation configuration - PASSED");
        log.info("✅ Configuration reload - PASSED");
        log.info("✅ Configuration summary - PASSED");
        log.info("✅ Additional templates - PASSED");
        log.info("=== ALL EXCEL CONFIGURATION TESTS PASSED ===");
        
        log.info("🎉 Excel-based configuration system is working perfectly!");
        log.info("📋 Next steps:");
        log.info("   1. Open config/Framework_Configuration.xlsx");
        log.info("   2. Update the configuration with your actual values");
        log.info("   3. Run the automated tests with Excel configuration");
        
        // Print final configuration summary
        configManager.printConfigurationSummary();
        
        Assert.assertTrue(true, "All Excel configuration tests passed successfully");
    }
}
