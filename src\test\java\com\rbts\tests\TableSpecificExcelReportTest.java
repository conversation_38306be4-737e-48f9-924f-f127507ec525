package com.rbts.tests;

import com.rbts.reporting.ServiceSpecificTestExecutionReporter;
import com.rbts.reporting.TestCaseResult;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;

/**
 * Test class to demonstrate table-specific Excel reporting functionality
 * Creates separate Excel sheets for each table within services
 * Example: Order service has Order_Order_Test_Results and Order_bundle_products_Test_Results sheets
 */
@Slf4j
public class TableSpecificExcelReportTest {
    
    private ServiceSpecificTestExecutionReporter tableReporter;
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up Table-Specific Excel Report Test");
        
        // Create reports directory if it doesn't exist
        File reportsDir = new File("reports");
        if (!reportsDir.exists()) {
            reportsDir.mkdirs();
            log.info("📁 Created reports directory");
        }
        
        // Delete existing report file to start fresh
        File existingReport = new File("reports/Service_Specific_Test_Execution_Report.xlsx");
        if (existingReport.exists()) {
            existingReport.delete();
            log.info("🗑️ Deleted existing table-specific report file");
        }
        
        // Initialize table-specific test reporter
        tableReporter = new ServiceSpecificTestExecutionReporter();
        
        log.info("✅ Table-Specific Excel Report Test setup completed");
        log.info("📊 Report will be generated at: {}", tableReporter.getReportFilePath());
    }
    
    /**
     * Test Order service - Order table reporting
     */
    @Test(description = "Test Order service Order table reporting to separate Excel sheet")
    public void testOrderServiceOrderTableReporting() {
        log.info("🧪 Testing Order service - Order table reporting");
        
        // Order creation success
        TestCaseResult orderSuccess = TestCaseResult.builder()
                .tableName("Order.Order")
                .operation("POST")
                .testCase("Create Order - Valid Customer and Products")
                .expectedResult("Status: 201, Order created successfully")
                .actualResult("Status: 201, Order created with ID: 12345")
                .status("PASS")
                .requestBody("{\"customerId\": 1, \"orderDate\": \"2024-01-01T10:00:00Z\"}")
                .responseBody("{\"id\": 12345, \"customerId\": 1, \"total\": 59.98}")
                .statusCode(201)
                .build();
        
        tableReporter.reportTestCaseWithColorCodingForService("Order", orderSuccess);
        
        // Order constraint violation
        TestCaseResult orderConstraint = TestCaseResult.builder()
                .tableName("Order.Order")
                .operation("POST")
                .testCase("Constraint Violation - Null Customer ID")
                .expectedResult("Status: 400, Customer ID cannot be null")
                .actualResult("Status: 400, Customer ID is required")
                .status("PASS")
                .requestBody("{\"customerId\": null, \"orderDate\": \"2024-01-01T10:00:00Z\"}")
                .responseBody("{\"error\": \"Validation failed\", \"message\": \"Customer ID is required\"}")
                .statusCode(400)
                .build();
        
        tableReporter.reportTestCaseWithColorCodingForService("Order", orderConstraint);
        
        log.info("✅ Order service Order table test cases reported to Order_Order_Test_Results sheet");
    }
    
    /**
     * Test Order service - bundle_products table reporting
     */
    @Test(description = "Test Order service bundle_products table reporting to separate Excel sheet")
    public void testOrderServiceBundleProductsTableReporting() {
        log.info("🧪 Testing Order service - bundle_products table reporting");
        
        // Bundle product creation success
        TestCaseResult bundleProductSuccess = TestCaseResult.builder()
                .tableName("Order.bundle_products")
                .operation("POST")
                .testCase("Create Bundle Product - Valid Bundle Configuration")
                .expectedResult("Status: 201, Bundle product created successfully")
                .actualResult("Status: 201, Bundle product created with ID: 67890")
                .status("PASS")
                .requestBody("{\"bundleName\": \"Gaming Bundle\", \"products\": [{\"productId\": 1, \"quantity\": 1}, {\"productId\": 2, \"quantity\": 1}], \"discountPercentage\": 15}")
                .responseBody("{\"id\": 67890, \"bundleName\": \"Gaming Bundle\", \"totalPrice\": 149.99, \"discountedPrice\": 127.49}")
                .statusCode(201)
                .build();
        
        tableReporter.reportTestCaseWithColorCodingForService("Order", bundleProductSuccess);
        
        // Bundle product validation error
        TestCaseResult bundleProductValidation = TestCaseResult.builder()
                .tableName("Order.bundle_products")
                .operation("POST")
                .testCase("Validation Error - Empty Product List")
                .expectedResult("Status: 400, Bundle must contain at least one product")
                .actualResult("Status: 400, Products list cannot be empty")
                .status("PASS")
                .requestBody("{\"bundleName\": \"Empty Bundle\", \"products\": [], \"discountPercentage\": 10}")
                .responseBody("{\"error\": \"Validation failed\", \"message\": \"Products list cannot be empty\"}")
                .statusCode(400)
                .build();
        
        tableReporter.reportTestCaseWithColorCodingForService("Order", bundleProductValidation);
        
        // Bundle product failure case
        TestCaseResult bundleProductFailure = TestCaseResult.builder()
                .tableName("Order.bundle_products")
                .operation("PUT")
                .testCase("Update Bundle Product - Invalid Discount")
                .expectedResult("Status: 400, Discount percentage must be between 0 and 100")
                .actualResult("Status: 500, Internal server error")
                .status("FAIL")
                .requestBody("{\"id\": 1, \"bundleName\": \"Updated Bundle\", \"discountPercentage\": 150}")
                .responseBody("{\"error\": \"Internal server error\"}")
                .statusCode(500)
                .errorMessage("Expected 400 but got 500")
                .build();
        
        tableReporter.reportTestCaseWithColorCodingForService("Order", bundleProductFailure);
        
        log.info("✅ Order service bundle_products table test cases reported to Order_bundle_products_Test_Results sheet");
    }
    
    /**
     * Test Order service - OrderItem table reporting
     */
    @Test(description = "Test Order service OrderItem table reporting to separate Excel sheet")
    public void testOrderServiceOrderItemTableReporting() {
        log.info("🧪 Testing Order service - OrderItem table reporting");
        
        // OrderItem creation success
        TestCaseResult orderItemSuccess = TestCaseResult.builder()
                .tableName("Order.OrderItem")
                .operation("POST")
                .testCase("Create Order Item - Valid Product and Quantity")
                .expectedResult("Status: 201, Order item created successfully")
                .actualResult("Status: 201, Order item created with ID: 111")
                .status("PASS")
                .requestBody("{\"orderId\": 1, \"productId\": 1, \"quantity\": 2, \"unitPrice\": 29.99}")
                .responseBody("{\"id\": 111, \"orderId\": 1, \"productId\": 1, \"quantity\": 2, \"totalPrice\": 59.98}")
                .statusCode(201)
                .build();
        
        tableReporter.reportTestCaseWithColorCodingForService("Order", orderItemSuccess);
        
        // OrderItem foreign key violation
        TestCaseResult orderItemFK = TestCaseResult.builder()
                .tableName("Order.OrderItem")
                .operation("POST")
                .testCase("Foreign Key Violation - Invalid Product ID")
                .expectedResult("Status: 502, Product service unavailable or product ID not found")
                .actualResult("Status: 502, Product service error")
                .status("PASS")
                .requestBody("{\"orderId\": 1, \"productId\": 999, \"quantity\": 1}")
                .responseBody("{\"error\": \"External service error\", \"message\": \"Product service unavailable\"}")
                .statusCode(502)
                .build();
        
        tableReporter.reportTestCaseWithColorCodingForService("Order", orderItemFK);
        
        log.info("✅ Order service OrderItem table test cases reported to Order_OrderItem_Test_Results sheet");
    }
    
    /**
     * Test Authentication service - User table reporting
     */
    @Test(description = "Test Authentication service User table reporting to separate Excel sheet")
    public void testAuthenticationServiceUserTableReporting() {
        log.info("🧪 Testing Authentication service - User table reporting");
        
        // User creation success
        TestCaseResult userSuccess = TestCaseResult.builder()
                .tableName("Authentication.User")
                .operation("POST")
                .testCase("Create User - Valid Email and Password")
                .expectedResult("Status: 201, User created successfully")
                .actualResult("Status: 201, User created with ID: 456")
                .status("PASS")
                .requestBody("{\"email\": \"<EMAIL>\", \"firstName\": \"John\", \"password\": \"SecurePass123!\"}")
                .responseBody("{\"id\": 456, \"email\": \"<EMAIL>\", \"firstName\": \"John\"}")
                .statusCode(201)
                .build();
        
        tableReporter.reportTestCaseWithColorCodingForService("Authentication", userSuccess);
        
        log.info("✅ Authentication service User table test cases reported to Authentication_User_Test_Results sheet");
    }
    
    /**
     * Test Authentication service - UserProfile table reporting
     */
    @Test(description = "Test Authentication service UserProfile table reporting to separate Excel sheet")
    public void testAuthenticationServiceUserProfileTableReporting() {
        log.info("🧪 Testing Authentication service - UserProfile table reporting");
        
        // UserProfile creation success
        TestCaseResult userProfileSuccess = TestCaseResult.builder()
                .tableName("Authentication.UserProfile")
                .operation("POST")
                .testCase("Create User Profile - Valid User Data")
                .expectedResult("Status: 201, User profile created successfully")
                .actualResult("Status: 201, User profile created with ID: 789")
                .status("PASS")
                .requestBody("{\"userId\": 1, \"firstName\": \"John\", \"lastName\": \"Doe\", \"phoneNumber\": \"+1234567890\"}")
                .responseBody("{\"id\": 789, \"userId\": 1, \"firstName\": \"John\", \"lastName\": \"Doe\"}")
                .statusCode(201)
                .build();
        
        tableReporter.reportTestCaseWithColorCodingForService("Authentication", userProfileSuccess);
        
        log.info("✅ Authentication service UserProfile table test cases reported to Authentication_UserProfile_Test_Results sheet");
    }
    
    /**
     * Table-specific Excel reporting test summary
     */
    @Test(description = "Table-specific Excel reporting test summary", 
          dependsOnMethods = {"testOrderServiceOrderTableReporting", "testOrderServiceBundleProductsTableReporting", 
                             "testOrderServiceOrderItemTableReporting", "testAuthenticationServiceUserTableReporting", 
                             "testAuthenticationServiceUserProfileTableReporting"})
    public void tableSpecificExcelReportingTestSummary() {
        log.info("📊 TABLE-SPECIFIC EXCEL REPORTING TEST SUMMARY");
        log.info("===============================================");
        
        int totalTestCases = tableReporter.getTotalTestCases();
        int totalDefects = tableReporter.getTotalDefects();
        String reportFile = tableReporter.getReportFilePath();
        
        log.info("📋 Total Test Cases Reported: {}", totalTestCases);
        log.info("🐛 Total Defects Generated: {}", totalDefects);
        log.info("📁 Excel Report File: {}", reportFile);
        
        // Check if report file exists
        File reportFileObj = new File(reportFile);
        if (reportFileObj.exists()) {
            log.info("✅ Table-specific Excel report file created successfully");
            log.info("📊 File size: {} bytes", reportFileObj.length());
        } else {
            log.error("❌ Table-specific Excel report file not found!");
        }
        
        log.info("");
        log.info("📊 TABLE-SPECIFIC EXCEL SHEETS CREATED:");
        log.info("1. 📋 Order_Order_Test_Results - Order service Order table (2 test cases)");
        log.info("2. 📦 Order_bundle_products_Test_Results - Order service bundle_products table (3 test cases, 1 defect)");
        log.info("3. 📝 Order_OrderItem_Test_Results - Order service OrderItem table (2 test cases)");
        log.info("4. 👤 Authentication_User_Test_Results - Authentication service User table (1 test case)");
        log.info("5. 📄 Authentication_UserProfile_Test_Results - Authentication service UserProfile table (1 test case)");
        
        log.info("");
        log.info("🎯 TABLE-SPECIFIC FEATURES DEMONSTRATED:");
        log.info("✅ Separate Excel sheets for each table within services");
        log.info("✅ Table-specific test case IDs (TC_ServiceName_TableName_Operation_001)");
        log.info("✅ Table-specific defect IDs (D_ServiceName_TableName_Operation_001)");
        log.info("✅ Independent counters for each table");
        log.info("✅ Automatic sheet creation when new table is tested");
        log.info("✅ Color coding within each table sheet");
        log.info("✅ Complete test data recording per table");
        
        log.info("");
        log.info("📋 EXCEL REPORT STRUCTURE:");
        log.info("File: Service_Specific_Test_Execution_Report.xlsx");
        log.info("├── Order_Order_Test_Results");
        log.info("│   ├── TC_Order_Order_POST_001 (PASS)");
        log.info("│   └── TC_Order_Order_POST_002 (PASS)");
        log.info("├── Order_bundle_products_Test_Results");
        log.info("│   ├── TC_Order_bundle_products_POST_001 (PASS)");
        log.info("│   ├── TC_Order_bundle_products_POST_002 (PASS)");
        log.info("│   └── TC_Order_bundle_products_PUT_003 (FAIL) - D_Order_bundle_products_PUT_001");
        log.info("├── Order_OrderItem_Test_Results");
        log.info("│   ├── TC_Order_OrderItem_POST_001 (PASS)");
        log.info("│   └── TC_Order_OrderItem_POST_002 (PASS)");
        log.info("├── Authentication_User_Test_Results");
        log.info("│   └── TC_Authentication_User_POST_001 (PASS)");
        log.info("└── Authentication_UserProfile_Test_Results");
        log.info("    └── TC_Authentication_UserProfile_POST_001 (PASS)");
        
        log.info("");
        log.info("🚀 BENEFITS OF TABLE-SPECIFIC REPORTING:");
        log.info("1. 🎯 Clear separation of test results by table within services");
        log.info("2. 📊 Easy table-specific analysis and metrics");
        log.info("3. 🔍 Independent defect tracking per table");
        log.info("4. 📋 Table teams can focus on their specific table results");
        log.info("5. 🎛️ Scalable - automatically creates sheets for new tables");
        log.info("6. 📈 Table-specific pass/fail ratios");
        log.info("7. 🔄 Easy to update existing table sheets");
        log.info("8. 🏢 Service organization with table granularity");
        
        log.info("");
        log.info("📋 EXAMPLE: ORDER SERVICE TABLE SEPARATION:");
        log.info("✅ Order.Order tests → Order_Order_Test_Results sheet");
        log.info("✅ Order.bundle_products tests → Order_bundle_products_Test_Results sheet");
        log.info("✅ Order.OrderItem tests → Order_OrderItem_Test_Results sheet");
        log.info("📊 Each table has independent test case numbering and defect tracking");
        
        log.info("");
        log.info("📋 HOW TO VIEW TABLE-SPECIFIC RESULTS:");
        log.info("1. Open Excel file: {}", reportFile);
        log.info("2. Click on table-specific sheet tabs at the bottom");
        log.info("3. Each sheet contains only that table's test results");
        log.info("4. Review color-coded results (Green=PASS, Red=FAIL)");
        log.info("5. Check table-specific defect IDs for failures");
        
        log.info("");
        log.info("🎯 PERFECT SOLUTION FOR YOUR BUNDLE PRODUCTS TESTING:");
        log.info("✅ Order.bundle_products gets its own dedicated sheet");
        log.info("✅ Test case IDs: TC_Order_bundle_products_POST_001, TC_Order_bundle_products_PUT_002, etc.");
        log.info("✅ Defect IDs: D_Order_bundle_products_POST_001, D_Order_bundle_products_PUT_002, etc.");
        log.info("✅ Independent from Order.Order table testing");
        log.info("✅ Clear separation and organization");
        
        log.info("");
        log.info("✅ TABLE-SPECIFIC EXCEL REPORTING TEST COMPLETED SUCCESSFULLY!");
        log.info("📊 {} test cases across {} tables with {} defects!", totalTestCases, 5, totalDefects);
        log.info("🎯 Bundle products now has its own dedicated Excel sheet for reporting!");
    }
}
