package com.rbts.tests;

import com.rbts.config.ExcelConfigManager;
import com.rbts.config.ExcelConfigTemplateGenerator;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;

/**
 * Comprehensive test for the complete framework functionality
 * Tests both null endpoint skipping and constraint validation status codes
 */
@Slf4j
public class ComprehensiveFrameworkTest {
    
    private ExcelConfigManager configManager;
    private ExcelConfigTemplateGenerator templateGenerator;
    
    @BeforeClass
    public void setUp() {
        templateGenerator = new ExcelConfigTemplateGenerator();
        log.info("Comprehensive Framework Test initialized");
    }
    
    /**
     * Test complete framework functionality
     */
    @Test(description = "Test complete framework functionality", priority = 1)
    public void testCompleteFrameworkFunctionality() {
        log.info("Testing complete framework functionality");
        
        try {
            // Generate Excel template
            templateGenerator.generateConfigurationTemplate();
            
            // Initialize config manager
            configManager = ExcelConfigManager.getInstance();
            
            log.info("=== TESTING FRAMEWORK FEATURES ===");
            
            // 1. Test getAll column functionality
            testGetAllColumnFunctionality();
            
            // 2. Test null endpoint skipping
            testNullEndpointSkipping();
            
            // 3. Test constraint validation status codes
            testConstraintValidationStatusCodes();
            
            // 4. Test service-based foreign key validation
            testServiceBasedForeignKeyValidation();
            
            // 5. Test complete configuration summary
            testConfigurationSummary();
            
            log.info("=== ALL FRAMEWORK FEATURES WORKING PERFECTLY ===");
            
        } catch (Exception e) {
            log.error("❌ Error testing complete framework functionality: {}", e.getMessage());
            Assert.fail("Complete framework functionality test failed: " + e.getMessage());
        }
    }
    
    private void testGetAllColumnFunctionality() {
        log.info("🔍 Testing getAll column functionality...");
        
        // Test that getAll is included in operations
        String operations = configManager.getGeneralConfig("auto.test.operations");
        Assert.assertTrue(operations.contains("getall"), "Operations should include getall");
        
        // Test getAll validation status code
        String getAllStatus = configManager.getValidationConfig("validation.status_code.getall");
        Assert.assertEquals(getAllStatus, "200", "getAll status code should be 200");
        
        // Test getAll endpoint for a table
        String getAllEndpoint = configManager.getTableEndpoint("AddressType", "getall");
        Assert.assertNotNull(getAllEndpoint, "getAll endpoint should be available");
        Assert.assertTrue(getAllEndpoint.contains("getAll"), "getAll endpoint should contain 'getAll'");
        
        log.info("✅ getAll column functionality working correctly");
    }
    
    private void testNullEndpointSkipping() {
        log.info("🔍 Testing null endpoint skipping...");
        
        // Test ExampleTable which has null endpoints
        List<String> operationsToTest = configManager.getOperationsToTestForTable("ExampleTable");
        
        // Should include operations with endpoints
        Assert.assertTrue(operationsToTest.contains("post"), "POST should be tested");
        Assert.assertTrue(operationsToTest.contains("get"), "GET should be tested");
        Assert.assertTrue(operationsToTest.contains("getall"), "GET ALL should be tested");
        
        // Should skip operations with null/empty endpoints
        Assert.assertFalse(operationsToTest.contains("put"), "PUT should be skipped (empty endpoint)");
        Assert.assertFalse(operationsToTest.contains("patch"), "PATCH should be skipped (null endpoint)");
        Assert.assertFalse(operationsToTest.contains("delete"), "DELETE should be skipped (empty endpoint)");
        
        log.info("✅ Null endpoint skipping working correctly");
        log.info("   Operations to test for ExampleTable: {}", operationsToTest);
    }
    
    private void testConstraintValidationStatusCodes() {
        log.info("🔍 Testing constraint validation status codes...");
        
        // Test specific constraint status codes
        String uniqueStatus = configManager.getValidationConfig("validation.unique_constraint.expected_status");
        Assert.assertEquals(uniqueStatus, "701", "Unique constraint should return 701");
        
        String nullStatus = configManager.getValidationConfig("validation.null_constraint.expected_status");
        Assert.assertEquals(nullStatus, "700", "Null constraint should return 700");
        
        String foreignKeySameStatus = configManager.getValidationConfig("validation.foreign_key_same_service.expected_status");
        Assert.assertEquals(foreignKeySameStatus, "404", "Foreign key same service should return 404");
        
        String foreignKeyOtherStatus = configManager.getValidationConfig("validation.foreign_key_other_service.expected_status");
        Assert.assertEquals(foreignKeyOtherStatus, "702", "Foreign key other service should return 702");
        
        log.info("✅ Constraint validation status codes working correctly");
        log.info("   Unique: {}, Null: {}, FK Same: {}, FK Other: {}", 
                uniqueStatus, nullStatus, foreignKeySameStatus, foreignKeyOtherStatus);
    }
    
    private void testServiceBasedForeignKeyValidation() {
        log.info("🔍 Testing service-based foreign key validation...");
        
        // Test same service foreign key (Order -> OrderStatus)
        String sameServiceStatus = configManager.getConstraintViolationStatusCodeForTable("foreign_key_invalid", "Order", "OrderStatus");
        Assert.assertEquals(sameServiceStatus, "404", "Same service FK should return 404");
        
        // Test different service foreign key (Order -> User)
        String differentServiceStatus = configManager.getConstraintViolationStatusCodeForTable("foreign_key_invalid", "Order", "User");
        Assert.assertEquals(differentServiceStatus, "702", "Different service FK should return 702");
        
        // Test constraint violation expectation
        boolean sameServiceExpected = configManager.isConstraintViolationExpected("foreign_key_invalid", 404, "Order", "OrderStatus");
        Assert.assertTrue(sameServiceExpected, "Status 404 should be expected for same service FK");
        
        boolean differentServiceExpected = configManager.isConstraintViolationExpected("foreign_key_invalid", 702, "Order", "User");
        Assert.assertTrue(differentServiceExpected, "Status 702 should be expected for different service FK");
        
        log.info("✅ Service-based foreign key validation working correctly");
        log.info("   Order -> OrderStatus (same service): {}", sameServiceStatus);
        log.info("   Order -> User (different service): {}", differentServiceStatus);
    }
    
    private void testConfigurationSummary() {
        log.info("🔍 Testing configuration summary...");
        
        // Print complete configuration summary
        configManager.printConfigurationSummary();
        
        // Verify configuration counts
        Assert.assertTrue(configManager.getAllConfiguredTables().size() > 0, "Should have configured tables");
        Assert.assertTrue(configManager.getServiceNames().size() > 0, "Should have configured services");
        
        log.info("✅ Configuration summary working correctly");
    }
    
    /**
     * Test summary and demonstration
     */
    @Test(description = "Framework demonstration and summary", dependsOnMethods = "testCompleteFrameworkFunctionality", priority = 2)
    public void frameworkDemonstrationAndSummary() {
        log.info("=== COMPREHENSIVE FRAMEWORK TEST SUMMARY ===");
        log.info("✅ getAll column functionality - PASSED");
        log.info("✅ Null endpoint skipping - PASSED");
        log.info("✅ Constraint validation status codes - PASSED");
        log.info("✅ Service-based foreign key validation - PASSED");
        log.info("✅ Configuration summary - PASSED");
        log.info("=== ALL COMPREHENSIVE TESTS PASSED ===");
        
        log.info("");
        log.info("🎉 FRAMEWORK FEATURES SUMMARY:");
        log.info("");
        
        log.info("📊 1. ENHANCED TABLE ENDPOINTS:");
        log.info("   ✅ Added getAll column to Table_Endpoints sheet");
        log.info("   ✅ Support for 6 CRUD operations: POST, PUT, PATCH, GET, GET ALL, DELETE");
        log.info("   ✅ Null/empty endpoint skipping for fine-grained control");
        log.info("");
        
        log.info("🎯 2. SMART OPERATION SKIPPING:");
        log.info("   ✅ Leave endpoint cell empty → Skip testing that operation");
        log.info("   ✅ Enter 'null' → Skip testing that operation");
        log.info("   ✅ Enter endpoint → Test that operation");
        log.info("   ✅ No table configuration → Test all operations (defaults)");
        log.info("");
        
        log.info("🔴 3. CONSTRAINT VALIDATION STATUS CODES:");
        log.info("   ✅ Unique Constraint Violation: 701");
        log.info("   ✅ Null Constraint Violation: 700");
        log.info("   ✅ Foreign Key Same Service: 404");
        log.info("   ✅ Foreign Key Other Service: 702");
        log.info("");
        
        log.info("🏗️ 4. SERVICE-AWARE VALIDATION:");
        log.info("   ✅ Automatic service detection for each table");
        log.info("   ✅ Smart foreign key validation based on service relationship");
        log.info("   ✅ Different status codes for same vs different service FK violations");
        log.info("");
        
        log.info("📋 5. EXCEL-BASED CONFIGURATION:");
        log.info("   ✅ Easy configuration through Excel sheets");
        log.info("   ✅ No code changes required for configuration updates");
        log.info("   ✅ Visual and intuitive configuration management");
        log.info("   ✅ Comprehensive validation rules and endpoint management");
        log.info("");
        
        log.info("🚀 6. REAL-WORLD EXAMPLES:");
        log.info("");
        log.info("   📝 Example 1 - Read-Only Table:");
        log.info("      Table: AuditLog");
        log.info("      POST: (empty) ← No creation");
        log.info("      PUT: (empty) ← No updates");
        log.info("      GET: /audit/api/AuditLog/getById ← Read by ID");
        log.info("      GET ALL: /audit/api/AuditLog/getAll ← Read all");
        log.info("      DELETE: (empty) ← No deletion");
        log.info("      Result: Only GET and GET ALL operations tested");
        log.info("");
        
        log.info("   📝 Example 2 - Cross-Service Foreign Key:");
        log.info("      Table: Order (order service)");
        log.info("      Foreign Key: User (authentication service)");
        log.info("      Expected Status: 702 (different service)");
        log.info("");
        
        log.info("   📝 Example 3 - Same-Service Foreign Key:");
        log.info("      Table: OrderItem (order service)");
        log.info("      Foreign Key: Order (order service)");
        log.info("      Expected Status: 404 (same service)");
        log.info("");
        
        log.info("🎯 NEXT STEPS:");
        log.info("1. Open config/Framework_Configuration.xlsx");
        log.info("2. Configure your actual endpoints and validation rules");
        log.info("3. Set null/empty endpoints for operations you don't want to test");
        log.info("4. Run automated tests with your configuration");
        log.info("5. Framework will automatically handle all the smart validation!");
        
        Assert.assertTrue(true, "All comprehensive framework tests passed successfully");
    }
}
