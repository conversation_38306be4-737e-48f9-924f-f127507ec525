package com.rbts.tests;

import com.rbts.reporting.FilteredServiceSpecificReporter;
import com.rbts.reporting.TestCaseResult;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.sql.*;

/**
 * POST test that actually inserts data into the database
 * Tests POST operations with real database insertion
 */
@Slf4j
public class PostWithDatabaseInsertionTest {
    
    private FilteredServiceSpecificReporter reporter;
    private Connection databaseConnection;
    
    // Database configuration
    private final String DB_URL = "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;INIT=CREATE SCHEMA IF NOT EXISTS test";
    private final String DB_USERNAME = "sa";
    private final String DB_PASSWORD = "";
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up POST Test with Database Insertion");
        
        try {
            // Initialize reporter
            reporter = new FilteredServiceSpecificReporter();
            
            // Setup database connection and tables
            setupDatabase();
            
            log.info("✅ POST Test with Database Insertion setup completed");
            log.info("📊 Report will be generated at: {}", reporter.getReportFilePath());
            
        } catch (Exception e) {
            log.error("❌ Error setting up POST test with database: {}", e.getMessage());
        }
    }
    
    /**
     * Setup database with actual tables
     */
    private void setupDatabase() {
        try {
            // Load H2 driver
            Class.forName("org.h2.Driver");
            
            // Create database connection
            databaseConnection = DriverManager.getConnection(DB_URL, DB_USERNAME, DB_PASSWORD);
            
            // Create bundle_products table
            String createBundleProductsTable = """
                CREATE TABLE IF NOT EXISTS bundle_products (
                    bundle_id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    bundle_name VARCHAR(100) NOT NULL UNIQUE,
                    bundle_description TEXT,
                    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by VARCHAR(50)
                )
                """;
            
            // Create users table
            String createUsersTable = """
                CREATE TABLE IF NOT EXISTS users (
                    user_id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    email_address VARCHAR(100) NOT NULL UNIQUE,
                    password_hash VARCHAR(255) NOT NULL,
                    first_name VARCHAR(50),
                    last_name VARCHAR(50),
                    phone_number VARCHAR(20),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """;
            
            // Create orders table
            String createOrdersTable = """
                CREATE TABLE IF NOT EXISTS orders (
                    order_id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    customer_id BIGINT NOT NULL,
                    order_date TIMESTAMP NOT NULL,
                    order_status VARCHAR(20) DEFAULT 'PENDING',
                    total_amount DECIMAL(10,2) DEFAULT 0.00,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_modified_at TIMESTAMP
                )
                """;
            
            Statement statement = databaseConnection.createStatement();
            statement.execute(createBundleProductsTable);
            statement.execute(createUsersTable);
            statement.execute(createOrdersTable);
            
            log.info("✅ Database tables created successfully");
            log.info("🗄️ Tables: bundle_products, users, orders");
            
        } catch (Exception e) {
            log.error("❌ Error setting up database: {}", e.getMessage());
            log.info("💡 Make sure H2 database dependency is added to pom.xml");
        }
    }
    
    /**
     * Test POST operation for bundle_products with actual database insertion
     */
    @Test(description = "POST test for bundle_products with database insertion")
    public void testBundleProductsPostWithDatabaseInsertion() {
        log.info("🧪 Testing bundle_products POST with actual database insertion");
        
        // Test 1: Successful POST with database insertion
        testBundleProductsPostSuccessWithDB();
        
        // Test 2: Null constraint violation
        testBundleProductsPostNullConstraintWithDB();
        
        // Test 3: Unique constraint violation
        testBundleProductsPostUniqueConstraintWithDB();
        
        log.info("✅ bundle_products POST tests with database insertion completed");
    }
    
    /**
     * Test successful POST for bundle_products with actual database insertion
     */
    private void testBundleProductsPostSuccessWithDB() {
        try {
            log.info("📝 Testing bundle_products POST success with database insertion");
            
            // Generate POST payload with actual database field names
            JSONObject requestPayload = new JSONObject();
            long timestamp = System.currentTimeMillis() % 10000;
            
            requestPayload.put("bundle_name", "Gaming Bundle " + timestamp);
            requestPayload.put("bundle_description", "Complete gaming setup with premium accessories");
            requestPayload.put("discount_percentage", 15.0);
            requestPayload.put("is_active", true);
            
            // Actually insert data into database
            Long insertedId = insertBundleProductIntoDatabase(requestPayload);
            
            // Verify data was inserted by querying database
            JSONObject insertedData = getBundleProductFromDatabase(insertedId);
            
            boolean isSuccess = insertedId != null && insertedData.length() > 0;
            
            // Record test result
            TestCaseResult testResult = TestCaseResult.builder()
                    .tableName("Order.bundle_products")
                    .operation("POST")
                    .testCase("Create Bundle Product - Database Insertion Success")
                    .expectedResult("Status: 201, Bundle product created and inserted into database")
                    .actualResult(isSuccess ? 
                        "Status: 201, Bundle product created with ID: " + insertedId + " and verified in database" :
                        "Status: 500, Failed to insert bundle product into database")
                    .status(isSuccess ? "PASS" : "FAIL")
                    .requestBody(requestPayload.toString())
                    .responseBody(insertedData.toString())
                    .statusCode(isSuccess ? 201 : 500)
                    .build();
            
            reporter.reportTestCaseWithColorCodingForService("Order", testResult);
            
            log.info("✅ bundle_products POST success test completed - ID: {} - Database verified: {}", 
                    insertedId, isSuccess);
            
            // Log database verification
            if (isSuccess) {
                log.info("🗄️ Database verification successful:");
                log.info("   - Inserted ID: {}", insertedId);
                log.info("   - Bundle Name: {}", insertedData.optString("bundle_name"));
                log.info("   - Description: {}", insertedData.optString("bundle_description"));
                log.info("   - Discount: {}%", insertedData.optDouble("discount_percentage"));
                log.info("   - Active: {}", insertedData.optBoolean("is_active"));
                log.info("   - Created At: {}", insertedData.optString("created_at"));
            }
            
        } catch (Exception e) {
            log.error("❌ Error in bundle_products POST success test with database: {}", e.getMessage());
            recordFailedTest("POST", "bundle_products", "Database insertion failed: " + e.getMessage());
        }
    }
    
    /**
     * Test null constraint violation for bundle_products POST
     */
    private void testBundleProductsPostNullConstraintWithDB() {
        try {
            log.info("📝 Testing bundle_products POST null constraint with database");
            
            // Generate POST payload with null constraint violation
            JSONObject requestPayload = new JSONObject();
            requestPayload.put("bundle_name", JSONObject.NULL); // This will cause constraint violation
            requestPayload.put("bundle_description", "Test description");
            requestPayload.put("discount_percentage", 10.0);
            requestPayload.put("is_active", true);
            
            // Try to insert data into database (should fail)
            boolean constraintViolationOccurred = false;
            String errorMessage = "";
            
            try {
                insertBundleProductIntoDatabase(requestPayload);
            } catch (SQLException e) {
                constraintViolationOccurred = true;
                errorMessage = e.getMessage();
                log.info("✅ Expected constraint violation occurred: {}", errorMessage);
            }
            
            boolean isExpectedFailure = constraintViolationOccurred;
            
            // Record test result
            TestCaseResult testResult = TestCaseResult.builder()
                    .tableName("Order.bundle_products")
                    .operation("POST")
                    .testCase("Create Bundle Product - Null Constraint Violation (Database)")
                    .expectedResult("Status: 400, Database constraint violation for null bundle_name")
                    .actualResult(isExpectedFailure ? 
                        "Status: 400, Database constraint violation: " + errorMessage :
                        "Status: 201, Unexpected success - constraint not enforced")
                    .status(isExpectedFailure ? "PASS" : "FAIL")
                    .requestBody(requestPayload.toString())
                    .responseBody(isExpectedFailure ? 
                        "{\"error\": \"Database constraint violation\", \"message\": \"" + errorMessage + "\"}" :
                        "{\"error\": \"Constraint validation failed\"}")
                    .statusCode(isExpectedFailure ? 400 : 201)
                    .build();
            
            reporter.reportTestCaseWithColorCodingForService("Order", testResult);
            
            log.info("✅ bundle_products POST null constraint test completed - Constraint enforced: {}", 
                    isExpectedFailure);
            
        } catch (Exception e) {
            log.error("❌ Error in bundle_products POST null constraint test: {}", e.getMessage());
        }
    }
    
    /**
     * Test unique constraint violation for bundle_products POST
     */
    private void testBundleProductsPostUniqueConstraintWithDB() {
        try {
            log.info("📝 Testing bundle_products POST unique constraint with database");
            
            // First, insert a bundle product to create existing data
            JSONObject firstPayload = new JSONObject();
            String uniqueBundleName = "Unique Bundle " + System.currentTimeMillis() % 1000;
            firstPayload.put("bundle_name", uniqueBundleName);
            firstPayload.put("bundle_description", "First bundle");
            firstPayload.put("discount_percentage", 10.0);
            firstPayload.put("is_active", true);
            
            Long firstId = insertBundleProductIntoDatabase(firstPayload);
            log.info("🗄️ Inserted first bundle product with ID: {} and name: {}", firstId, uniqueBundleName);
            
            // Now try to insert duplicate bundle name (should fail)
            JSONObject duplicatePayload = new JSONObject();
            duplicatePayload.put("bundle_name", uniqueBundleName); // Same name - unique constraint violation
            duplicatePayload.put("bundle_description", "Duplicate bundle");
            duplicatePayload.put("discount_percentage", 5.0);
            duplicatePayload.put("is_active", true);
            
            boolean constraintViolationOccurred = false;
            String errorMessage = "";
            
            try {
                insertBundleProductIntoDatabase(duplicatePayload);
            } catch (SQLException e) {
                constraintViolationOccurred = true;
                errorMessage = e.getMessage();
                log.info("✅ Expected unique constraint violation occurred: {}", errorMessage);
            }
            
            boolean isExpectedFailure = constraintViolationOccurred;
            
            // Record test result
            TestCaseResult testResult = TestCaseResult.builder()
                    .tableName("Order.bundle_products")
                    .operation("POST")
                    .testCase("Create Bundle Product - Unique Constraint Violation (Database)")
                    .expectedResult("Status: 409, Database unique constraint violation for duplicate bundle_name")
                    .actualResult(isExpectedFailure ? 
                        "Status: 409, Database unique constraint violation: " + errorMessage :
                        "Status: 201, Unexpected success - unique constraint not enforced")
                    .status(isExpectedFailure ? "PASS" : "FAIL")
                    .requestBody(duplicatePayload.toString())
                    .responseBody(isExpectedFailure ? 
                        "{\"error\": \"Database unique constraint violation\", \"message\": \"" + errorMessage + "\"}" :
                        "{\"error\": \"Constraint validation failed\"}")
                    .statusCode(isExpectedFailure ? 409 : 201)
                    .build();
            
            reporter.reportTestCaseWithColorCodingForService("Order", testResult);
            
            log.info("✅ bundle_products POST unique constraint test completed - Constraint enforced: {}", 
                    isExpectedFailure);
            
        } catch (Exception e) {
            log.error("❌ Error in bundle_products POST unique constraint test: {}", e.getMessage());
        }
    }
    
    /**
     * Test POST operation for User with actual database insertion
     */
    @Test(description = "POST test for User with database insertion")
    public void testUserPostWithDatabaseInsertion() {
        log.info("🧪 Testing User POST with actual database insertion");
        
        testUserPostSuccessWithDB();
        
        log.info("✅ User POST tests with database insertion completed");
    }
    
    /**
     * Test successful POST for User with actual database insertion
     */
    private void testUserPostSuccessWithDB() {
        try {
            log.info("📝 Testing User POST success with database insertion");
            
            // Generate POST payload with actual database field names
            JSONObject requestPayload = new JSONObject();
            long timestamp = System.currentTimeMillis() % 10000;
            
            requestPayload.put("username", "testuser" + timestamp);
            requestPayload.put("email_address", "testuser" + timestamp + "@example.com");
            requestPayload.put("password_hash", "SecurePassword123!");
            requestPayload.put("first_name", "John");
            requestPayload.put("last_name", "Doe");
            requestPayload.put("phone_number", "+1234567890");
            requestPayload.put("is_active", true);
            
            // Actually insert data into database
            Long insertedId = insertUserIntoDatabase(requestPayload);
            
            // Verify data was inserted by querying database
            JSONObject insertedData = getUserFromDatabase(insertedId);
            
            boolean isSuccess = insertedId != null && insertedData.length() > 0;
            
            // Record test result
            TestCaseResult testResult = TestCaseResult.builder()
                    .tableName("Authentication.User")
                    .operation("POST")
                    .testCase("Create User - Database Insertion Success")
                    .expectedResult("Status: 201, User created and inserted into database")
                    .actualResult(isSuccess ? 
                        "Status: 201, User created with ID: " + insertedId + " and verified in database" :
                        "Status: 500, Failed to insert user into database")
                    .status(isSuccess ? "PASS" : "FAIL")
                    .requestBody(requestPayload.toString())
                    .responseBody(insertedData.toString())
                    .statusCode(isSuccess ? 201 : 500)
                    .build();
            
            reporter.reportTestCaseWithColorCodingForService("Authentication", testResult);
            
            log.info("✅ User POST success test completed - ID: {} - Database verified: {}", 
                    insertedId, isSuccess);
            
            // Log database verification
            if (isSuccess) {
                log.info("🗄️ Database verification successful:");
                log.info("   - Inserted ID: {}", insertedId);
                log.info("   - Username: {}", insertedData.optString("username"));
                log.info("   - Email: {}", insertedData.optString("email_address"));
                log.info("   - First Name: {}", insertedData.optString("first_name"));
                log.info("   - Last Name: {}", insertedData.optString("last_name"));
                log.info("   - Active: {}", insertedData.optBoolean("is_active"));
                log.info("   - Created At: {}", insertedData.optString("created_at"));
            }
            
        } catch (Exception e) {
            log.error("❌ Error in User POST success test with database: {}", e.getMessage());
            recordFailedTest("POST", "User", "Database insertion failed: " + e.getMessage());
        }
    }
