package com.rbts.crud;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rbts.api.ApiRequestHandler;
import com.rbts.comparison.JsonComparator;
import com.rbts.config.ConfigManager;
import com.rbts.constraint.ConstraintTestGenerator;
import com.rbts.database.DatabaseManager;
import com.rbts.defect.DefectManager;
import com.rbts.utils.ExcelUtils;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

/**
 * CRUD Test Executor for API Testing Framework
 * Handles complete CRUD operation testing with database validation
 */
@Slf4j
public class CrudTestExecutor {

    private final ConfigManager configManager;
    private final DatabaseManager databaseManager;
    private final DefectManager defectManager;
    private final JsonComparator jsonComparator;
    private final ConstraintTestGenerator constraintTestGenerator;
    private final ExcelUtils excelUtils;
    private final ObjectMapper objectMapper;
    private final ApiRequestHandler apiRequestHandler;

    public CrudTestExecutor() {
        this.configManager = ConfigManager.getInstance();
        this.databaseManager = new DatabaseManager();
        this.defectManager = new DefectManager();
        this.jsonComparator = new JsonComparator();
        this.constraintTestGenerator = new ConstraintTestGenerator();
        this.excelUtils = new ExcelUtils();
        this.objectMapper = new ObjectMapper();
        this.apiRequestHandler = new ApiRequestHandler();
    }
    
    /**
     * Execute POST operation test
     */
    public TestResult executePostTest(String entity, String filePath, String sheetName, int rowNum, 
                                    int urlColumn, int requestBodyColumn, int expectedResultColumn, 
                                    int actualResultColumn, int statusColumn) {
        
        TestResult result = new TestResult();
        result.setEntity(entity);
        result.setOperation("POST");
        result.setTestType("NORMAL");
        
        try {
            // Get test data from Excel
            String endpoint = excelUtils.getCellData(filePath, sheetName, rowNum, urlColumn);
            String requestBody = excelUtils.getCellData(filePath, sheetName, rowNum, requestBodyColumn);
            String expectedResult = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultColumn);
            
            // Validate inputs
            if (endpoint == null || endpoint.trim().isEmpty()) {
                endpoint = configManager.getApiEndpoint("post", entity);
            }
            
            if (requestBody == null || requestBody.trim().isEmpty()) {
                throw new IllegalArgumentException("Request body cannot be empty for POST operation");
            }
            
            // Construct full URL
            String baseUrl = configManager.getBaseUrl();
            String fullUrl = baseUrl + endpoint;
            
            log.info("Executing POST test for entity: {} at URL: {}", entity, fullUrl);
            
            // Execute API request using ApiRequestHandler
            Response response = apiRequestHandler.executeRequest(entity, "post", requestBody);
            result.setStatusCode(response.getStatusCode());
            result.setResponseBody(response.getBody().asString());
            
            // Check if request was successful (2xx status codes)
            if (response.getStatusCode() >= 200 && response.getStatusCode() < 300) {
                // Extract ID from response
                String extractedId = extractIdFromResponse(response.getBody().asString(), entity);
                
                if (extractedId != null) {
                    result.setExtractedId(extractedId);
                    
                    // Query database and get actual result
                    String actualResult = databaseManager.queryByIdAndConvertToJson(entity, extractedId);
                    result.setActualResult(actualResult);
                    
                    // Store actual result in Excel
                    excelUtils.setCellData(filePath, sheetName, rowNum, actualResultColumn, actualResult);
                    
                    // Compare expected vs actual
                    if (expectedResult != null && !expectedResult.trim().isEmpty()) {
                        JsonComparator.ComparisonResult comparisonResult = 
                            jsonComparator.compareJson(expectedResult, actualResult);
                        
                        result.setTestPassed(comparisonResult.isMatch());
                        result.setComparisonMessage(comparisonResult.getMessage());
                        result.setDifferences(comparisonResult.getDifferences());
                        
                        // Update status in Excel
                        String status = comparisonResult.isMatch() ? "PASSED" : "FAILED";
                        excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, status);
                        
                        if (!comparisonResult.isMatch()) {
                            // Generate defect ID for failed test
                            String defectId = defectManager.generateDefectId(entity, "post", "normal");
                            result.setDefectId(defectId);
                            
                            // Create defect record
                            defectManager.createDefectRecord(
                                defectId, entity, "POST", "NORMAL",
                                "POST operation test failed - JSON comparison mismatch",
                                expectedResult, actualResult, requestBody
                            );
                        }
                    } else {
                        result.setTestPassed(true);
                        result.setComparisonMessage("No expected result provided, test passed based on successful API response");
                        excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "PASSED");
                    }
                } else {
                    result.setTestPassed(false);
                    result.setComparisonMessage("Could not extract ID from response");
                    excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "FAILED");
                    
                    // Generate defect for ID extraction failure
                    String defectId = defectManager.generateDefectId(entity, "post", "id_extraction");
                    result.setDefectId(defectId);
                    
                    defectManager.createDefectRecord(
                        defectId, entity, "POST", "ID_EXTRACTION",
                        "Could not extract ID from POST response",
                        "Valid ID in response", response.getBody().asString(), requestBody
                    );
                }
            } else {
                result.setTestPassed(false);
                result.setComparisonMessage("API request failed with status code: " + response.getStatusCode());
                excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "FAILED");
                
                // Generate defect for API failure
                String defectId = defectManager.generateDefectId(entity, "post", "api_failure");
                result.setDefectId(defectId);
                
                defectManager.createDefectRecord(
                    defectId, entity, "POST", "API_FAILURE",
                    "POST API request failed with status code: " + response.getStatusCode(),
                    "Successful API response (2xx)", response.getBody().asString(), requestBody
                );
            }
            
        } catch (Exception e) {
            log.error("Error executing POST test for entity {}: {}", entity, e.getMessage());
            result.setTestPassed(false);
            result.setComparisonMessage("Test execution error: " + e.getMessage());
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "ERROR");
            
            // Generate defect for execution error
            String defectId = defectManager.generateDefectId(entity, "post", "execution_error");
            result.setDefectId(defectId);
            
            defectManager.createDefectRecord(
                defectId, entity, "POST", "EXECUTION_ERROR",
                "Test execution error: " + e.getMessage(),
                "Successful test execution", "Error: " + e.getMessage(), 
                result.getRequestBody() != null ? result.getRequestBody() : "N/A"
            );
        }
        
        return result;
    }
    

    
    /**
     * Execute POST test for null constraint violation
     */
    public TestResult executePostNullConstraintTest(String entity, String baseRequestBody,
                                                  String filePath, String sheetName, int rowNum,
                                                  int statusColumn) {

        TestResult result = new TestResult();
        result.setEntity(entity);
        result.setOperation("POST");
        result.setTestType("NULL_CONSTRAINT_VIOLATION");

        try {
            // Generate request body with null constraint violation
            String violationRequestBody = constraintTestGenerator.generateNullConstraintViolationRequest(entity, baseRequestBody);
            result.setRequestBody(violationRequestBody);

            log.info("Executing POST null constraint violation test for entity: {}", entity);

            // Execute API request using ApiRequestHandler
            Response response = apiRequestHandler.executeRequest(entity, "post", violationRequestBody);
            result.setStatusCode(response.getStatusCode());
            result.setResponseBody(response.getBody().asString());

            // For null constraint violation, we expect the request to fail (4xx status)
            if (response.getStatusCode() >= 400 && response.getStatusCode() < 500) {
                result.setTestPassed(true);
                result.setComparisonMessage("Null constraint violation correctly rejected by API");
                excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "PASSED");
            } else {
                result.setTestPassed(false);
                result.setComparisonMessage("Null constraint violation was not rejected by API. Status: " + response.getStatusCode());
                excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "FAILED");

                // Generate defect for constraint not enforced
                String defectId = defectManager.generateDefectId(entity, "post", "null_constraint");
                result.setDefectId(defectId);

                defectManager.createDefectRecord(
                    defectId, entity, "POST", "NULL_CONSTRAINT_VIOLATION",
                    "Null constraint violation was not rejected by API",
                    "API should reject request with 4xx status", response.getBody().asString(), violationRequestBody
                );
            }

        } catch (Exception e) {
            log.error("Error executing POST null constraint test for entity {}: {}", entity, e.getMessage());
            result.setTestPassed(false);
            result.setComparisonMessage("Test execution error: " + e.getMessage());
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "ERROR");
        }

        return result;
    }

    /**
     * Execute POST test for unique constraint violation
     */
    public TestResult executePostUniqueConstraintTest(String entity, String baseRequestBody,
                                                    String filePath, String sheetName, int rowNum,
                                                    int statusColumn) {

        TestResult result = new TestResult();
        result.setEntity(entity);
        result.setOperation("POST");
        result.setTestType("UNIQUE_CONSTRAINT_VIOLATION");

        try {
            // Generate request body with unique constraint violation
            String violationRequestBody = constraintTestGenerator.generateUniqueConstraintViolationRequest(entity, baseRequestBody);
            result.setRequestBody(violationRequestBody);

            log.info("Executing POST unique constraint violation test for entity: {}", entity);

            // Execute API request using ApiRequestHandler
            Response response = apiRequestHandler.executeRequest(entity, "post", violationRequestBody);
            result.setStatusCode(response.getStatusCode());
            result.setResponseBody(response.getBody().asString());

            // For unique constraint violation, we expect the request to fail (4xx status)
            if (response.getStatusCode() >= 400 && response.getStatusCode() < 500) {
                result.setTestPassed(true);
                result.setComparisonMessage("Unique constraint violation correctly rejected by API");
                excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "PASSED");
            } else {
                result.setTestPassed(false);
                result.setComparisonMessage("Unique constraint violation was not rejected by API. Status: " + response.getStatusCode());
                excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "FAILED");

                // Generate defect for constraint not enforced
                String defectId = defectManager.generateDefectId(entity, "post", "unique_constraint");
                result.setDefectId(defectId);

                defectManager.createDefectRecord(
                    defectId, entity, "POST", "UNIQUE_CONSTRAINT_VIOLATION",
                    "Unique constraint violation was not rejected by API",
                    "API should reject request with 4xx status", response.getBody().asString(), violationRequestBody
                );
            }

        } catch (Exception e) {
            log.error("Error executing POST unique constraint test for entity {}: {}", entity, e.getMessage());
            result.setTestPassed(false);
            result.setComparisonMessage("Test execution error: " + e.getMessage());
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNum, statusColumn, "ERROR");
        }

        return result;
    }

    /**
     * Extract ID from API response
     */
    private String extractIdFromResponse(String responseBody, String entity) {
        try {
            JsonNode responseNode = objectMapper.readTree(responseBody);
            String primaryKey = configManager.getPrimaryKey(entity);

            // Try to get the primary key field
            if (responseNode.has(primaryKey)) {
                return responseNode.get(primaryKey).asText();
            }

            // Try common ID field names
            String[] commonIdFields = {"id", "Id", "ID", entity + "_id", entity + "Id"};
            for (String idField : commonIdFields) {
                if (responseNode.has(idField)) {
                    return responseNode.get(idField).asText();
                }
            }

            log.warn("Could not extract ID from response for entity {}. Response: {}", entity, responseBody);
            return null;

        } catch (Exception e) {
            log.error("Error extracting ID from response: {}", e.getMessage());
            return null;
        }
    }
}
