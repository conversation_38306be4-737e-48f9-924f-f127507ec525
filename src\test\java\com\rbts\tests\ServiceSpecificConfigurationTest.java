package com.rbts.tests;

import com.rbts.config.ServiceSpecificConfigManager;
import com.rbts.config.ServiceSpecificConfigTemplateGenerator;
import com.rbts.config.ServiceTableConfig;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Set;

/**
 * TestNG test class for service-specific comprehensive configuration
 * Demonstrates one sheet per service containing all configurations
 */
@Slf4j
public class ServiceSpecificConfigurationTest {
    
    private ServiceSpecificConfigTemplateGenerator templateGenerator;
    private ServiceSpecificConfigManager configManager;
    
    @BeforeClass
    public void setUp() {
        log.info("🚀 Setting up Service-Specific Configuration Test");
        
        // Generate service-specific configuration template
        templateGenerator = new ServiceSpecificConfigTemplateGenerator();
        templateGenerator.generateServiceSpecificConfigurationTemplate();
        
        // Initialize config manager
        configManager = ServiceSpecificConfigManager.getInstance();
        configManager.loadConfigurations();
        
        log.info("✅ Service-Specific Configuration Test setup completed");
    }
    
    /**
     * Test general configuration loading
     */
    @Test(description = "Test general configuration loading")
    public void testGeneralConfiguration() {
        log.info("🔧 Testing General Configuration");
        
        // Test framework configuration
        String frameworkName = configManager.getGeneralConfig("framework.name");
        Assert.assertNotNull(frameworkName, "Framework name should be configured");
        Assert.assertTrue(frameworkName.contains("Service-Specific"), "Should be service-specific framework");
        log.info("✅ Framework Name: {}", frameworkName);
        
        // Test base URLs
        String directBaseUrl = configManager.getGeneralConfig("base.url.direct");
        String proxyBaseUrl = configManager.getGeneralConfig("base.url.proxy");
        Assert.assertNotNull(directBaseUrl, "Direct base URL should be configured");
        Assert.assertNotNull(proxyBaseUrl, "Proxy base URL should be configured");
        log.info("✅ Direct Base URL: {}", directBaseUrl);
        log.info("✅ Proxy Base URL: {}", proxyBaseUrl);
        
        // Test database configuration
        String databaseUrl = configManager.getGeneralConfig("database.url");
        Assert.assertNotNull(databaseUrl, "Database URL should be configured");
        Assert.assertTrue(databaseUrl.contains("postgresql"), "Should be PostgreSQL database");
        log.info("✅ Database URL: {}", databaseUrl);
    }
    
    /**
     * Test status code configuration
     */
    @Test(description = "Test status code configuration")
    public void testStatusCodeConfiguration() {
        log.info("📊 Testing Status Code Configuration");
        
        // Test POST operation status codes
        int postSuccessCode = configManager.getStatusCode("POST", "success");
        int postNullConstraintCode = configManager.getStatusCode("POST", "null_constraint");
        int postUniqueConstraintCode = configManager.getStatusCode("POST", "unique_constraint");
        
        Assert.assertEquals(postSuccessCode, 201, "POST success should be 201");
        Assert.assertEquals(postNullConstraintCode, 700, "POST null constraint should be 700");
        Assert.assertEquals(postUniqueConstraintCode, 701, "POST unique constraint should be 701");
        
        log.info("✅ POST Status Codes - Success: {}, Null: {}, Unique: {}", 
                postSuccessCode, postNullConstraintCode, postUniqueConstraintCode);
        
        // Test foreign key status codes
        int fkSameServiceCode = configManager.getStatusCode("POST", "foreign_key_same_service");
        int fkDifferentServiceCode = configManager.getStatusCode("POST", "foreign_key_different_service");
        
        Assert.assertEquals(fkSameServiceCode, 404, "FK same service should be 404");
        Assert.assertEquals(fkDifferentServiceCode, 702, "FK different service should be 702");
        
        log.info("✅ FK Status Codes - Same Service: {}, Different Service: {}", 
                fkSameServiceCode, fkDifferentServiceCode);
    }
    
    /**
     * Test service configuration loading
     */
    @Test(description = "Test service configuration loading")
    public void testServiceConfigurationLoading() {
        log.info("🏢 Testing Service Configuration Loading");
        
        // Test configured services
        Set<String> configuredServices = configManager.getConfiguredServices();
        Assert.assertFalse(configuredServices.isEmpty(), "Should have configured services");
        
        log.info("✅ Configured Services: {}", configuredServices);
        
        // Test each service has configurations
        for (String service : configuredServices) {
            List<ServiceTableConfig> serviceConfigs = configManager.getServiceConfigurations(service);
            Assert.assertFalse(serviceConfigs.isEmpty(), 
                    "Service " + service + " should have configurations");
            
            Set<String> tables = configManager.getTablesForService(service);
            log.info("✅ Service '{}': {} configurations, {} tables", 
                    service, serviceConfigs.size(), tables.size());
        }
    }
    
    /**
     * Test Contact service specific configuration
     */
    @Test(description = "Test Contact service configuration")
    public void testContactServiceConfiguration() {
        log.info("📋 Testing Contact Service Configuration");
        
        // Test AddressType table configuration
        List<ServiceTableConfig> addressTypeConfigs = configManager.getTableConfigurations("contact", "AddressType");
        Assert.assertFalse(addressTypeConfigs.isEmpty(), "AddressType should have configurations");
        
        // Test API endpoint configuration
        String postEndpoint = configManager.getApiEndpoint("contact", "AddressType", "POST");
        Assert.assertNotNull(postEndpoint, "AddressType should have POST endpoint");
        Assert.assertTrue(postEndpoint.contains("/contact/api/AddressType/save"), 
                "POST endpoint should be correct");
        log.info("✅ AddressType POST Endpoint: {}", postEndpoint);
        
        // Test testing enablement
        boolean isEnabled = configManager.isTableEnabledForTesting("contact", "AddressType");
        Assert.assertTrue(isEnabled, "AddressType should be enabled for testing");
        log.info("✅ AddressType Testing Enabled: {}", isEnabled);
        
        // Test operations to test
        List<String> operationsToTest = configManager.getOperationsToTest("contact", "AddressType");
        Assert.assertFalse(operationsToTest.isEmpty(), "AddressType should have operations to test");
        log.info("✅ AddressType Operations to Test: {}", operationsToTest);
    }
    
    /**
     * Test Authentication service specific configuration
     */
    @Test(description = "Test Authentication service configuration")
    public void testAuthenticationServiceConfiguration() {
        log.info("🔐 Testing Authentication Service Configuration");
        
        // Test User table email field null constraint
        ServiceTableConfig emailNullConstraint = configManager.getErrorMessageValidation(
                "authentication", "User", "email_address", "null_constraint");
        
        Assert.assertNotNull(emailNullConstraint, "User email null constraint should be configured");
        Assert.assertTrue(emailNullConstraint.getExpectedErrorMessage().contains("Email address is required"),
                "Error message should be correct");
        log.info("✅ User Email Null Constraint: {}", emailNullConstraint.getExpectedErrorMessage());
        
        // Test User table email field unique constraint
        ServiceTableConfig emailUniqueConstraint = configManager.getErrorMessageValidation(
                "authentication", "User", "email_address", "unique_constraint");
        
        Assert.assertNotNull(emailUniqueConstraint, "User email unique constraint should be configured");
        Assert.assertTrue(emailUniqueConstraint.getExpectedErrorMessage().contains("already registered"),
                "Unique constraint message should be correct");
        log.info("✅ User Email Unique Constraint: {}", emailUniqueConstraint.getExpectedErrorMessage());
        
        // Test UserProfile foreign key constraints
        ServiceTableConfig fkSameService = configManager.getErrorMessageValidation(
                "authentication", "UserProfile", "user_id", "foreign_key_same_service");
        
        Assert.assertNotNull(fkSameService, "UserProfile FK same service should be configured");
        log.info("✅ UserProfile FK Same Service: {}", fkSameService.getExpectedErrorMessage());
        
        ServiceTableConfig fkDifferentService = configManager.getErrorMessageValidation(
                "authentication", "UserProfile", "country_id", "foreign_key_different_service");
        
        Assert.assertNotNull(fkDifferentService, "UserProfile FK different service should be configured");
        log.info("✅ UserProfile FK Different Service: {}", fkDifferentService.getExpectedErrorMessage());
    }
    
    /**
     * Test Core service specific configuration
     */
    @Test(description = "Test Core service configuration")
    public void testCoreServiceConfiguration() {
        log.info("🏛️ Testing Core Service Configuration");
        
        // Test State table stateShortName constraints
        ServiceTableConfig stateNullConstraint = configManager.getErrorMessageValidation(
                "core", "State", "state_short_name", "null_constraint");
        
        Assert.assertNotNull(stateNullConstraint, "State short name null constraint should be configured");
        Assert.assertTrue(stateNullConstraint.getExpectedErrorMessage().contains("State short name cannot be null"),
                "State null constraint message should be correct");
        log.info("✅ State Short Name Null Constraint: {}", stateNullConstraint.getExpectedErrorMessage());
        
        ServiceTableConfig stateUniqueConstraint = configManager.getErrorMessageValidation(
                "core", "State", "state_short_name", "unique_constraint");
        
        Assert.assertNotNull(stateUniqueConstraint, "State short name unique constraint should be configured");
        Assert.assertTrue(stateUniqueConstraint.getExpectedErrorMessage().contains("already exists"),
                "State unique constraint message should be correct");
        log.info("✅ State Short Name Unique Constraint: {}", stateUniqueConstraint.getExpectedErrorMessage());
        
        // Test Country table (should be disabled)
        boolean countryEnabled = configManager.isTableEnabledForTesting("core", "Country");
        Assert.assertFalse(countryEnabled, "Country should be disabled for testing");
        log.info("✅ Country Testing Disabled: {}", !countryEnabled);
    }
    
    /**
     * Test Order service specific configuration
     */
    @Test(description = "Test Order service configuration")
    public void testOrderServiceConfiguration() {
        log.info("📦 Testing Order Service Configuration");
        
        // Test Order table success message
        ServiceTableConfig orderSuccess = configManager.getErrorMessageValidation(
                "order", "Order", "", "success");
        
        Assert.assertNotNull(orderSuccess, "Order success message should be configured");
        Assert.assertTrue(orderSuccess.getExpectedErrorMessage().contains("Order created successfully"),
                "Order success message should be correct");
        log.info("✅ Order Success Message: {}", orderSuccess.getExpectedErrorMessage());
        
        // Test Order table operations
        List<String> orderOperations = configManager.getOperationsToTest("order", "Order");
        Assert.assertTrue(orderOperations.contains("post"), "Order should support POST");
        Assert.assertTrue(orderOperations.contains("get"), "Order should support GET");
        Assert.assertTrue(orderOperations.contains("put"), "Order should support PUT");
        Assert.assertTrue(orderOperations.contains("delete"), "Order should support DELETE");
        log.info("✅ Order Operations: {}", orderOperations);
    }
    
    /**
     * Test Product service specific configuration
     */
    @Test(description = "Test Product service configuration")
    public void testProductServiceConfiguration() {
        log.info("🛍️ Testing Product Service Configuration");
        
        // Test Product price validation error
        ServiceTableConfig priceValidation = configManager.getErrorMessageValidation(
                "product", "Product", "price", "validation_error");
        
        Assert.assertNotNull(priceValidation, "Product price validation should be configured");
        Assert.assertTrue(priceValidation.getExpectedErrorMessage().contains("must be greater than 0"),
                "Price validation message should be correct");
        log.info("✅ Product Price Validation: {}", priceValidation.getExpectedErrorMessage());
        
        // Test Product test value
        Object testValue = priceValidation.getTestValueAsObject();
        Assert.assertNotNull(testValue, "Product price test value should be configured");
        Assert.assertTrue(testValue instanceof Double, "Test value should be a double");
        Assert.assertTrue((Double) testValue < 0, "Test value should be negative for validation error");
        log.info("✅ Product Price Test Value: {}", testValue);
    }
    
    /**
     * Test service-specific configuration benefits
     */
    @Test(description = "Test service-specific configuration benefits", 
          dependsOnMethods = {"testGeneralConfiguration", "testStatusCodeConfiguration", 
                             "testContactServiceConfiguration", "testAuthenticationServiceConfiguration",
                             "testCoreServiceConfiguration", "testOrderServiceConfiguration", 
                             "testProductServiceConfiguration"})
    public void testServiceSpecificConfigurationBenefits() {
        log.info("🎯 Testing Service-Specific Configuration Benefits");
        
        // Print configuration summary
        configManager.printConfigurationSummary();
        
        // Test scalability
        Set<String> services = configManager.getConfiguredServices();
        log.info("📊 SCALABILITY TEST:");
        log.info("   Current Services: {}", services.size());
        log.info("   Services: {}", services);
        
        // Test maintainability
        int totalConfigurations = 0;
        for (String service : services) {
            List<ServiceTableConfig> configs = configManager.getServiceConfigurations(service);
            totalConfigurations += configs.size();
        }
        
        log.info("📋 MAINTAINABILITY TEST:");
        log.info("   Total Configurations: {}", totalConfigurations);
        log.info("   Average per Service: {}", totalConfigurations / services.size());
        
        log.info("");
        log.info("🎉 SERVICE-SPECIFIC CONFIGURATION BENEFITS DEMONSTRATED:");
        log.info("✅ One comprehensive sheet per service");
        log.info("✅ All configurations in one place per service:");
        log.info("   • Field mappings (DB ↔ API Request ↔ API Response)");
        log.info("   • API endpoints configuration");
        log.info("   • Test execution control");
        log.info("   • Error message validation");
        log.info("   • Constraint type configuration");
        log.info("   • Foreign key relationships");
        log.info("✅ Easy to maintain and scale");
        log.info("✅ Service-specific organization");
        log.info("✅ Reduced sheet proliferation");
        log.info("✅ Better readability and management");
        
        log.info("");
        log.info("📊 COMPARISON:");
        log.info("❌ OLD APPROACH: {} services × 5 sheets = {} total sheets", services.size(), services.size() * 5);
        log.info("✅ NEW APPROACH: {} services + 2 common sheets = {} total sheets", services.size(), services.size() + 2);
        log.info("🎯 REDUCTION: {}% fewer sheets!", 
                Math.round((1.0 - (double)(services.size() + 2) / (services.size() * 5)) * 100));
        
        Assert.assertTrue(services.size() > 0, "Should have configured services");
        Assert.assertTrue(totalConfigurations > 0, "Should have total configurations");
    }
}
