package com.rbts.generator;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.javafaker.Faker;
import com.rbts.config.ConfigManager;
import com.rbts.schema.DatabaseSchemaDetector;
import com.rbts.schema.DatabaseSchemaDetector.TableSchema;
import com.rbts.schema.DatabaseSchemaDetector.ColumnInfo;
import com.rbts.schema.DatabaseSchemaDetector.ForeignKeyInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Automated Request Body Generator
 * Generates request bodies based on database schema auto-detection
 */
@Slf4j
public class AutoRequestBodyGenerator {
    
    private final ConfigManager configManager;
    private final DatabaseSchemaDetector schemaDetector;
    private final ObjectMapper objectMapper;
    private final Faker faker;
    
    public AutoRequestBodyGenerator() {
        this.configManager = ConfigManager.getInstance();
        this.schemaDetector = new DatabaseSchemaDetector();
        this.objectMapper = new ObjectMapper();
        this.faker = new Faker();
    }
    
    /**
     * Generate normal request body for POST/PUT operations
     */
    public String generateNormalRequestBody(String tableName, String operation) {
        try {
            TableSchema schema = schemaDetector.getTableSchema(tableName);
            ObjectNode requestBody = objectMapper.createObjectNode();
            
            for (ColumnInfo column : schema.getColumns()) {
                // Skip primary key for POST operations
                if (operation.equalsIgnoreCase("post") && schema.getPrimaryKeys().contains(column.getColumnName())) {
                    continue;
                }
                
                // Skip auto-generated columns
                if (isAutoGeneratedColumn(column.getColumnName())) {
                    continue;
                }
                
                Object value = generateValueForColumn(column, schema.getForeignKeys(), false);
                addValueToRequestBody(requestBody, column.getColumnName(), value, column.getDataType());
            }
            
            log.debug("Generated normal request body for table {}: {}", tableName, requestBody.toString());
            return objectMapper.writeValueAsString(requestBody);
            
        } catch (Exception e) {
            log.error("Error generating normal request body for table {}: {}", tableName, e.getMessage());
            throw new RuntimeException("Request body generation failed", e);
        }
    }
    
    /**
     * Generate PATCH request body (only non-foreign key fields)
     */
    public String generatePatchRequestBody(String tableName) {
        try {
            TableSchema schema = schemaDetector.getTableSchema(tableName);
            ObjectNode requestBody = objectMapper.createObjectNode();
            
            // Get foreign key column names for exclusion
            List<String> foreignKeyColumns = schema.getForeignKeys().stream()
                    .map(ForeignKeyInfo::getColumnName)
                    .toList();
            
            for (ColumnInfo column : schema.getColumns()) {
                // Skip primary keys, auto-generated columns, and foreign keys
                if (schema.getPrimaryKeys().contains(column.getColumnName()) ||
                    isAutoGeneratedColumn(column.getColumnName()) ||
                    foreignKeyColumns.contains(column.getColumnName())) {
                    continue;
                }
                
                Object value = generateValueForColumn(column, schema.getForeignKeys(), false);
                addValueToRequestBody(requestBody, column.getColumnName(), value, column.getDataType());
            }
            
            log.debug("Generated PATCH request body for table {}: {}", tableName, requestBody.toString());
            return objectMapper.writeValueAsString(requestBody);
            
        } catch (Exception e) {
            log.error("Error generating PATCH request body for table {}: {}", tableName, e.getMessage());
            throw new RuntimeException("PATCH request body generation failed", e);
        }
    }
    
    /**
     * Generate request body with null constraint violation
     */
    public String generateNullConstraintViolationBody(String tableName, String operation) {
        try {
            TableSchema schema = schemaDetector.getTableSchema(tableName);
            ObjectNode requestBody = objectMapper.createObjectNode();
            
            // Find a not-null column to violate
            String violationColumn = null;
            for (String notNullColumn : schema.getNotNullConstraints()) {
                if (!schema.getPrimaryKeys().contains(notNullColumn) && !isAutoGeneratedColumn(notNullColumn)) {
                    violationColumn = notNullColumn;
                    break;
                }
            }
            
            for (ColumnInfo column : schema.getColumns()) {
                // Skip primary key for POST operations
                if (operation.equalsIgnoreCase("post") && schema.getPrimaryKeys().contains(column.getColumnName())) {
                    continue;
                }
                
                // Skip auto-generated columns
                if (isAutoGeneratedColumn(column.getColumnName())) {
                    continue;
                }
                
                if (column.getColumnName().equals(violationColumn)) {
                    // Set this column to null to violate constraint
                    requestBody.putNull(column.getColumnName());
                } else {
                    Object value = generateValueForColumn(column, schema.getForeignKeys(), false);
                    addValueToRequestBody(requestBody, column.getColumnName(), value, column.getDataType());
                }
            }
            
            log.debug("Generated null constraint violation body for table {} (violated column: {}): {}", 
                    tableName, violationColumn, requestBody.toString());
            return objectMapper.writeValueAsString(requestBody);
            
        } catch (Exception e) {
            log.error("Error generating null constraint violation body for table {}: {}", tableName, e.getMessage());
            throw new RuntimeException("Null constraint violation body generation failed", e);
        }
    }
    
    /**
     * Generate request body with unique constraint violation
     */
    public String generateUniqueConstraintViolationBody(String tableName, String operation) {
        try {
            TableSchema schema = schemaDetector.getTableSchema(tableName);
            ObjectNode requestBody = objectMapper.createObjectNode();
            
            // Find a unique column to violate
            String violationColumn = null;
            for (String uniqueColumn : schema.getUniqueConstraints()) {
                if (!schema.getPrimaryKeys().contains(uniqueColumn)) {
                    violationColumn = uniqueColumn;
                    break;
                }
            }
            
            for (ColumnInfo column : schema.getColumns()) {
                // Skip primary key for POST operations
                if (operation.equalsIgnoreCase("post") && schema.getPrimaryKeys().contains(column.getColumnName())) {
                    continue;
                }
                
                // Skip auto-generated columns
                if (isAutoGeneratedColumn(column.getColumnName())) {
                    continue;
                }
                
                if (column.getColumnName().equals(violationColumn)) {
                    // Use an existing value to violate unique constraint
                    Object existingValue = getExistingUniqueValue(tableName, column.getColumnName());
                    addValueToRequestBody(requestBody, column.getColumnName(), existingValue, column.getDataType());
                } else {
                    Object value = generateValueForColumn(column, schema.getForeignKeys(), false);
                    addValueToRequestBody(requestBody, column.getColumnName(), value, column.getDataType());
                }
            }
            
            log.debug("Generated unique constraint violation body for table {} (violated column: {}): {}", 
                    tableName, violationColumn, requestBody.toString());
            return objectMapper.writeValueAsString(requestBody);
            
        } catch (Exception e) {
            log.error("Error generating unique constraint violation body for table {}: {}", tableName, e.getMessage());
            throw new RuntimeException("Unique constraint violation body generation failed", e);
        }
    }
    
    /**
     * Generate request body with invalid foreign key
     */
    public String generateInvalidForeignKeyBody(String tableName, String operation) {
        try {
            TableSchema schema = schemaDetector.getTableSchema(tableName);
            ObjectNode requestBody = objectMapper.createObjectNode();
            
            for (ColumnInfo column : schema.getColumns()) {
                // Skip primary key for POST operations
                if (operation.equalsIgnoreCase("post") && schema.getPrimaryKeys().contains(column.getColumnName())) {
                    continue;
                }
                
                // Skip auto-generated columns
                if (isAutoGeneratedColumn(column.getColumnName())) {
                    continue;
                }
                
                Object value = generateValueForColumn(column, schema.getForeignKeys(), true);
                addValueToRequestBody(requestBody, column.getColumnName(), value, column.getDataType());
            }
            
            log.debug("Generated invalid foreign key body for table {}: {}", tableName, requestBody.toString());
            return objectMapper.writeValueAsString(requestBody);
            
        } catch (Exception e) {
            log.error("Error generating invalid foreign key body for table {}: {}", tableName, e.getMessage());
            throw new RuntimeException("Invalid foreign key body generation failed", e);
        }
    }
    
    /**
     * Generate value for a specific column
     */
    private Object generateValueForColumn(ColumnInfo column, List<ForeignKeyInfo> foreignKeys, boolean useInvalidForeignKey) {
        // Check if this is a foreign key column
        for (ForeignKeyInfo fk : foreignKeys) {
            if (fk.getColumnName().equals(column.getColumnName())) {
                if (useInvalidForeignKey) {
                    return schemaDetector.getInvalidForeignKeyValue(fk.getReferencedTable(), fk.getReferencedColumn());
                } else {
                    return schemaDetector.getExistingForeignKeyValue(fk.getReferencedTable(), fk.getReferencedColumn());
                }
            }
        }
        
        // Generate value based on data type
        return generateValueByDataType(column.getDataType(), column.getColumnName());
    }
    
    /**
     * Generate value based on data type
     */
    private Object generateValueByDataType(String dataType, String columnName) {
        String lowerDataType = dataType.toLowerCase();
        String lowerColumnName = columnName.toLowerCase();
        
        // Use intelligent naming for better test data
        if (lowerColumnName.contains("email")) {
            return faker.internet().emailAddress();
        } else if (lowerColumnName.contains("name")) {
            return faker.name().fullName();
        } else if (lowerColumnName.contains("phone")) {
            return faker.phoneNumber().phoneNumber();
        } else if (lowerColumnName.contains("address")) {
            return faker.address().fullAddress();
        }
        
        // Generate by data type
        if (lowerDataType.contains("varchar") || lowerDataType.contains("text") || lowerDataType.contains("char")) {
            return configManager.getProperty("sample.data.string", "Sample Text") + "_" + System.currentTimeMillis();
        } else if (lowerDataType.contains("int")) {
            return Integer.parseInt(configManager.getProperty("sample.data.integer", "123"));
        } else if (lowerDataType.contains("bigint")) {
            return Long.parseLong(configManager.getProperty("sample.data.bigint", "123456789"));
        } else if (lowerDataType.contains("decimal") || lowerDataType.contains("numeric")) {
            return Double.parseDouble(configManager.getProperty("sample.data.decimal", "99.99"));
        } else if (lowerDataType.contains("boolean")) {
            return Boolean.parseBoolean(configManager.getProperty("sample.data.boolean", "true"));
        } else if (lowerDataType.contains("date")) {
            return configManager.getProperty("sample.data.date", "2024-01-01");
        } else if (lowerDataType.contains("timestamp")) {
            return configManager.getProperty("sample.data.timestamp", "2024-01-01T10:00:00");
        } else if (lowerDataType.contains("uuid")) {
            return configManager.getProperty("sample.data.uuid", "550e8400-e29b-41d4-a716-************");
        } else {
            return "Generated_" + System.currentTimeMillis();
        }
    }
    
    /**
     * Add value to request body with proper type conversion
     */
    private void addValueToRequestBody(ObjectNode requestBody, String columnName, Object value, String dataType) {
        if (value == null) {
            requestBody.putNull(columnName);
        } else if (value instanceof String) {
            requestBody.put(columnName, (String) value);
        } else if (value instanceof Integer) {
            requestBody.put(columnName, (Integer) value);
        } else if (value instanceof Long) {
            requestBody.put(columnName, (Long) value);
        } else if (value instanceof Double) {
            requestBody.put(columnName, (Double) value);
        } else if (value instanceof Boolean) {
            requestBody.put(columnName, (Boolean) value);
        } else {
            requestBody.put(columnName, value.toString());
        }
    }
    
    /**
     * Check if column is auto-generated (created_at, updated_at, etc.)
     */
    private boolean isAutoGeneratedColumn(String columnName) {
        String lowerColumnName = columnName.toLowerCase();
        return lowerColumnName.contains("created_at") || 
               lowerColumnName.contains("updated_at") || 
               lowerColumnName.contains("created_date") || 
               lowerColumnName.contains("updated_date") ||
               lowerColumnName.contains("created_by") ||
               lowerColumnName.contains("updated_by") ||
               lowerColumnName.contains("modified_at") ||
               lowerColumnName.contains("modified_by");
    }
    
    /**
     * Get existing unique value from database
     */
    private Object getExistingUniqueValue(String tableName, String columnName) {
        // This would query the database to get an existing value
        // For now, return a static value that might exist
        return "EXISTING_VALUE_" + System.currentTimeMillis();
    }
}
