package com.rbts.base.post;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.restassured.response.Response;
import org.slf4j.Logger;
import com.rbts.utils.ExcelUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static io.restassured.RestAssured.given;

public class PostWithDynamicRequestBody implements ApiMethod {
    private Logger logger;
    private String filePath;
    private String sheetName;
    private int url;
    private int body;
    private int serviceColumn = -1; // Optional column for service name
    private int entityColumn = -1;  // Optional column for entity type
    private int apiPatternColumn = -1; // Optional column for API pattern

    // Configuration maps loaded from properties
    private Map<String, String> apiPatterns = new HashMap<>();
    private Map<String, String> operationSuffixes = new HashMap<>();
    private Map<String, String> operationToRequestType = new HashMap<>();
    private List<String> commonServices = new ArrayList<>();
    private List<String> commonOperations = new ArrayList<>();
    private List<String> specialEndpoints = new ArrayList<>();
    private Map<String, String> entityToService = new HashMap<>();

    // Properties file paths
    private static final String CONFIG_FILE = "api-config.properties";
    private static final String DEFAULT_CONFIG_FILE = "default-api-config.properties";
    private Properties properties;

    /**
     * Constructor with basic parameters
     */
    public PostWithDynamicRequestBody(Logger logger, String filePath, String sheetName, int url, int body) {
        this.logger = logger;
        this.filePath = filePath;
        this.sheetName = sheetName;
        this.url = url;
        this.body = body;
        loadProperties();
    }

    /**
     * Constructor with additional columns for service and entity information
     */
    public PostWithDynamicRequestBody(Logger logger, String filePath, String sheetName, int url, int body,
                                     int serviceColumn, int entityColumn, int apiPatternColumn) {
        this(logger, filePath, sheetName, url, body);
        this.serviceColumn = serviceColumn;
        this.entityColumn = entityColumn;
        this.apiPatternColumn = apiPatternColumn;
    }

    /**
     * Load properties from the configuration file
     */
    private void loadProperties() {
        properties = new Properties();

        // First try to load from the main properties file
        try (InputStream input = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (input == null) {
                logger.warn("Unable to find main properties file {}, trying default properties", CONFIG_FILE);
                loadDefaultProperties();
                return;
            }

            properties.load(input);
            logger.info("Loaded properties from {}", CONFIG_FILE);

            // Load API patterns
            for (String key : properties.stringPropertyNames()) {
                if (key.startsWith("api.pattern.")) {
                    String service = key.substring("api.pattern.".length());
                    String pattern = properties.getProperty(key);
                    apiPatterns.put(service, pattern);
                }
            }

            // Load operation suffixes
            for (String key : properties.stringPropertyNames()) {
                if (key.startsWith("operation.suffix.")) {
                    String requestType = key.substring("operation.suffix.".length());
                    String suffix = properties.getProperty(key);
                    operationSuffixes.put(requestType, suffix);
                }
            }

            // Load operation to request type mapping
            for (String key : properties.stringPropertyNames()) {
                if (key.startsWith("operation.type.")) {
                    String operation = key.substring("operation.type.".length());
                    String requestType = properties.getProperty(key);
                    operationToRequestType.put(operation, requestType);
                }
            }

            // Load common operations
            String commonOpsStr = properties.getProperty("common.operations", "");
            if (!commonOpsStr.isEmpty()) {
                commonOperations = Arrays.asList(commonOpsStr.split(","));
            }

            // Load common services
            String commonServicesStr = properties.getProperty("common.services", "");
            if (!commonServicesStr.isEmpty()) {
                commonServices = Arrays.asList(commonServicesStr.split(","));
            }

            // Load special endpoints
            String specialEndpointsStr = properties.getProperty("special.endpoints", "");
            if (!specialEndpointsStr.isEmpty()) {
                specialEndpoints = Arrays.asList(specialEndpointsStr.split(","));
            }

            // Load entity to service mapping
            for (String key : properties.stringPropertyNames()) {
                if (key.startsWith("entity.service.")) {
                    String entity = key.substring("entity.service.".length());
                    String service = properties.getProperty(key);
                    entityToService.put(entity, service);
                }
            }

        } catch (IOException ex) {
            logger.error("Error loading properties from {}: {}", CONFIG_FILE, ex.getMessage());
            loadDefaultProperties();
        }
    }

    /**
     * Load default properties if the configuration file is not found
     */
    private void loadDefaultProperties() {
        logger.info("Trying to load default properties from {}", DEFAULT_CONFIG_FILE);

        try (InputStream input = getClass().getClassLoader().getResourceAsStream(DEFAULT_CONFIG_FILE)) {
            if (input == null) {
                logger.warn("Unable to find default properties file {}, using hardcoded defaults", DEFAULT_CONFIG_FILE);
                loadHardcodedDefaults();
                return;
            }

            Properties defaultProps = new Properties();
            defaultProps.load(input);
            logger.info("Loaded default properties from {}", DEFAULT_CONFIG_FILE);

            // Load API patterns
            for (String key : defaultProps.stringPropertyNames()) {
                if (key.startsWith("api.pattern.")) {
                    String service = key.substring("api.pattern.".length());
                    String pattern = defaultProps.getProperty(key);
                    apiPatterns.put(service, pattern);
                }
            }

            // Load operation suffixes
            for (String key : defaultProps.stringPropertyNames()) {
                if (key.startsWith("operation.suffix.")) {
                    String requestType = key.substring("operation.suffix.".length());
                    String suffix = defaultProps.getProperty(key);
                    operationSuffixes.put(requestType, suffix);
                }
            }

            // Load operation to request type mapping
            for (String key : defaultProps.stringPropertyNames()) {
                if (key.startsWith("operation.type.")) {
                    String operation = key.substring("operation.type.".length());
                    String requestType = defaultProps.getProperty(key);
                    operationToRequestType.put(operation, requestType);
                }
            }

            // Load common operations
            String commonOpsStr = defaultProps.getProperty("common.operations", "");
            if (!commonOpsStr.isEmpty()) {
                commonOperations = Arrays.asList(commonOpsStr.split(","));
            }

            // Load common services
            String commonServicesStr = defaultProps.getProperty("common.services", "");
            if (!commonServicesStr.isEmpty()) {
                commonServices = Arrays.asList(commonServicesStr.split(","));
            }

            // Load special endpoints
            String specialEndpointsStr = defaultProps.getProperty("special.endpoints", "");
            if (!specialEndpointsStr.isEmpty()) {
                specialEndpoints = Arrays.asList(specialEndpointsStr.split(","));
            }

            // Load entity to service mapping
            for (String key : defaultProps.stringPropertyNames()) {
                if (key.startsWith("entity.service.")) {
                    String entity = key.substring("entity.service.".length());
                    String service = defaultProps.getProperty(key);
                    entityToService.put(entity, service);
                }
            }

        } catch (IOException ex) {
            logger.error("Error loading default properties from {}: {}", DEFAULT_CONFIG_FILE, ex.getMessage());
            loadHardcodedDefaults();
        }
    }

    /**
     * Load hardcoded default properties as a last resort
     * This is only used if both the main and default properties files cannot be loaded
     */
    private void loadHardcodedDefaults() {
        logger.info("Loading hardcoded default properties");

        // Default API patterns
        apiPatterns.put("default", "/api/");
        apiPatterns.put("core", "/api/");
        apiPatterns.put("auth", "/api/");
        apiPatterns.put("contact", "/api/");
        apiPatterns.put("order", "/api/");

        // Default operation suffixes
        operationSuffixes.put("post", "/save");
        operationSuffixes.put("put", "/update");
        operationSuffixes.put("delete", "/delete");
        operationSuffixes.put("get", "");
        operationSuffixes.put("getAll", "/list");

        // Default operation to request type mapping
        operationToRequestType.put("save", "post");
        operationToRequestType.put("update", "put");
        operationToRequestType.put("delete", "delete");
        operationToRequestType.put("list", "get");
        operationToRequestType.put("getall", "get");
        operationToRequestType.put("findall", "get");

        // Default common services
        commonServices = Arrays.asList(
            "core", "auth", "authentication", "contact", "order", "user", "product", "payment"
        );

        // Default common operations
        commonOperations = Arrays.asList(
            "save", "update", "delete", "get", "find", "search", "list", "all", "count", "exists",
            "findBy", "getBy", "searchBy", "findAll", "getAll", "findById", "getById"
        );

        // Default special endpoints
        specialEndpoints = Arrays.asList(
            "/decrypt", "/oauth/", "/token", "/login", "/auth/"
        );

        // Default entity to service mapping
        entityToService.put("country", "core");
        entityToService.put("state", "core");
        entityToService.put("master", "core");
        entityToService.put("user", "auth");
        entityToService.put("login", "auth");
        entityToService.put("contact", "contact");
        entityToService.put("address", "contact");
        entityToService.put("order", "order");
        entityToService.put("product", "order");
    }

    /**
     * Make a request with the given request body
     * @param rowNum Excel row number
     * @param requestBody Request body as a JSON string
     * @return Response from the request
     */
    public Response post(int rowNum, String requestBody) {
        ExcelUtils excelUtils = new ExcelUtils();

        // Get URI from Excel
        String uri = excelUtils.getCellData(filePath, sheetName, rowNum, url);
        logger.info("Using URI from Excel: {}", uri);

        // Fix malformed URLs
        uri = fixMalformedUrl(uri);

        // Get service and entity information from Excel if available
        String serviceFromExcel = (serviceColumn > 0) ? excelUtils.getCellData(filePath, sheetName, rowNum, serviceColumn) : "";
        String entityFromExcel = (entityColumn > 0) ? excelUtils.getCellData(filePath, sheetName, rowNum, entityColumn) : "";
        String apiPatternFromExcel = (apiPatternColumn > 0) ? excelUtils.getCellData(filePath, sheetName, rowNum, apiPatternColumn) : "";

        // Default request type is POST
        String requestType = "post";

        // Check if this is a special endpoint
        boolean isDecryptEndpoint = isDecryptEndpoint(uri);
        boolean isSpecialEndpoint = isDecryptEndpoint || isOtherSpecialEndpoint(uri);

        try {
            // Parse the request body as JSON
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(requestBody);

            // Get request type from body
            if (root.has("type")) {
                requestType = root.get("type").asText().toLowerCase();
                logger.info("Request type from body: {}", requestType);
            }

            // If the endpoint contains an operation that implies a specific request type,
            // use that instead (unless explicitly specified in the body)
            if (root.has("endpoint")) {
                String endpoint = root.get("endpoint").asText();
                String operation = extractOperationFromEndpoint(endpoint);

                if (!operation.isEmpty() && !root.has("type")) {
                    String inferredRequestType = getRequestTypeFromOperation(operation, requestType);
                    if (!inferredRequestType.equals(requestType)) {
                        requestType = inferredRequestType;
                        logger.info("Inferred request type '{}' from operation '{}' in endpoint", requestType, operation);

                        // Update the request type in the body
                        ((ObjectNode) root).put("type", requestType);
                        requestBody = mapper.writeValueAsString(root);
                    }
                }
            }

            // Process endpoint if present
            if (root.has("endpoint")) {
                // Get the endpoint from the request body
                String endpoint = root.get("endpoint").asText();
                logger.info("Endpoint from request body: {}", endpoint);

                // Ensure the endpoint starts with a slash
                endpoint = ensureLeadingSlash(endpoint);

                // Update the endpoint in the request body if it was changed
                if (!endpoint.equals(root.get("endpoint").asText())) {
                    ((ObjectNode) root).put("endpoint", endpoint);
                    requestBody = mapper.writeValueAsString(root);
                }

                // Extract entity type from endpoint
                String entityType = extractEntityTypeFromEndpoint(endpoint);

                // Use entity from Excel if available, otherwise use the extracted one
                if (!entityFromExcel.isEmpty()) {
                    entityType = entityFromExcel;
                    logger.info("Using entity type from Excel: {}", entityType);
                } else if (!entityType.isEmpty()) {
                    logger.info("Extracted entity type from endpoint: {}", entityType);
                }

                // Determine service name
                String serviceName;
                if (!serviceFromExcel.isEmpty()) {
                    // Use service name from Excel if available
                    serviceName = serviceFromExcel;
                    logger.info("Using service name from Excel: {}", serviceName);
                } else {
                    // Otherwise extract or infer it
                    serviceName = determineServiceName(endpoint, entityType, sheetName);
                }

                // Determine API pattern
                String apiPattern;
                if (!apiPatternFromExcel.isEmpty()) {
                    // Use API pattern from Excel if available
                    apiPattern = apiPatternFromExcel;
                    logger.info("Using API pattern from Excel: {}", apiPattern);
                } else {
                    // Otherwise use default for the service
                    apiPattern = apiPatterns.getOrDefault(serviceName, apiPatterns.get("default"));
                    logger.info("Using default API pattern for service {}: {}", serviceName, apiPattern);
                }

                // Format the endpoint correctly
                endpoint = formatEndpoint(endpoint, serviceName, apiPattern, requestType, entityType);

                // Update the endpoint in the request body
                ((ObjectNode) root).put("endpoint", endpoint);
                requestBody = mapper.writeValueAsString(root);

                logger.info("Final request body: {}", requestBody);
            }
        } catch (Exception e) {
            logger.warn("Could not process request body as JSON: {}", e.getMessage());
        }

        // Make the request
        return makeRequest(uri, requestBody, requestType, isSpecialEndpoint);
    }

    /**
     * Fix malformed URLs with multiple http:// prefixes
     */
    private String fixMalformedUrl(String uri) {
        if (uri != null && uri.contains("http://") && uri.indexOf("http://") != uri.lastIndexOf("http://")) {
            String fixedUri = uri.substring(uri.lastIndexOf("http://"));
            logger.info("Fixed malformed URL: {} -> {}", uri, fixedUri);
            return fixedUri;
        }
        return uri;
    }

    /**
     * Check if this is a decrypt endpoint
     */
    private boolean isDecryptEndpoint(String uri) {
        return uri != null && (uri.contains("/decrypt") || uri.endsWith("/decrypt"));
    }

    /**
     * Check if this is another special endpoint that requires custom handling
     */
    private boolean isOtherSpecialEndpoint(String uri) {
        if (uri == null) {
            return false;
        }

        // Check against the special endpoints from properties
        for (String specialEndpoint : specialEndpoints) {
            if (uri.contains(specialEndpoint)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Ensure the endpoint starts with a slash
     */
    private String ensureLeadingSlash(String endpoint) {
        if (endpoint != null && !endpoint.isEmpty() && !endpoint.startsWith("/")) {
            String originalEndpoint = endpoint;
            endpoint = "/" + endpoint;
            logger.info("Added leading slash to endpoint: {} -> {}", originalEndpoint, endpoint);
        }
        return endpoint;
    }

    /**
     * Extract entity type from endpoint
     * @param endpoint The endpoint to extract from
     * @return The extracted entity type
     */
    private String extractEntityTypeFromEndpoint(String endpoint) {
        // Try to extract entity type from endpoint
        // Format: /service/api/EntityType/operation or /service/api/EntityType/id
        String entityType = "";

        if (endpoint != null && !endpoint.isEmpty()) {
            String[] parts = endpoint.split("/");

            // Check if the endpoint follows the pattern /service/api/EntityType/operation or /service/api/EntityType/id
            if (parts.length >= 4 && parts[2].equalsIgnoreCase("api")) {
                entityType = parts[3];
                logger.info("Extracted entity type '{}' from endpoint pattern /service/api/EntityType", entityType);
            }
            // Check if the endpoint follows the pattern /api/EntityType/operation or /api/EntityType/id
            else if (parts.length >= 3 && parts[1].equalsIgnoreCase("api")) {
                entityType = parts[2];
                logger.info("Extracted entity type '{}' from endpoint pattern /api/EntityType", entityType);
            }
            // Check if the endpoint follows the pattern /EntityType/operation or /EntityType/id
            else if (parts.length >= 2) {
                entityType = parts[1];
                logger.info("Extracted entity type '{}' from endpoint pattern /EntityType", entityType);
            }
        }

        return entityType;
    }



    /**
     * Extract operation from endpoint
     * @param endpoint The endpoint to extract from
     * @return The extracted operation or empty string if not found
     */
    private String extractOperationFromEndpoint(String endpoint) {
        // Try to extract operation from endpoint
        // Format: /service/api/EntityType/operation
        String operation = "";

        if (endpoint != null && !endpoint.isEmpty()) {
            String[] parts = endpoint.split("/");

            // Check if the endpoint follows the pattern /service/api/EntityType/operation
            if (parts.length >= 5 && parts[2].equalsIgnoreCase("api")) {
                String lastPart = parts[4];

                // Check if it looks like an operation (not an ID)
                if (isOperationName(lastPart)) {
                    operation = lastPart;
                    logger.info("Extracted operation '{}' from endpoint", operation);
                }
            }
            // Check if the endpoint follows the pattern /api/EntityType/operation
            else if (parts.length >= 4 && parts[1].equalsIgnoreCase("api")) {
                String lastPart = parts[3];

                // Check if it looks like an operation (not an ID)
                if (isOperationName(lastPart)) {
                    operation = lastPart;
                    logger.info("Extracted operation '{}' from endpoint", operation);
                }
            }
        }

        return operation;
    }

    /**
     * Get the request type based on the operation
     * @param operation The operation
     * @param defaultType The default request type to use if the operation is not recognized
     * @return The request type
     */
    private String getRequestTypeFromOperation(String operation, String defaultType) {
        // Convert to lowercase for case-insensitive comparison
        String lowerOperation = operation.toLowerCase();

        // Get the request type from the mapping
        return operationToRequestType.getOrDefault(lowerOperation, defaultType);
    }

    /**
     * Check if a string is a common operation name
     * @param str The string to check
     * @return True if it's an operation name, false otherwise
     */
    private boolean isOperationName(String str) {
        // Use the common operations from properties
        List<String> operationNames = commonOperations;

        // Check if the string is an operation name
        for (String op : operationNames) {
            if (str.equalsIgnoreCase(op) || str.toLowerCase().startsWith(op.toLowerCase())) {
                return true;
            }
        }

        // Also check if it's in our operation to request type mapping
        return operationToRequestType.containsKey(str.toLowerCase());
    }

    /**
     * Determine the service name from the endpoint, entity type, or sheet name
     */
    private String determineServiceName(String endpoint, String entityType, String sheetName) {
        String serviceName = "";

        // Try to extract service name from the endpoint
        if (endpoint.contains("/api/")) {
            // Format: /serviceName/api/...
            int apiIndex = endpoint.indexOf("/api/");
            if (apiIndex > 0) {
                serviceName = endpoint.substring(1, apiIndex); // Extract service name
                logger.info("Extracted service name from endpoint: {}", serviceName);
                return serviceName;
            }
        }

        // If we couldn't extract a service name, check if the endpoint already has a service prefix
        if (serviceName.isEmpty()) {
            for (String service : commonServices) {
                if (endpoint.startsWith("/" + service + "/api/") || endpoint.contains("/" + service + "/api/")) {
                    serviceName = service;
                    logger.info("Found existing service name in endpoint: {}", serviceName);
                    return serviceName;
                }
            }
        }

        // If we still don't have a service name, try to infer it from the entity type
        if (serviceName.isEmpty() && !entityType.isEmpty()) {
            // First check exact matches in the entity to service mapping
            String entityLower = entityType.toLowerCase();
            if (entityToService.containsKey(entityLower)) {
                serviceName = entityToService.get(entityLower);
                logger.info("Found service name '{}' for entity '{}' in mapping", serviceName, entityType);
                return serviceName;
            }

            // If no exact match, check for partial matches
            for (Map.Entry<String, String> entry : entityToService.entrySet()) {
                if (entityLower.contains(entry.getKey())) {
                    serviceName = entry.getValue();
                    logger.info("Inferred service name '{}' from entity '{}' containing '{}'",
                               serviceName, entityType, entry.getKey());
                    return serviceName;
                }
            }
        }

        // If we still don't have a service name, try to infer it from the endpoint content
        if (serviceName.isEmpty()) {
            if (endpoint.contains("country") || endpoint.contains("state") || endpoint.contains("master")) {
                serviceName = "core";
            } else if (endpoint.contains("auth") || endpoint.contains("login") || endpoint.contains("user")) {
                serviceName = "auth";
            } else if (endpoint.contains("contact") || endpoint.contains("address")) {
                serviceName = "contact";
            } else if (endpoint.contains("order") || endpoint.contains("product")) {
                serviceName = "order";
            } else {
                // If we still can't determine the service, check the sheet name
                if (sheetName != null) {
                    if (sheetName.toLowerCase().contains("core")) {
                        serviceName = "core";
                    } else if (sheetName.toLowerCase().contains("auth")) {
                        serviceName = "auth";
                    } else if (sheetName.toLowerCase().contains("contact")) {
                        serviceName = "contact";
                    } else if (sheetName.toLowerCase().contains("order")) {
                        serviceName = "order";
                    } else {
                        // Default to "api" if we can't determine the service
                        serviceName = "api";
                    }
                } else {
                    serviceName = "api";
                }
            }
            logger.info("Inferred service name from context: {}", serviceName);
        }

        return serviceName;
    }

    /**
     * Format the endpoint with the correct service name, API pattern, and operation suffix
     */
    private String formatEndpoint(String endpoint, String serviceName, String apiPattern, String requestType, String entityType) {
        // If the endpoint already has the correct format, return it as is
        if (endpoint.contains("/" + serviceName + apiPattern)) {
            logger.info("Endpoint already has the correct format: {}", endpoint);
            return endpoint;
        }

        // If the endpoint starts with the API pattern but missing the service prefix
        if (endpoint.startsWith(apiPattern)) {
            String correctedEndpoint = "/" + serviceName + endpoint;
            logger.info("Adding service prefix to endpoint: {} -> {}", endpoint, correctedEndpoint);
            return correctedEndpoint;
        }

        // If the endpoint doesn't have the API pattern at all
        if (!endpoint.contains(apiPattern)) {
            // Check if the endpoint already has a different API pattern
            boolean hasAnyApiPattern = false;
            for (String pattern : apiPatterns.values()) {
                if (endpoint.contains(pattern)) {
                    hasAnyApiPattern = true;
                    break;
                }
            }

            if (!hasAnyApiPattern) {
                // If it doesn't have any API pattern, add the service and API pattern
                String correctedEndpoint = "/" + serviceName + apiPattern.substring(0, apiPattern.length() - 1) +
                                          (endpoint.startsWith("/") ? endpoint : "/" + endpoint);
                logger.info("Adding service and API pattern to endpoint: {} -> {}", endpoint, correctedEndpoint);
                return correctedEndpoint;
            }
        }

        // Add operation suffix for POST/PUT/DELETE requests if needed
        if (Arrays.asList("post", "put", "delete").contains(requestType)) {
            String operationSuffix = operationSuffixes.getOrDefault(requestType, "/save");

            // Check if the endpoint already has an operation suffix
            boolean hasOperationSuffix = false;
            for (String suffix : operationSuffixes.values()) {
                if (!suffix.isEmpty() && endpoint.endsWith(suffix)) {
                    hasOperationSuffix = true;
                    break;
                }
            }

            if (!hasOperationSuffix && !endpoint.endsWith("/" + entityType)) {
                String correctedEndpoint = endpoint + (endpoint.endsWith("/") ? operationSuffix.substring(1) : operationSuffix);
                logger.info("Adding operation suffix to endpoint: {} -> {}", endpoint, correctedEndpoint);
                return correctedEndpoint;
            }
        }

        return endpoint;
    }

    /**
     * Make the request with the given parameters
     */
    private Response makeRequest(String uri, String requestBody, String requestType, boolean isSpecialEndpoint) {
        // Special handling for special endpoints - use POST method for most operations
        if (isSpecialEndpoint) {
            logger.info("Detected special endpoint at: {}", uri);
            logger.info("Using {} operation type in the request body", requestType.toUpperCase());

            // For special endpoints like /decrypt, we should NOT concatenate the endpoint from the request body
            // Instead, we should use just the URI from Excel
            String fullUrl = uri;
            logger.info("Full URL for special endpoint: {}", fullUrl);

            // Create a request specification
            io.restassured.specification.RequestSpecification requestSpec = given()
                    .contentType("application/json")
                    .body(requestBody)
                    .when()
                    .log().headers();

            // Execute the appropriate request type
            Response response;
            switch (requestType.toLowerCase()) {
                case "get":
                    response = requestSpec.get(fullUrl);
                    break;
                case "put":
                    response = requestSpec.put(fullUrl);
                    break;
                case "delete":
                    response = requestSpec.delete(fullUrl);
                    break;
                case "patch":
                    response = requestSpec.patch(fullUrl);
                    break;
                default: // Default to POST
                    response = requestSpec.post(fullUrl);
                    break;
            }

            logResponse(response);
            return response;
        }

        // For non-special endpoints, use the standard approach
        logger.info("Making {} request to URL: {}", requestType.toUpperCase(), uri);

        // Create a request specification
        io.restassured.specification.RequestSpecification requestSpec = given()
                .contentType("application/json")
                .when()
                .log().headers();

        // Add body for non-GET requests
        if (!requestType.equalsIgnoreCase("get")) {
            requestSpec.body(requestBody);
        }

        // Execute the appropriate request type
        Response response;
        switch (requestType.toLowerCase()) {
            case "get":
                response = requestSpec.get(uri);
                break;
            case "put":
                response = requestSpec.put(uri);
                break;
            case "delete":
                response = requestSpec.delete(uri);
                break;
            case "patch":
                response = requestSpec.patch(uri);
                break;
            default: // Default to POST
                response = requestSpec.body(requestBody).post(uri);
                break;
        }

        logResponse(response);
        return response;
    }

    /**
     * Log the response details
     */
    private void logResponse(Response response) {
        String responseBody = response.getBody().asString();
        logger.info("Request Headers: {}", response.getHeaders());
        logger.info("Response Body: {}", responseBody);
        logger.info("Response Cookies: {}", response.getDetailedCookies());
        logger.info("Status Code: {}", response.getStatusCode());
        logger.info("Content-Type: {}", response.getContentType());
    }
}
