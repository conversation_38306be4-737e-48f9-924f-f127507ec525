package com.rbts.generator;

import com.rbts.config.ConfigManager;
import lombok.extern.slf4j.Slf4j;

/**
 * Automated URL Generator
 * Generates URLs for both direct and proxy patterns based on configuration
 */
@Slf4j
public class AutoUrlGenerator {
    
    private final ConfigManager configManager;
    
    public AutoUrlGenerator() {
        this.configManager = ConfigManager.getInstance();
    }
    
    /**
     * Generate URL for direct pattern
     * Format: {base_url}/{service}/api/{table}/{operation}
     */
    public String generateDirectUrl(String service, String tableName, String operation) {
        String baseUrl = configManager.getProperty("direct.pattern.base_url");
        String urlStructure = configManager.getProperty("direct.pattern.url_structure");
        String operationEndpoint = configManager.getProperty("direct.operation." + operation.toLowerCase());
        
        if (operationEndpoint == null) {
            operationEndpoint = operation.toLowerCase();
        }
        
        String url = urlStructure
                .replace("{base_url}", baseUrl)
                .replace("{service}", service.toLowerCase())
                .replace("{table}", tableName)
                .replace("{operation}", operationEndpoint);
        
        log.debug("Generated direct URL for {}.{}.{}: {}", service, tableName, operation, url);
        return url;
    }
    
    /**
     * Generate URL for proxy pattern (always the same decrypt endpoint)
     */
    public String generateProxyUrl() {
        String baseUrl = configManager.getProperty("proxy.pattern.base_url");
        String endpoint = configManager.getProperty("proxy.pattern.endpoint");
        
        String url = baseUrl + endpoint;
        log.debug("Generated proxy URL: {}", url);
        return url;
    }
    
    /**
     * Generate internal endpoint for proxy pattern
     * Format: /{service}/api/{table}/{operation}
     */
    public String generateProxyInternalEndpoint(String service, String tableName, String operation) {
        String endpointStructure = configManager.getProperty("proxy.internal.endpoint_structure");
        String operationEndpoint = configManager.getProperty("proxy.operation." + operation.toLowerCase());
        
        if (operationEndpoint == null) {
            operationEndpoint = operation.toLowerCase();
        }
        
        String endpoint = endpointStructure
                .replace("{service}", service.toLowerCase())
                .replace("{table}", tableName)
                .replace("{operation}", operationEndpoint);
        
        log.debug("Generated proxy internal endpoint for {}.{}.{}: {}", service, tableName, operation, endpoint);
        return endpoint;
    }
    
    /**
     * Generate URL with ID for GET/DELETE operations (direct pattern)
     */
    public String generateDirectUrlWithId(String service, String tableName, String operation, Object id) {
        String baseUrl = generateDirectUrl(service, tableName, operation);
        
        // For getById and delete operations, append the ID
        if ("get".equalsIgnoreCase(operation) || "delete".equalsIgnoreCase(operation)) {
            return baseUrl + "/" + id;
        }
        
        return baseUrl;
    }
    
    /**
     * Generate internal endpoint with ID for proxy pattern
     */
    public String generateProxyInternalEndpointWithId(String service, String tableName, String operation, Object id) {
        String baseEndpoint = generateProxyInternalEndpoint(service, tableName, operation);
        
        // For getById and delete operations, append the ID
        if ("get".equalsIgnoreCase(operation) || "delete".equalsIgnoreCase(operation)) {
            return baseEndpoint + "/" + id;
        }
        
        return baseEndpoint;
    }
    
    /**
     * Determine service name from table name (if not explicitly provided)
     */
    public String determineServiceFromTable(String tableName) {
        // Check all configured services to find which one contains this table
        for (String serviceName : getAllConfiguredServices()) {
            String tablesProperty = "service.tables." + serviceName;
            String tables = configManager.getProperty(tablesProperty);
            
            if (tables != null) {
                String[] tableArray = tables.split(",");
                for (String table : tableArray) {
                    if (table.trim().equalsIgnoreCase(tableName)) {
                        log.debug("Determined service '{}' for table '{}'", serviceName, tableName);
                        return serviceName;
                    }
                }
            }
        }
        
        log.warn("Could not determine service for table '{}', using 'unknown'", tableName);
        return "unknown";
    }
    
    /**
     * Get pattern for service
     */
    public String getServicePattern(String service) {
        String pattern = configManager.getProperty("service.pattern." + service.toLowerCase());
        if (pattern == null) {
            pattern = configManager.getDefaultPattern();
        }
        
        log.debug("Pattern for service '{}': {}", service, pattern);
        return pattern;
    }
    
    /**
     * Generate complete URL based on pattern detection
     */
    public String generateUrl(String tableName, String operation, Object id) {
        String service = determineServiceFromTable(tableName);
        String pattern = getServicePattern(service);
        
        if ("proxy".equalsIgnoreCase(pattern)) {
            return generateProxyUrl();
        } else {
            if (id != null && ("get".equalsIgnoreCase(operation) || "delete".equalsIgnoreCase(operation))) {
                return generateDirectUrlWithId(service, tableName, operation, id);
            } else {
                return generateDirectUrl(service, tableName, operation);
            }
        }
    }
    
    /**
     * Generate complete URL based on pattern detection (without ID)
     */
    public String generateUrl(String tableName, String operation) {
        return generateUrl(tableName, operation, null);
    }
    
    /**
     * Get all configured services
     */
    private java.util.Set<String> getAllConfiguredServices() {
        java.util.Set<String> services = new java.util.HashSet<>();
        
        for (String propertyName : configManager.getPropertyNames()) {
            if (propertyName.startsWith("service.tables.")) {
                String serviceName = propertyName.substring("service.tables.".length());
                services.add(serviceName);
            }
        }
        
        return services;
    }
    
    /**
     * Get all tables for a service
     */
    public java.util.List<String> getTablesForService(String service) {
        String tablesProperty = "service.tables." + service.toLowerCase();
        String tables = configManager.getProperty(tablesProperty);
        
        if (tables != null && !tables.trim().isEmpty()) {
            return java.util.Arrays.asList(tables.split(","))
                    .stream()
                    .map(String::trim)
                    .collect(java.util.stream.Collectors.toList());
        }
        
        return new java.util.ArrayList<>();
    }
    
    /**
     * Get all configured services
     */
    public java.util.Set<String> getConfiguredServices() {
        return getAllConfiguredServices();
    }
    
    /**
     * Get all configured tables across all services
     */
    public java.util.Map<String, String> getAllTablesWithServices() {
        java.util.Map<String, String> tableServiceMap = new java.util.HashMap<>();
        
        for (String service : getAllConfiguredServices()) {
            java.util.List<String> tables = getTablesForService(service);
            for (String table : tables) {
                tableServiceMap.put(table, service);
            }
        }
        
        return tableServiceMap;
    }
    
    /**
     * Validate if table is configured
     */
    public boolean isTableConfigured(String tableName) {
        return getAllTablesWithServices().containsKey(tableName);
    }
    
    /**
     * Get service for table
     */
    public String getServiceForTable(String tableName) {
        return getAllTablesWithServices().get(tableName);
    }
}
