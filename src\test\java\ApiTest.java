import com.rbts.crud.CrudTestExecutor;
import com.rbts.crud.TestResult;
import com.rbts.utils.ExcelUtils;
import com.rbts.utils.TestDataManager;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

/**
 * Main API Test Class for CRUD Operations
 * This is the entry point for Maven Surefire plugin execution
 */
@Slf4j
public class ApiTest {
    
    private CrudTestExecutor crudTestExecutor;
    private ExcelUtils excelUtils;
    private TestDataManager testDataManager;
    
    // Test configuration
    private static final String EXCEL_FILE_PATH = "data/api_test_data.xlsx";
    private static final String SHEET_NAME = "API_Tests";
    
    @BeforeClass
    public void setUp() {
        log.info("Setting up API Test Suite");
        
        crudTestExecutor = new CrudTestExecutor();
        excelUtils = new ExcelUtils();
        testDataManager = new TestDataManager();
        
        // Create test data if Excel file doesn't exist
        try {
            testDataManager.createTestDataForExcel(EXCEL_FILE_PATH, SHEET_NAME);
            log.info("Test data setup completed");
        } catch (Exception e) {
            log.warn("Could not create test data file: {}", e.getMessage());
        }
    }
    
    /**
     * Test POST operation for User entity
     */
    @Test(priority = 1, description = "Test POST operation for User entity")
    public void testPostUser() {
        log.info("Starting POST test for User entity");
        
        String entity = "user";
        String requestBody = testDataManager.generateUserRequestBody();
        
        // Store request body in Excel for this test
        excelUtils.setCellData(EXCEL_FILE_PATH, SHEET_NAME, 2, 2, requestBody);
        
        TestResult result = crudTestExecutor.executePostTest(
            entity, 
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            2, // Row 2 for user test
            1, // URL column
            2, // Request body column
            3, // Expected result column
            4, // Actual result column
            5  // Status column
        );
        
        log.info("User POST test completed: {}", result.getSummary());
        
        if (!result.isTestPassed()) {
            log.error("User POST test failed:\n{}", result.getDetailedResult());
        }
        
        Assert.assertTrue(result.isTestPassed(), 
            "User POST test failed: " + result.getComparisonMessage());
    }
    
    /**
     * Test POST operation for Product entity
     */
    @Test(priority = 2, description = "Test POST operation for Product entity")
    public void testPostProduct() {
        log.info("Starting POST test for Product entity");
        
        String entity = "product";
        String requestBody = testDataManager.generateProductRequestBody();
        
        // Store request body in Excel for this test
        excelUtils.setCellData(EXCEL_FILE_PATH, SHEET_NAME, 3, 2, requestBody);
        
        TestResult result = crudTestExecutor.executePostTest(
            entity, 
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            3, // Row 3 for product test
            1, 2, 3, 4, 5
        );
        
        log.info("Product POST test completed: {}", result.getSummary());
        
        if (!result.isTestPassed()) {
            log.error("Product POST test failed:\n{}", result.getDetailedResult());
        }
        
        Assert.assertTrue(result.isTestPassed(), 
            "Product POST test failed: " + result.getComparisonMessage());
    }
    
    /**
     * Test POST operation for Country entity
     */
    @Test(priority = 3, description = "Test POST operation for Country entity")
    public void testPostCountry() {
        log.info("Starting POST test for Country entity");
        
        String entity = "country";
        String requestBody = testDataManager.generateCountryRequestBody();
        
        // Store request body in Excel for this test
        excelUtils.setCellData(EXCEL_FILE_PATH, SHEET_NAME, 4, 2, requestBody);
        
        TestResult result = crudTestExecutor.executePostTest(
            entity, 
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            4, // Row 4 for country test
            1, 2, 3, 4, 5
        );
        
        log.info("Country POST test completed: {}", result.getSummary());
        
        if (!result.isTestPassed()) {
            log.error("Country POST test failed:\n{}", result.getDetailedResult());
        }
        
        Assert.assertTrue(result.isTestPassed(), 
            "Country POST test failed: " + result.getComparisonMessage());
    }
    
    /**
     * Test null constraint violation for User entity
     */
    @Test(priority = 4, description = "Test null constraint violation for User entity")
    public void testUserNullConstraintViolation() {
        log.info("Starting null constraint violation test for User entity");
        
        String entity = "user";
        String baseRequestBody = testDataManager.generateUserRequestBody();
        
        TestResult result = crudTestExecutor.executePostNullConstraintTest(
            entity, 
            baseRequestBody,
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            5, // Row 5 for null constraint test
            5  // Status column
        );
        
        log.info("User null constraint test completed: {}", result.getSummary());
        
        if (!result.isTestPassed()) {
            log.error("User null constraint test failed:\n{}", result.getDetailedResult());
        }
        
        Assert.assertTrue(result.isTestPassed(), 
            "Null constraint violation should be correctly rejected by API: " + result.getComparisonMessage());
    }
    
    /**
     * Test unique constraint violation for User entity
     */
    @Test(priority = 5, description = "Test unique constraint violation for User entity")
    public void testUserUniqueConstraintViolation() {
        log.info("Starting unique constraint violation test for User entity");
        
        String entity = "user";
        String baseRequestBody = testDataManager.generateUserRequestBody();
        
        TestResult result = crudTestExecutor.executePostUniqueConstraintTest(
            entity, 
            baseRequestBody,
            EXCEL_FILE_PATH, 
            SHEET_NAME, 
            6, // Row 6 for unique constraint test
            5  // Status column
        );
        
        log.info("User unique constraint test completed: {}", result.getSummary());
        
        if (!result.isTestPassed()) {
            log.error("User unique constraint test failed:\n{}", result.getDetailedResult());
        }
        
        Assert.assertTrue(result.isTestPassed(), 
            "Unique constraint violation should be correctly rejected by API: " + result.getComparisonMessage());
    }
    
    /**
     * Test multiple entities in sequence
     */
    @Test(priority = 6, description = "Test POST operations for multiple entities")
    public void testMultipleEntitiesPost() {
        log.info("Starting multiple entities POST test");
        
        String[] entities = {"user", "country", "product", "order"};
        int rowNum = 7; // Starting from row 7
        
        for (String entity : entities) {
            log.info("Testing POST operation for entity: {}", entity);
            
            String requestBody = testDataManager.generateRequestBodyForEntity(entity);
            excelUtils.setCellData(EXCEL_FILE_PATH, SHEET_NAME, rowNum, 2, requestBody);
            excelUtils.setCellData(EXCEL_FILE_PATH, SHEET_NAME, rowNum, 6, entity);
            
            TestResult result = crudTestExecutor.executePostTest(
                entity, 
                EXCEL_FILE_PATH, 
                SHEET_NAME, 
                rowNum,
                1, 2, 3, 4, 5
            );
            
            log.info("Entity {} POST test result: {}", entity, result.getSummary());
            
            // Log failures but continue with other entities
            if (!result.isTestPassed()) {
                log.error("POST test failed for entity {}: {}", entity, result.getComparisonMessage());
            }
            
            rowNum++;
        }
        
        log.info("Multiple entities POST test completed");
    }
}
