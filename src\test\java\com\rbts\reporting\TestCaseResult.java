package com.rbts.reporting;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Model for test case execution result
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestCaseResult {
    
    /**
     * Table name being tested
     */
    private String tableName;
    
    /**
     * Operation being tested (POST, PUT, PATCH, GET, DELETE)
     */
    private String operation;
    
    /**
     * Description of what is being tested
     */
    private String testCase;
    
    /**
     * Expected result of the test
     */
    private String expectedResult;
    
    /**
     * Actual result of the test
     */
    private String actualResult;
    
    /**
     * Test status (PASS/FAIL)
     */
    private String status;
    
    /**
     * Request body sent to API
     */
    private String requestBody;
    
    /**
     * Response body received from API
     */
    private String responseBody;
    
    /**
     * HTTP status code received
     */
    private int statusCode;
    
    /**
     * Additional test details
     */
    private String testDetails;
    
    /**
     * Error message if test failed
     */
    private String errorMessage;
    
    /**
     * Test execution duration in milliseconds
     */
    private long executionTime;
    
    /**
     * Create a PASS test result
     */
    public static TestCaseResult pass(String tableName, String operation, String testCase, 
                                     String expectedResult, String actualResult) {
        return TestCaseResult.builder()
                .tableName(tableName)
                .operation(operation)
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status("PASS")
                .build();
    }
    
    /**
     * Create a FAIL test result
     */
    public static TestCaseResult fail(String tableName, String operation, String testCase, 
                                     String expectedResult, String actualResult, String errorMessage) {
        return TestCaseResult.builder()
                .tableName(tableName)
                .operation(operation)
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status("FAIL")
                .errorMessage(errorMessage)
                .build();
    }
    
    /**
     * Create a test result for successful POST operation
     */
    public static TestCaseResult postSuccess(String tableName, String requestBody, String responseBody, int statusCode) {
        return TestCaseResult.builder()
                .tableName(tableName)
                .operation("POST")
                .testCase("Create new record with valid data")
                .expectedResult("Status Code: 201, Record created successfully")
                .actualResult(String.format("Status Code: %d, Record created", statusCode))
                .status(statusCode == 201 ? "PASS" : "FAIL")
                .requestBody(requestBody)
                .responseBody(responseBody)
                .statusCode(statusCode)
                .build();
    }
    
    /**
     * Create a test result for constraint violation
     */
    public static TestCaseResult constraintViolation(String tableName, String constraintType, 
                                                    String expectedStatusCode, String requestBody, 
                                                    String responseBody, int actualStatusCode) {
        String testCase = String.format("Constraint Violation - %s", constraintType);
        String expectedResult = String.format("Status Code: %s", expectedStatusCode);
        String actualResult = String.format("Status Code: %d", actualStatusCode);
        String status = expectedStatusCode.equals(String.valueOf(actualStatusCode)) ? "PASS" : "FAIL";
        
        return TestCaseResult.builder()
                .tableName(tableName)
                .operation("POST")
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody)
                .responseBody(responseBody)
                .statusCode(actualStatusCode)
                .build();
    }
    
    /**
     * Create a test result for foreign key validation
     */
    public static TestCaseResult foreignKeyValidation(String tableName, String foreignKeyField, 
                                                     String referencedService, String expectedStatusCode, 
                                                     String requestBody, String responseBody, int actualStatusCode) {
        String testCase = String.format("Foreign Key Validation - %s (references %s service)", foreignKeyField, referencedService);
        String expectedResult = String.format("Status Code: %s", expectedStatusCode);
        String actualResult = String.format("Status Code: %d", actualStatusCode);
        String status = expectedStatusCode.equals(String.valueOf(actualStatusCode)) ? "PASS" : "FAIL";
        
        return TestCaseResult.builder()
                .tableName(tableName)
                .operation("POST")
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .requestBody(requestBody)
                .responseBody(responseBody)
                .statusCode(actualStatusCode)
                .build();
    }
    
    /**
     * Create a test result for field mapping validation
     */
    public static TestCaseResult fieldMappingValidation(String tableName, String operation, 
                                                       String fieldName, String expectedValue, 
                                                       String actualValue, String responseBody) {
        String testCase = String.format("Field Mapping Validation - %s field", fieldName);
        String expectedResult = String.format("Field '%s' should be '%s'", fieldName, expectedValue);
        String actualResult = String.format("Field '%s' is '%s'", fieldName, actualValue);
        String status = expectedValue.equals(actualValue) ? "PASS" : "FAIL";
        
        return TestCaseResult.builder()
                .tableName(tableName)
                .operation(operation)
                .testCase(testCase)
                .expectedResult(expectedResult)
                .actualResult(actualResult)
                .status(status)
                .responseBody(responseBody)
                .build();
    }
    
    /**
     * Check if test passed
     */
    public boolean isPassed() {
        return "PASS".equals(status);
    }
    
    /**
     * Check if test failed
     */
    public boolean isFailed() {
        return "FAIL".equals(status);
    }
    
    @Override
    public String toString() {
        return String.format("TestCaseResult{table='%s', operation='%s', testCase='%s', status='%s'}", 
                tableName, operation, testCase, status);
    }
}
