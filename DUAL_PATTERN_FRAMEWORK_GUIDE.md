# 🚀 Dual Pattern CRUD Testing Framework

## 📋 Overview

This enhanced CRUD testing framework supports **TWO distinct API request patterns**:

1. **Direct Pattern**: Traditional REST API calls with headers and authentication
2. **Proxy Pattern**: Encrypted/wrapped requests through a decrypt endpoint

## 🔄 API Request Patterns

### 🎯 Pattern 1: Direct API Calls

**Characteristics:**
- **URL**: `http://localhost:8071/api/field-configs`
- **Headers**: Custom headers + Bearer token authentication
- **Body**: Direct JSON payload
- **Authentication**: Bearer token in Authorization header

**Example Request:**
```http
POST http://localhost:8071/api/field-configs
Authorization: Bearer your_bearer_token_here
Content-Type: application/json

{
    "fieldType": {
        "id": 18,
        "fieldTypeName": "otp",
        "fieldTypeDesc": "One-time password input",
        "displayName": "OTP",
        "helpText": "Enter one-time password",
        "isActive": false
    },
    "configType": {
        "createdBy": "anonymousUser",
        "createdAt": "2025-05-09T10:13:14.085725",
        "modifiedBy": "anonymousUser",
        "modifiedAt": "2025-05-09T10:13:14.085725",
        "id": 101,
        "configTypeName": "string1",
        "configTypeDesc": "string",
        "displayName": "string",
        "additionalInfo": "string",
        "disclaimerText": "string",
        "placeholderText": "string",
        "isActive": true
    },
    "configName": "12",
    "isActive": true,
    "valueType": "string2"
}
```

### 🔐 Pattern 2: Proxy/Encrypted API Calls

**Characteristics:**
- **URL**: `http://localhost:9762/decrypt` (same for all operations)
- **Headers**: Standard JSON headers
- **Body**: Wrapper containing endpoint, payload, type, tenantId, auth
- **Authentication**: Handled internally by the proxy

**Example Request:**
```http
POST http://localhost:9762/decrypt
Content-Type: application/json

{
    "endpoint": "/authentication/api/user/user-permissions/save",
    "payload": {
        "userId": {"id": 10002},
        "permissionKey": "ACCGR12",
        "isActive": true
    },
    "type": "post",
    "tenantId": "redberyl_redberyltech_com",
    "auth": null
}
```

## ⚙️ Configuration

### config.properties Setup

```properties
# API Request Patterns Configuration
# Pattern 1: Direct API calls
api.pattern.direct.base_url=http://localhost:8071
api.pattern.direct.auth_type=bearer
api.pattern.direct.bearer_token=your_bearer_token_here
api.pattern.direct.headers.Content-Type=application/json
api.pattern.direct.headers.Accept=application/json

# Pattern 2: Proxy API calls
api.pattern.proxy.base_url=http://localhost:9762
api.pattern.proxy.endpoint=/decrypt
api.pattern.proxy.tenant_id=redberyl_redberyltech_com

# Entity to Pattern Mapping
entity.pattern.field_config=direct
entity.pattern.user_permission=proxy
entity.pattern.user=proxy
entity.pattern.product=direct

# Direct Pattern Endpoints
api.direct.post.field_config=/api/field-configs
api.direct.put.field_config=/api/field-configs
api.direct.get.field_config=/api/field-configs
api.direct.delete.field_config=/api/field-configs

# Proxy Pattern Internal Endpoints
api.proxy.post.user_permission=/authentication/api/user/user-permissions/save
api.proxy.put.user_permission=/authentication/api/user/user-permissions/update
api.proxy.get.user_permission=/authentication/api/user/user-permissions/getById
api.proxy.delete.user_permission=/authentication/api/user/user-permissions/delete
```

## 🏗️ Framework Architecture

### Enhanced Components

1. **ApiRequestHandler** - Handles both direct and proxy patterns
2. **ConfigManager** - Enhanced with pattern-specific configurations
3. **CrudTestExecutor** - Uses ApiRequestHandler for pattern-agnostic testing
4. **SampleDataGenerator** - Generates sample data for both patterns

### Pattern Detection

The framework automatically detects which pattern to use based on entity configuration:

```java
// Framework automatically determines pattern
if (configManager.isDirectPattern(entity)) {
    // Use direct API call with headers and authentication
    executeDirectRequest(entity, operation, requestBody);
} else {
    // Use proxy API call with wrapper structure
    executeProxyRequest(entity, operation, requestBody);
}
```

## 🚀 Usage Examples

### 1. Direct Pattern Test

```java
@Test
public void testDirectPatternFieldConfig() {
    String entity = "field_config"; // Configured as direct pattern
    String requestBody = sampleDataGenerator.generateFieldConfigRequestBody();
    
    TestResult result = crudTestExecutor.executePostTest(
        entity, 
        "data/test_data.xlsx", 
        "API_Tests", 
        2, // Row number
        1, 2, 3, 4, 5 // Column indices
    );
    
    Assert.assertTrue(result.isTestPassed());
}
```

### 2. Proxy Pattern Test

```java
@Test
public void testProxyPatternUserPermission() {
    String entity = "user_permission"; // Configured as proxy pattern
    String requestBody = sampleDataGenerator.generateUserPermissionPayload();
    
    TestResult result = crudTestExecutor.executePostTest(
        entity, 
        "data/test_data.xlsx", 
        "API_Tests", 
        3, // Row number
        1, 2, 3, 4, 5 // Column indices
    );
    
    Assert.assertTrue(result.isTestPassed());
}
```

### 3. Mixed Pattern Testing

```java
@Test
public void testMixedPatterns() {
    String[] directEntities = {"field_config", "product"};
    String[] proxyEntities = {"user_permission", "user"};
    
    // Test direct pattern entities
    for (String entity : directEntities) {
        TestResult result = crudTestExecutor.executePostTest(entity, ...);
        log.info("Direct pattern test for {}: {}", entity, result.getSummary());
    }
    
    // Test proxy pattern entities
    for (String entity : proxyEntities) {
        TestResult result = crudTestExecutor.executePostTest(entity, ...);
        log.info("Proxy pattern test for {}: {}", entity, result.getSummary());
    }
}
```

## 📊 Excel Structure for Mixed Patterns

| URL | Request Body | Expected Result | Actual Result | Status | Entity | Pattern |
|-----|-------------|----------------|---------------|--------|--------|---------|
| `/api/field-configs` | `{direct JSON}` | `{expected}` | `[auto-filled]` | `[auto-filled]` | `field_config` | `direct` |
| `/decrypt` | `{payload only}` | `{expected}` | `[auto-filled]` | `[auto-filled]` | `user_permission` | `proxy` |

**Note**: For proxy pattern, store only the payload in the Request Body column. The framework will automatically wrap it with endpoint, type, tenantId, and auth.

## 🔧 Key Features

### ✅ Automatic Pattern Detection
- Framework automatically detects pattern based on entity configuration
- No code changes needed when switching between patterns
- Seamless testing experience

### ✅ Pattern-Specific Request Handling
- **Direct Pattern**: Adds headers, authentication, and makes direct API calls
- **Proxy Pattern**: Wraps payload in proxy structure and calls decrypt endpoint

### ✅ Unified Test Interface
- Same test methods work for both patterns
- Same Excel structure for test data
- Same result validation and defect tracking

### ✅ Configuration-Driven
- Easy to add new entities and assign patterns
- Flexible endpoint and authentication configuration
- Environment-specific settings

## 🎯 Benefits

### 🔄 Pattern Flexibility
- Support for multiple API architectures in one framework
- Easy migration between patterns
- Future-proof design for new patterns

### 🚀 Simplified Testing
- Write tests once, work with any pattern
- Automatic request formatting
- Consistent validation approach

### 📈 Comprehensive Coverage
- Both patterns support all CRUD operations
- Constraint violation testing for both patterns
- Database validation for both patterns

## 🛠️ Setup Instructions

1. **Configure Patterns**: Update config.properties with your endpoints and authentication
2. **Map Entities**: Assign each entity to direct or proxy pattern
3. **Prepare Test Data**: Create Excel files with appropriate request bodies
4. **Run Tests**: Execute test classes - framework handles pattern detection automatically

## 📝 Sample Data Generation

```java
SampleDataGenerator generator = new SampleDataGenerator();

// Generate direct pattern data
String directRequest = generator.generateFieldConfigRequestBody();

// Generate proxy pattern payload
String proxyPayload = generator.generateUserPermissionPayload();

// Generate complete proxy request (for reference)
String completeProxy = generator.generateCompleteProxyRequestBody();
```

This dual-pattern framework provides maximum flexibility while maintaining simplicity and consistency across different API architectures.
