package com.rbts.tests;

import com.rbts.config.ExcelConfigManager;
import com.rbts.config.ExcelConfigTemplateGenerator;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;

/**
 * Test for null endpoint skipping functionality
 * Verifies that operations with null/empty endpoints are skipped
 */
@Slf4j
public class NullEndpointSkippingTest {
    
    private ExcelConfigManager configManager;
    private ExcelConfigTemplateGenerator templateGenerator;
    
    @BeforeClass
    public void setUp() {
        templateGenerator = new ExcelConfigTemplateGenerator();
        log.info("Null Endpoint Skipping Test initialized");
    }
    
    /**
     * Generate Excel template and test null endpoint functionality
     */
    @Test(description = "Test null endpoint skipping functionality", priority = 1)
    public void testNullEndpointSkipping() {
        log.info("Testing null endpoint skipping functionality");
        
        try {
            // Generate Excel template with example table that has null endpoints
            templateGenerator.generateConfigurationTemplate();
            
            // Initialize config manager to load the Excel configuration
            configManager = ExcelConfigManager.getInstance();
            
            // Test the ExampleTable which has some null endpoints
            String tableName = "ExampleTable";
            
            // Get operations to test for ExampleTable
            List<String> operationsToTest = configManager.getOperationsToTestForTable(tableName);
            
            log.info("Operations to test for {}: {}", tableName, operationsToTest);
            
            // Verify that operations with null/empty endpoints are skipped
            Assert.assertTrue(operationsToTest.contains("post"), "POST should be tested (has endpoint)");
            Assert.assertFalse(operationsToTest.contains("put"), "PUT should be skipped (empty endpoint)");
            Assert.assertFalse(operationsToTest.contains("patch"), "PATCH should be skipped (null endpoint)");
            Assert.assertTrue(operationsToTest.contains("get"), "GET should be tested (has endpoint)");
            Assert.assertTrue(operationsToTest.contains("getall"), "GET ALL should be tested (has endpoint)");
            Assert.assertFalse(operationsToTest.contains("delete"), "DELETE should be skipped (empty endpoint)");
            
            log.info("✅ Null endpoint skipping working correctly");
            
            // Test individual operation checking
            Assert.assertTrue(configManager.shouldTestOperation(tableName, "post"), "POST should be tested");
            Assert.assertFalse(configManager.shouldTestOperation(tableName, "put"), "PUT should be skipped");
            Assert.assertFalse(configManager.shouldTestOperation(tableName, "patch"), "PATCH should be skipped");
            Assert.assertTrue(configManager.shouldTestOperation(tableName, "get"), "GET should be tested");
            Assert.assertTrue(configManager.shouldTestOperation(tableName, "getall"), "GET ALL should be tested");
            Assert.assertFalse(configManager.shouldTestOperation(tableName, "delete"), "DELETE should be skipped");
            
            log.info("✅ Individual operation checking working correctly");
            
        } catch (Exception e) {
            log.error("❌ Error testing null endpoint skipping: {}", e.getMessage());
            Assert.fail("Null endpoint skipping test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test operations for tables with all endpoints configured
     */
    @Test(description = "Test operations for tables with all endpoints", dependsOnMethods = "testNullEndpointSkipping", priority = 2)
    public void testFullEndpointConfiguration() {
        log.info("Testing operations for tables with all endpoints configured");
        
        try {
            // Test AddressType which has all endpoints configured
            String tableName = "AddressType";
            
            List<String> operationsToTest = configManager.getOperationsToTestForTable(tableName);
            
            log.info("Operations to test for {}: {}", tableName, operationsToTest);
            
            // Verify that all operations are included when all endpoints are configured
            Assert.assertTrue(operationsToTest.contains("post"), "POST should be tested");
            Assert.assertTrue(operationsToTest.contains("put"), "PUT should be tested");
            Assert.assertTrue(operationsToTest.contains("patch"), "PATCH should be tested");
            Assert.assertTrue(operationsToTest.contains("get"), "GET should be tested");
            Assert.assertTrue(operationsToTest.contains("getall"), "GET ALL should be tested");
            Assert.assertTrue(operationsToTest.contains("delete"), "DELETE should be tested");
            
            log.info("✅ Full endpoint configuration working correctly");
            
        } catch (Exception e) {
            log.error("❌ Error testing full endpoint configuration: {}", e.getMessage());
            Assert.fail("Full endpoint configuration test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test operations for tables not in Table_Endpoints sheet (should use defaults)
     */
    @Test(description = "Test operations for tables not in Table_Endpoints sheet", dependsOnMethods = "testFullEndpointConfiguration", priority = 3)
    public void testDefaultEndpointBehavior() {
        log.info("Testing operations for tables not in Table_Endpoints sheet");
        
        try {
            // Test a table that's not in the Table_Endpoints sheet
            String tableName = "NonExistentTable";
            
            List<String> operationsToTest = configManager.getOperationsToTestForTable(tableName);
            
            log.info("Operations to test for {}: {}", tableName, operationsToTest);
            
            // Verify that all operations are included when table is not in Table_Endpoints sheet
            Assert.assertTrue(operationsToTest.contains("post"), "POST should be tested (default behavior)");
            Assert.assertTrue(operationsToTest.contains("put"), "PUT should be tested (default behavior)");
            Assert.assertTrue(operationsToTest.contains("patch"), "PATCH should be tested (default behavior)");
            Assert.assertTrue(operationsToTest.contains("get"), "GET should be tested (default behavior)");
            Assert.assertTrue(operationsToTest.contains("getall"), "GET ALL should be tested (default behavior)");
            Assert.assertTrue(operationsToTest.contains("delete"), "DELETE should be tested (default behavior)");
            
            log.info("✅ Default endpoint behavior working correctly");
            
        } catch (Exception e) {
            log.error("❌ Error testing default endpoint behavior: {}", e.getMessage());
            Assert.fail("Default endpoint behavior test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test endpoint retrieval for null endpoints
     */
    @Test(description = "Test endpoint retrieval for null endpoints", dependsOnMethods = "testDefaultEndpointBehavior", priority = 4)
    public void testEndpointRetrieval() {
        log.info("Testing endpoint retrieval for null endpoints");
        
        try {
            String tableName = "ExampleTable";
            
            // Test endpoint retrieval for configured endpoints
            String postEndpoint = configManager.getTableEndpoint(tableName, "post");
            Assert.assertNotNull(postEndpoint, "POST endpoint should be returned");
            Assert.assertTrue(postEndpoint.contains("save"), "POST endpoint should contain 'save'");
            
            // Test endpoint retrieval for null endpoints (should return defaults)
            String putEndpoint = configManager.getTableEndpoint(tableName, "put");
            Assert.assertNotNull(putEndpoint, "PUT endpoint should return default");
            Assert.assertTrue(putEndpoint.contains("update"), "PUT endpoint should contain 'update' (default)");
            
            String deleteEndpoint = configManager.getTableEndpoint(tableName, "delete");
            Assert.assertNotNull(deleteEndpoint, "DELETE endpoint should return default");
            Assert.assertTrue(deleteEndpoint.contains("delete"), "DELETE endpoint should contain 'delete' (default)");
            
            log.info("✅ Endpoint retrieval working correctly");
            
        } catch (Exception e) {
            log.error("❌ Error testing endpoint retrieval: {}", e.getMessage());
            Assert.fail("Endpoint retrieval test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test summary
     */
    @Test(description = "Null endpoint skipping test summary", dependsOnMethods = {"testNullEndpointSkipping", "testFullEndpointConfiguration", "testDefaultEndpointBehavior", "testEndpointRetrieval"}, priority = 5)
    public void testSummary() {
        log.info("=== NULL ENDPOINT SKIPPING TEST SUMMARY ===");
        log.info("✅ Null endpoint skipping - PASSED");
        log.info("✅ Full endpoint configuration - PASSED");
        log.info("✅ Default endpoint behavior - PASSED");
        log.info("✅ Endpoint retrieval - PASSED");
        log.info("=== ALL NULL ENDPOINT TESTS PASSED ===");
        
        log.info("🎉 Null endpoint skipping functionality is working perfectly!");
        log.info("📋 How it works:");
        log.info("   ✅ Empty endpoint cell → Skip testing that operation");
        log.info("   ✅ 'null' endpoint value → Skip testing that operation");
        log.info("   ✅ Configured endpoint → Test that operation");
        log.info("   ✅ No table configuration → Test all operations (use defaults)");
        
        log.info("📊 Benefits:");
        log.info("   🎯 Fine-grained control over which operations to test");
        log.info("   🚀 Skip operations that don't exist for specific tables");
        log.info("   📝 Easy configuration through Excel cells");
        log.info("   🔧 Automatic default behavior for unconfigured tables");
        
        Assert.assertTrue(true, "All null endpoint skipping tests passed successfully");
    }
}
