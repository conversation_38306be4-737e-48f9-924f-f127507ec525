<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="650a8177-d94a-4fba-b6cc-7b36b0977dc4" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2xzFCeODExmpcqwfPE8sO2ZU0yf" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "TestNG.ApiTest.executor": "Run",
    "last_directory_selection": "C:/Users/<USER>/IdeaProjects/RestAssuredApiTestingFramework/src/test/java",
    "last_opened_file_path": "C:/Users/<USER>/IdeaProjects/RestAssuredApiTestingFramework",
    "onboarding.tips.debug.path": "C:/Users/<USER>/IdeaProjects/RestAssuredApiTestingFramework/src/main/java/com/rbts/base/Main.java"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\RestAssuredApiTestingFramework\src\test\java\com\rbts" />
      <recent name="C:\Users\<USER>\IdeaProjects\RestAssuredApiTestingFramework\src\main\java\com\rbts\base\post" />
      <recent name="C:\Users\<USER>\IdeaProjects\RestAssuredApiTestingFramework\src\main\resources" />
      <recent name="C:\Users\<USER>\IdeaProjects\RestAssuredApiTestingFramework\src\main\java\com\rbts\utils" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.rbts.constants" />
      <recent name="testNG" />
      <recent name="com.rbts.testNG" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="ApiTest" type="TestNG" temporary="true" nameIsGenerated="true">
      <module name="RestAssuredApiTestingFramework" />
      <option name="PACKAGE_NAME" value="" />
      <option name="MAIN_CLASS_NAME" value="ApiTest" />
      <option name="TEST_OBJECT" value="CLASS" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="TestNG.ApiTest" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="650a8177-d94a-4fba-b6cc-7b36b0977dc4" name="Changes" comment="" />
      <created>1748932869889</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748932869889</updated>
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rbts/base/Main.java</url>
          <line>13</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>