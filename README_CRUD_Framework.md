# CRUD Testing Framework with Database Validation

## 🚀 Overview

This comprehensive CRUD testing framework provides automated testing for REST APIs with database validation, constraint testing, and defect tracking. It supports POST, PUT, GET, DELETE, and PATCH operations with Excel-driven test data management.

## 🏗️ Architecture

### Core Components

1. **CrudTestExecutor** - Main orchestrator for CRUD operations
2. **DatabaseManager** - Database operations and JSON conversion with foreign key resolution
3. **DefectManager** - Unique defect ID generation and tracking
4. **JsonComparator** - Advanced JSON comparison with detailed difference reporting
5. **ConstraintTestGenerator** - Automated constraint violation testing
6. **ConfigManager** - Centralized configuration management
7. **TestDataManager** - Test data generation and management utilities

## 📋 Features

### ✅ POST Operation Testing
- **Normal POST Testing**: Execute API → Extract ID → Query Database → Compare JSON
- **Null Constraint Violation**: Automatically test null constraint enforcement
- **Unique Constraint Violation**: Test unique constraint enforcement
- **Foreign Key Resolution**: Handle foreign keys in database responses
- **Defect Tracking**: Generate unique defect IDs for failed tests

### ✅ Database Integration
- **Automatic ID Extraction**: Extract primary key from API responses
- **Foreign Key Resolution**: Convert foreign key IDs to full objects
- **JSON Conversion**: Convert database results to JSON format
- **Connection Management**: Robust database connection handling

### ✅ Excel Integration
- **Test Data Management**: Read test data from Excel files
- **Result Storage**: Store actual results and test status in Excel
- **Color-coded Results**: Visual status indication (Pass/Fail/Error)
- **Dynamic Data Generation**: Generate test data using JavaFaker

### ✅ Defect Management
- **Unique Defect IDs**: Format: `D_tablename_operation_type_001`
- **Automatic Tracking**: Store defect details in database
- **Status Management**: Track defect lifecycle
- **Detailed Logging**: Comprehensive defect information

## 🔧 Configuration

### config.properties
```properties
# Base URL
baseURI_Qa=http://localhost:8765

# Database Configuration
JDBC_URL=******************************************
JDBC_USER=postgres
JDBC_PASSWORD=postgres

# API Endpoints
api.post.user=/api/user/save
api.get.user=/api/user/getById
api.put.user=/api/user/update
api.delete.user=/api/user/delete

# Database Mappings
table.user=users
primarykey.user=user_id

# Foreign Key Mappings
foreignkey.order.user_id=users.user_id
foreignkey.product.country_id=countries.country_id

# Constraint Configurations
null.constraint.user=username,email
unique.constraint.user=username,email

# Defect Configuration
defect.table.name=defects
defect.id.prefix=D_
```

## 📊 Excel Structure

### Test Data Sheet Format
| URL | Request Body | Expected Result | Actual Result | Status | Entity |
|-----|-------------|----------------|---------------|--------|--------|
| /api/user/save | {"username":"test"} | {"user_id":1,"username":"test"} | [Auto-filled] | [Auto-filled] | user |

## 🚀 Usage Examples

### 1. Basic POST Test
```java
@Test
public void testPostUser() {
    CrudTestExecutor executor = new CrudTestExecutor();
    
    TestResult result = executor.executePostTest(
        "user",                    // Entity name
        "data/test_data.xlsx",     // Excel file path
        "POST_Tests",              // Sheet name
        2,                         // Row number
        1,                         // URL column
        2,                         // Request body column
        3,                         // Expected result column
        4,                         // Actual result column
        5                          // Status column
    );
    
    Assert.assertTrue(result.isTestPassed());
}
```

### 2. Null Constraint Violation Test
```java
@Test
public void testNullConstraintViolation() {
    String baseRequestBody = "{\"username\":\"test\",\"email\":\"<EMAIL>\"}";
    
    TestResult result = executor.executePostNullConstraintTest(
        "user",
        baseRequestBody,
        "data/test_data.xlsx",
        "POST_Tests",
        3,
        5
    );
    
    // Should pass because API correctly rejects null constraint violation
    Assert.assertTrue(result.isTestPassed());
}
```

### 3. Unique Constraint Violation Test
```java
@Test
public void testUniqueConstraintViolation() {
    String baseRequestBody = "{\"username\":\"existing_user\",\"email\":\"<EMAIL>\"}";
    
    TestResult result = executor.executePostUniqueConstraintTest(
        "user",
        baseRequestBody,
        "data/test_data.xlsx",
        "POST_Tests",
        4,
        5
    );
    
    // Should pass because API correctly rejects unique constraint violation
    Assert.assertTrue(result.isTestPassed());
}
```

## 🔄 Test Execution Flow

### Normal POST Test Flow
1. **Read Test Data** from Excel (URL, Request Body, Expected Result)
2. **Execute API Request** using RestAssured
3. **Extract ID** from API response
4. **Query Database** using extracted ID
5. **Convert Database Result** to JSON with foreign key resolution
6. **Compare JSONs** (Expected vs Actual)
7. **Update Excel** with actual result and status
8. **Generate Defect** if test fails

### Constraint Violation Test Flow
1. **Generate Violation Request** based on constraint configuration
2. **Execute API Request** expecting failure (4xx status)
3. **Validate API Response** correctly rejects the request
4. **Update Test Status** based on API behavior
5. **Generate Defect** if constraint not enforced

## 🎯 Key Benefits

### 🔍 Comprehensive Testing
- **End-to-End Validation**: API → Database → JSON comparison
- **Constraint Testing**: Automated null and unique constraint validation
- **Foreign Key Handling**: Automatic resolution of related data

### 📈 Detailed Reporting
- **Excel Integration**: Visual test results with color coding
- **Defect Tracking**: Automatic defect generation with unique IDs
- **Detailed Logging**: Comprehensive test execution logs

### 🔧 Reusability
- **Entity Agnostic**: Works with any entity by configuration
- **Configurable**: All endpoints, tables, and constraints configurable
- **Extensible**: Easy to add new operations and test types

### 🚀 Automation
- **Data Generation**: Automatic test data generation using JavaFaker
- **Constraint Detection**: Automatic constraint violation test generation
- **Result Management**: Automatic result storage and status updates

## 📝 Configuration Examples

### Adding New Entity
```properties
# API Endpoints
api.post.customer=/api/customer/save
api.get.customer=/api/customer/getById

# Database Mapping
table.customer=customers
primarykey.customer=customer_id

# Constraints
null.constraint.customer=customer_name,email
unique.constraint.customer=email,customer_code
```

### Foreign Key Configuration
```properties
# Foreign key: order.customer_id references customers.customer_id
foreignkey.order.customer_id=customers.customer_id
```

## 🛠️ Setup Instructions

1. **Configure Database**: Update JDBC settings in config.properties
2. **Create Tables**: Ensure database tables exist with proper constraints
3. **Prepare Excel**: Create test data Excel file with required structure
4. **Run Tests**: Execute test classes using TestNG

## 📋 Requirements

- Java 21+
- PostgreSQL Database
- Maven Dependencies (RestAssured, TestNG, Apache POI, Jackson, etc.)
- Excel file for test data

This framework provides a robust, scalable solution for comprehensive CRUD API testing with database validation and automated defect tracking.
