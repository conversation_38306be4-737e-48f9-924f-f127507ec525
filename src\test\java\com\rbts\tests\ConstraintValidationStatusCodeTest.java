package com.rbts.tests;

import com.rbts.config.ExcelConfigManager;
import com.rbts.config.ExcelConfigTemplateGenerator;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

/**
 * Test for constraint validation status codes
 * Verifies specific status codes for different constraint violations
 */
@Slf4j
public class ConstraintValidationStatusCodeTest {
    
    private ExcelConfigManager configManager;
    private ExcelConfigTemplateGenerator templateGenerator;
    
    @BeforeClass
    public void setUp() {
        templateGenerator = new ExcelConfigTemplateGenerator();
        log.info("Constraint Validation Status Code Test initialized");
    }
    
    /**
     * Generate Excel template and test constraint validation status codes
     */
    @Test(description = "Test constraint validation status codes", priority = 1)
    public void testConstraintValidationStatusCodes() {
        log.info("Testing constraint validation status codes");
        
        try {
            // Generate Excel template with constraint validation status codes
            templateGenerator.generateConfigurationTemplate();
            
            // Initialize config manager to load the Excel configuration
            configManager = ExcelConfigManager.getInstance();
            
            // Test unique constraint validation status code
            String uniqueConstraintStatus = configManager.getValidationConfig("validation.unique_constraint.expected_status");
            Assert.assertEquals(uniqueConstraintStatus, "701", "Unique constraint status code should be 701");
            log.info("✅ Unique constraint status code: {}", uniqueConstraintStatus);
            
            // Test null constraint validation status code
            String nullConstraintStatus = configManager.getValidationConfig("validation.null_constraint.expected_status");
            Assert.assertEquals(nullConstraintStatus, "700", "Null constraint status code should be 700");
            log.info("✅ Null constraint status code: {}", nullConstraintStatus);
            
            // Test foreign key same service status code
            String foreignKeySameServiceStatus = configManager.getValidationConfig("validation.foreign_key_same_service.expected_status");
            Assert.assertEquals(foreignKeySameServiceStatus, "404", "Foreign key same service status code should be 404");
            log.info("✅ Foreign key same service status code: {}", foreignKeySameServiceStatus);
            
            // Test foreign key other service status code
            String foreignKeyOtherServiceStatus = configManager.getValidationConfig("validation.foreign_key_other_service.expected_status");
            Assert.assertEquals(foreignKeyOtherServiceStatus, "702", "Foreign key other service status code should be 702");
            log.info("✅ Foreign key other service status code: {}", foreignKeyOtherServiceStatus);
            
        } catch (Exception e) {
            log.error("❌ Error testing constraint validation status codes: {}", e.getMessage());
            Assert.fail("Constraint validation status code test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test service detection for tables
     */
    @Test(description = "Test service detection for tables", dependsOnMethods = "testConstraintValidationStatusCodes", priority = 2)
    public void testServiceDetection() {
        log.info("Testing service detection for tables");
        
        try {
            // Test service detection for different tables
            String contactService = configManager.getServiceForTable("AddressType");
            Assert.assertEquals(contactService, "contact", "AddressType should belong to contact service");
            log.info("✅ AddressType belongs to service: {}", contactService);
            
            String authService = configManager.getServiceForTable("User");
            Assert.assertEquals(authService, "authentication", "User should belong to authentication service");
            log.info("✅ User belongs to service: {}", authService);
            
            String coreService = configManager.getServiceForTable("Country");
            Assert.assertEquals(coreService, "core", "Country should belong to core service");
            log.info("✅ Country belongs to service: {}", coreService);
            
            // Test non-existent table
            String nonExistentService = configManager.getServiceForTable("NonExistentTable");
            Assert.assertNull(nonExistentService, "Non-existent table should return null service");
            log.info("✅ Non-existent table service detection: {}", nonExistentService);
            
        } catch (Exception e) {
            log.error("❌ Error testing service detection: {}", e.getMessage());
            Assert.fail("Service detection test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test constraint violation status code determination
     */
    @Test(description = "Test constraint violation status code determination", dependsOnMethods = "testServiceDetection", priority = 3)
    public void testConstraintViolationStatusCodeDetermination() {
        log.info("Testing constraint violation status code determination");
        
        try {
            // Test unique constraint
            String uniqueStatus = configManager.getConstraintViolationStatusCode("unique_constraint", "contact", null);
            Assert.assertEquals(uniqueStatus, "701", "Unique constraint should return 701");
            log.info("✅ Unique constraint status: {}", uniqueStatus);
            
            // Test null constraint
            String nullStatus = configManager.getConstraintViolationStatusCode("null_constraint", "contact", null);
            Assert.assertEquals(nullStatus, "700", "Null constraint should return 700");
            log.info("✅ Null constraint status: {}", nullStatus);
            
            // Test foreign key same service
            String foreignKeySameStatus = configManager.getConstraintViolationStatusCode("foreign_key_invalid", "contact", "contact");
            Assert.assertEquals(foreignKeySameStatus, "404", "Foreign key same service should return 404");
            log.info("✅ Foreign key same service status: {}", foreignKeySameStatus);
            
            // Test foreign key different service
            String foreignKeyDifferentStatus = configManager.getConstraintViolationStatusCode("foreign_key_invalid", "contact", "authentication");
            Assert.assertEquals(foreignKeyDifferentStatus, "702", "Foreign key different service should return 702");
            log.info("✅ Foreign key different service status: {}", foreignKeyDifferentStatus);
            
        } catch (Exception e) {
            log.error("❌ Error testing constraint violation status code determination: {}", e.getMessage());
            Assert.fail("Constraint violation status code determination test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test automatic table-based constraint validation
     */
    @Test(description = "Test automatic table-based constraint validation", dependsOnMethods = "testConstraintViolationStatusCodeDetermination", priority = 4)
    public void testTableBasedConstraintValidation() {
        log.info("Testing automatic table-based constraint validation");
        
        try {
            // Test scenario: Order service table with foreign key to Order service table (same service)
            String orderToOrderStatus = configManager.getConstraintViolationStatusCodeForTable("foreign_key_invalid", "Order", "OrderStatus");
            Assert.assertEquals(orderToOrderStatus, "404", "Order to OrderStatus (same service) should return 404");
            log.info("✅ Order -> OrderStatus (same service): {}", orderToOrderStatus);
            
            // Test scenario: Order service table with foreign key to User table (different service)
            String orderToUser = configManager.getConstraintViolationStatusCodeForTable("foreign_key_invalid", "Order", "User");
            Assert.assertEquals(orderToUser, "702", "Order to User (different service) should return 702");
            log.info("✅ Order -> User (different service): {}", orderToUser);
            
            // Test scenario: Contact service table with foreign key to Country table (different service)
            String contactToCountry = configManager.getConstraintViolationStatusCodeForTable("foreign_key_invalid", "Contact", "Country");
            Assert.assertEquals(contactToCountry, "702", "Contact to Country (different service) should return 702");
            log.info("✅ Contact -> Country (different service): {}", contactToCountry);
            
            // Test unique constraint for any table
            String uniqueForContact = configManager.getConstraintViolationStatusCodeForTable("unique_constraint", "Contact", null);
            Assert.assertEquals(uniqueForContact, "701", "Unique constraint should always return 701");
            log.info("✅ Unique constraint for Contact: {}", uniqueForContact);
            
        } catch (Exception e) {
            log.error("❌ Error testing table-based constraint validation: {}", e.getMessage());
            Assert.fail("Table-based constraint validation test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test constraint violation expectation checking
     */
    @Test(description = "Test constraint violation expectation checking", dependsOnMethods = "testTableBasedConstraintValidation", priority = 5)
    public void testConstraintViolationExpectation() {
        log.info("Testing constraint violation expectation checking");
        
        try {
            // Test unique constraint expectation
            boolean uniqueExpected701 = configManager.isConstraintViolationExpected("unique_constraint", 701, "Contact", null);
            Assert.assertTrue(uniqueExpected701, "Status code 701 should be expected for unique constraint");
            
            boolean uniqueExpected400 = configManager.isConstraintViolationExpected("unique_constraint", 400, "Contact", null);
            Assert.assertFalse(uniqueExpected400, "Status code 400 should not be expected for unique constraint");
            
            // Test null constraint expectation
            boolean nullExpected700 = configManager.isConstraintViolationExpected("null_constraint", 700, "Contact", null);
            Assert.assertTrue(nullExpected700, "Status code 700 should be expected for null constraint");
            
            // Test foreign key same service expectation
            boolean foreignKeySameExpected404 = configManager.isConstraintViolationExpected("foreign_key_invalid", 404, "Order", "OrderStatus");
            Assert.assertTrue(foreignKeySameExpected404, "Status code 404 should be expected for foreign key same service");
            
            // Test foreign key different service expectation
            boolean foreignKeyDifferentExpected702 = configManager.isConstraintViolationExpected("foreign_key_invalid", 702, "Order", "User");
            Assert.assertTrue(foreignKeyDifferentExpected702, "Status code 702 should be expected for foreign key different service");
            
            log.info("✅ Constraint violation expectation checking working correctly");
            
        } catch (Exception e) {
            log.error("❌ Error testing constraint violation expectation: {}", e.getMessage());
            Assert.fail("Constraint violation expectation test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test summary
     */
    @Test(description = "Constraint validation status code test summary", dependsOnMethods = {"testConstraintValidationStatusCodes", "testServiceDetection", "testConstraintViolationStatusCodeDetermination", "testTableBasedConstraintValidation", "testConstraintViolationExpectation"}, priority = 6)
    public void testSummary() {
        log.info("=== CONSTRAINT VALIDATION STATUS CODE TEST SUMMARY ===");
        log.info("✅ Constraint validation status codes - PASSED");
        log.info("✅ Service detection - PASSED");
        log.info("✅ Constraint violation status code determination - PASSED");
        log.info("✅ Table-based constraint validation - PASSED");
        log.info("✅ Constraint violation expectation checking - PASSED");
        log.info("=== ALL CONSTRAINT VALIDATION TESTS PASSED ===");
        
        log.info("🎉 Constraint validation status codes are working perfectly!");
        log.info("📋 Status Code Configuration:");
        log.info("   🔴 Unique Constraint Violation: 701");
        log.info("   🔴 Null Constraint Violation: 700");
        log.info("   🔴 Foreign Key Same Service: 404");
        log.info("   🔴 Foreign Key Other Service: 702");
        
        log.info("📊 How it works:");
        log.info("   🎯 Framework automatically detects which service each table belongs to");
        log.info("   🎯 For foreign key violations, compares current service vs foreign key service");
        log.info("   🎯 Returns appropriate status code based on constraint type and service relationship");
        log.info("   🎯 Easy configuration through Excel Validation_Config sheet");
        
        // Print configuration summary
        configManager.printConfigurationSummary();
        
        Assert.assertTrue(true, "All constraint validation status code tests passed successfully");
    }
}
