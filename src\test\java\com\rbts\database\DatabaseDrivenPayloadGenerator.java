package com.rbts.database;

import com.rbts.database.DatabaseSchemaReader.TableSchema;
import com.rbts.database.DatabaseSchemaReader.ColumnInfo;
import com.rbts.database.DatabaseSchemaReader.ForeignKeyInfo;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * Database-driven payload generator that creates request payloads
 * based on actual database table schemas and column names
 */
@Slf4j
public class DatabaseDrivenPayloadGenerator {
    
    private final DatabaseSchemaReader schemaReader;
    private final Random random;
    
    public DatabaseDrivenPayloadGenerator(DatabaseSchemaReader schemaReader) {
        this.schemaReader = schemaReader;
        this.random = new Random();
    }
    
    /**
     * Generate POST request payload based on actual database schema
     */
    public JSONObject generatePostPayload(String tableName) {
        log.info("🔧 Generating POST payload for table: {}", tableName);
        
        TableSchema schema = schemaReader.getTableSchema(tableName);
        JSONObject payload = new JSONObject();
        
        for (ColumnInfo column : schema.getColumns()) {
            String columnName = column.getColumnName();
            
            // Skip auto-increment primary keys for POST requests
            if (column.isAutoIncrement() && schema.isPrimaryKey(columnName)) {
                log.debug("⏭️ Skipping auto-increment primary key: {}", columnName);
                continue;
            }
            
            // Skip audit fields that are system-generated
            if (isSystemGeneratedField(columnName)) {
                log.debug("⏭️ Skipping system-generated field: {}", columnName);
                continue;
            }
            
            // Generate value based on column type and constraints
            Object value = generateValueForColumn(column, schema, tableName);
            if (value != null) {
                try {
                    payload.put(columnName, value);
                    log.debug("📝 Added to payload: {} = {}", columnName, value);
                } catch (Exception e) {
                    log.error("Error adding field to payload: {} = {}", columnName, value);
                }
            }
        }
        
        log.info("✅ Generated POST payload for {}: {} fields", tableName, payload.length());
        return payload;
    }
    
    /**
     * Generate PUT request payload based on actual database schema
     */
    public JSONObject generatePutPayload(String tableName, Object id) {
        log.info("🔧 Generating PUT payload for table: {} with ID: {}", tableName, id);
        
        TableSchema schema = schemaReader.getTableSchema(tableName);
        JSONObject payload = new JSONObject();
        
        for (ColumnInfo column : schema.getColumns()) {
            String columnName = column.getColumnName();
            
            // Include primary key for PUT requests
            if (schema.isPrimaryKey(columnName)) {
                try {
                    payload.put(columnName, id);
                    log.debug("🔑 Added primary key: {} = {}", columnName, id);
                } catch (Exception e) {
                    log.error("Error adding primary key to payload: {} = {}", columnName, id);
                }
                continue;
            }
            
            // Skip system-generated fields except for lastModifiedAt
            if (isSystemGeneratedField(columnName) && !columnName.toLowerCase().contains("modified")) {
                log.debug("⏭️ Skipping system-generated field: {}", columnName);
                continue;
            }
            
            // Generate value based on column type and constraints
            Object value = generateValueForColumn(column, schema, tableName);
            if (value != null) {
                try {
                    payload.put(columnName, value);
                    log.debug("📝 Added to payload: {} = {}", columnName, value);
                } catch (Exception e) {
                    log.error("Error adding field to payload: {} = {}", columnName, value);
                }
            }
        }
        
        log.info("✅ Generated PUT payload for {}: {} fields", tableName, payload.length());
        return payload;
    }
    
    /**
     * Generate constraint violation payload for testing
     */
    public JSONObject generateConstraintViolationPayload(String tableName, String constraintType, String fieldName) {
        log.info("🔧 Generating {} constraint violation payload for {}.{}", constraintType, tableName, fieldName);
        
        JSONObject payload = generatePostPayload(tableName);
        
        // Override the specific field with constraint violation value
        try {
            switch (constraintType.toLowerCase()) {
                case "null":
                case "null_constraint":
                    payload.put(fieldName, JSONObject.NULL);
                    log.info("🚫 Set null constraint violation: {} = null", fieldName);
                    break;

                case "unique":
                case "unique_constraint":
                    payload.put(fieldName, generateDuplicateValue(tableName, fieldName));
                    log.info("🔄 Set unique constraint violation: {} = duplicate value", fieldName);
                    break;

                case "foreign_key":
                case "fk_violation":
                    payload.put(fieldName, 999999); // Non-existent ID
                    log.info("🔗 Set foreign key violation: {} = 999999", fieldName);
                    break;

                case "validation_error":
                    payload.put(fieldName, generateInvalidValue(tableName, fieldName));
                    log.info("❌ Set validation error: {} = invalid value", fieldName);
                    break;
            }
        } catch (Exception e) {
            log.error("Error setting constraint violation value: {} = {}", fieldName, e.getMessage());
        }
        
        return payload;
    }
    
    /**
     * Generate value for a specific column based on its type and constraints
     */
    private Object generateValueForColumn(ColumnInfo column, TableSchema schema, String tableName) {
        String columnName = column.getColumnName();
        String dataType = column.getDataType().toUpperCase();
        
        // Handle foreign keys
        if (schema.isForeignKey(columnName)) {
            return generateForeignKeyValue(columnName, schema);
        }
        
        // Handle specific column names
        Object specificValue = generateValueForSpecificColumn(columnName, tableName);
        if (specificValue != null) {
            return specificValue;
        }
        
        // Handle by data type
        switch (dataType) {
            case "VARCHAR":
            case "CHAR":
            case "TEXT":
            case "STRING":
                return generateStringValue(columnName, column.getColumnSize());
                
            case "INT":
            case "INTEGER":
            case "BIGINT":
            case "SMALLINT":
                return generateIntegerValue(columnName);
                
            case "DECIMAL":
            case "NUMERIC":
            case "DOUBLE":
            case "FLOAT":
                return generateDecimalValue(columnName);
                
            case "BOOLEAN":
            case "BIT":
                return generateBooleanValue();
                
            case "DATE":
                return generateDateValue();
                
            case "DATETIME":
            case "TIMESTAMP":
                return generateTimestampValue();
                
            case "TIME":
                return generateTimeValue();
                
            default:
                log.warn("⚠️ Unknown data type {} for column {}, using string", dataType, columnName);
                return generateStringValue(columnName, 50);
        }
    }
    
    /**
     * Generate value for specific column names (business logic)
     */
    private Object generateValueForSpecificColumn(String columnName, String tableName) {
        String lowerColumnName = columnName.toLowerCase();
        
        // Email fields
        if (lowerColumnName.contains("email")) {
            return "test" + System.currentTimeMillis() + "@example.com";
        }
        
        // Name fields
        if (lowerColumnName.contains("name") && !lowerColumnName.contains("username")) {
            if (lowerColumnName.contains("first")) {
                return "John";
            } else if (lowerColumnName.contains("last")) {
                return "Doe";
            } else if (lowerColumnName.contains("bundle")) {
                return "Gaming Bundle " + random.nextInt(1000);
            } else if (lowerColumnName.contains("product")) {
                return "Test Product " + random.nextInt(1000);
            } else {
                return "Test " + columnName + " " + random.nextInt(1000);
            }
        }
        
        // Username fields
        if (lowerColumnName.contains("username")) {
            return "user" + System.currentTimeMillis();
        }
        
        // Password fields
        if (lowerColumnName.contains("password")) {
            return "SecurePassword123!";
        }
        
        // Phone fields
        if (lowerColumnName.contains("phone")) {
            return "+1234567890";
        }
        
        // Address fields
        if (lowerColumnName.contains("address")) {
            return "123 Test Street, Test City";
        }
        
        // Price fields
        if (lowerColumnName.contains("price")) {
            return 29.99 + random.nextDouble() * 100;
        }
        
        // Quantity fields
        if (lowerColumnName.contains("quantity")) {
            return 1 + random.nextInt(10);
        }
        
        // Discount fields
        if (lowerColumnName.contains("discount")) {
            return random.nextInt(50); // 0-50% discount
        }
        
        // Status fields
        if (lowerColumnName.contains("status")) {
            if (tableName.toLowerCase().contains("order")) {
                return "PENDING";
            } else {
                return "ACTIVE";
            }
        }
        
        // Description fields
        if (lowerColumnName.contains("description")) {
            return "Test description for " + tableName;
        }
        
        return null; // Let data type handling take over
    }
    
    /**
     * Generate foreign key value
     */
    private Object generateForeignKeyValue(String columnName, TableSchema schema) {
        ForeignKeyInfo fkInfo = schema.getForeignKeys().stream()
                .filter(fk -> fk.getColumnName().equalsIgnoreCase(columnName))
                .findFirst()
                .orElse(null);
        
        if (fkInfo != null) {
            log.debug("🔗 Foreign key {} references {}.{}", columnName, fkInfo.getReferencedTable(), fkInfo.getReferencedColumn());
            // For testing, use a valid-looking ID
            return 1;
        }
        
        return 1; // Default FK value
    }
    
    /**
     * Generate string value
     */
    private String generateStringValue(String columnName, int maxLength) {
        String baseValue = "Test " + columnName.replaceAll("_", " ");
        String uniqueSuffix = " " + System.currentTimeMillis() % 10000;
        
        String fullValue = baseValue + uniqueSuffix;
        
        // Truncate if too long
        if (maxLength > 0 && fullValue.length() > maxLength) {
            return fullValue.substring(0, maxLength - 3) + "...";
        }
        
        return fullValue;
    }
    
    /**
     * Generate integer value
     */
    private Integer generateIntegerValue(String columnName) {
        if (columnName.toLowerCase().contains("quantity")) {
            return 1 + random.nextInt(10);
        } else if (columnName.toLowerCase().contains("count")) {
            return random.nextInt(100);
        } else {
            return 1 + random.nextInt(1000);
        }
    }
    
    /**
     * Generate decimal value
     */
    private Double generateDecimalValue(String columnName) {
        if (columnName.toLowerCase().contains("price")) {
            return 10.0 + random.nextDouble() * 1000;
        } else if (columnName.toLowerCase().contains("discount")) {
            return random.nextDouble() * 50; // 0-50%
        } else {
            return random.nextDouble() * 100;
        }
    }
    
    /**
     * Generate boolean value
     */
    private Boolean generateBooleanValue() {
        return random.nextBoolean();
    }
    
    /**
     * Generate date value
     */
    private String generateDateValue() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
    
    /**
     * Generate timestamp value
     */
    private String generateTimestampValue() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * Generate time value
     */
    private String generateTimeValue() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
    }
    
    /**
     * Check if field is system-generated
     */
    private boolean isSystemGeneratedField(String columnName) {
        String lowerName = columnName.toLowerCase();
        return lowerName.contains("created_at") || 
               lowerName.contains("createdat") ||
               lowerName.contains("created_by") ||
               lowerName.contains("createdby") ||
               lowerName.contains("updated_at") ||
               lowerName.contains("updatedat") ||
               lowerName.contains("modified_at") ||
               lowerName.contains("modifiedat") ||
               lowerName.contains("version") ||
               lowerName.contains("timestamp");
    }
    
    /**
     * Generate duplicate value for unique constraint testing
     */
    private Object generateDuplicateValue(String tableName, String fieldName) {
        // This would typically query the database for an existing value
        // For testing purposes, return a commonly used value
        if (fieldName.toLowerCase().contains("email")) {
            return "<EMAIL>";
        } else if (fieldName.toLowerCase().contains("username")) {
            return "duplicateuser";
        } else {
            return "DUPLICATE_VALUE";
        }
    }
    
    /**
     * Generate invalid value for validation testing
     */
    private Object generateInvalidValue(String tableName, String fieldName) {
        String lowerFieldName = fieldName.toLowerCase();
        
        if (lowerFieldName.contains("email")) {
            return "invalid-email-format";
        } else if (lowerFieldName.contains("price")) {
            return -10.50; // Negative price
        } else if (lowerFieldName.contains("quantity")) {
            return -5; // Negative quantity
        } else if (lowerFieldName.contains("discount")) {
            return 150; // Invalid discount percentage
        } else {
            return "INVALID_VALUE";
        }
    }
}
