baseURI_Qa=http://localhost:8765

# Database connection settings
# Format: *********************************************
# Note: Change port to 5433 if your PostgreSQL is running on that port
JDBC_URL = ******************************************

# Database credentials
JDBC_USER = postgres
JDBC_PASSWORD = postgres

# Connection timeout settings (in seconds)
JDBC_CONNECTION_TIMEOUT = 10

# API Endpoints Configuration
api.post.user=/api/user/save
api.post.product=/api/product/save
api.post.order=/api/order/save
api.post.country=/api/country/save

api.get.user=/api/user/getById
api.get.product=/api/product/getById
api.get.order=/api/order/getById
api.get.country=/api/country/getById

api.put.user=/api/user/update
api.put.product=/api/product/update
api.put.order=/api/order/update
api.put.country=/api/country/update

api.delete.user=/api/user/delete
api.delete.product=/api/product/delete
api.delete.order=/api/order/delete
api.delete.country=/api/country/delete

# Database Table Mappings
table.user=users
table.product=products
table.order=orders
table.country=countries

# Primary Key Mappings
primarykey.user=user_id
primarykey.product=product_id
primarykey.order=order_id
primarykey.country=country_id

# Foreign Key Mappings (table.field=referenced_table.referenced_field)
foreignkey.order.user_id=users.user_id
foreignkey.order.product_id=products.product_id
foreignkey.product.country_id=countries.country_id

# Constraint Configurations
# Null Constraints (fields that cannot be null)
null.constraint.user=username,email
null.constraint.product=product_name,price
null.constraint.order=user_id,product_id,quantity
null.constraint.country=country_name,country_code

# Unique Constraints (fields that must be unique)
unique.constraint.user=username,email
unique.constraint.product=product_code
unique.constraint.order=order_number
unique.constraint.country=country_code

# Defect Table Configuration
defect.table.name=defects
defect.id.prefix=D_

# API Request Patterns Configuration
# Pattern 1: Direct API calls with headers and authentication
api.pattern.direct.base_url=http://localhost:8071
api.pattern.direct.auth_type=bearer
api.pattern.direct.bearer_token=your_bearer_token_here
api.pattern.direct.headers.Content-Type=application/json
api.pattern.direct.headers.Accept=application/json

# Pattern 2: Encrypted/Proxy API calls through decrypt endpoint
api.pattern.proxy.base_url=http://localhost:9762
api.pattern.proxy.endpoint=/decrypt
api.pattern.proxy.tenant_id=redberyl_redberyltech_com

# Excel Column Configuration
# Define which columns contain what information
excel.column.url=1
excel.column.request_body=2
excel.column.expected_result=3
excel.column.actual_result=4
excel.column.status=5
excel.column.entity=6
excel.column.pattern=7
excel.column.operation=8
excel.column.table_name=9

# Pattern Detection Methods
# auto = detect from Excel pattern column
# config = use entity.pattern.* configuration
# url = detect based on URL (if contains /decrypt then proxy, else direct)
pattern.detection.method=auto

# Default patterns if not specified in Excel
default.pattern=direct

# Excel-driven configuration
# The framework will read URLs and request bodies directly from Excel
# No need to pre-configure all endpoints - they come from Excel data




