baseURI_Qa=http://localhost:8765

# Database connection settings
# Format: *********************************************
# Note: Change port to 5433 if your PostgreSQL is running on that port
JDBC_URL = ******************************************

# Database credentials
JDBC_USER = postgres
JDBC_PASSWORD = postgres

# Connection timeout settings (in seconds)
JDBC_CONNECTION_TIMEOUT = 10

# API Endpoints Configuration
api.post.user=/api/user/save
api.post.product=/api/product/save
api.post.order=/api/order/save
api.post.country=/api/country/save

api.get.user=/api/user/getById
api.get.product=/api/product/getById
api.get.order=/api/order/getById
api.get.country=/api/country/getById

api.put.user=/api/user/update
api.put.product=/api/product/update
api.put.order=/api/order/update
api.put.country=/api/country/update

api.delete.user=/api/user/delete
api.delete.product=/api/product/delete
api.delete.order=/api/order/delete
api.delete.country=/api/country/delete

# Database Table Mappings
table.user=users
table.product=products
table.order=orders
table.country=countries

# Primary Key Mappings
primarykey.user=user_id
primarykey.product=product_id
primarykey.order=order_id
primarykey.country=country_id

# Foreign Key Mappings (table.field=referenced_table.referenced_field)
foreignkey.order.user_id=users.user_id
foreignkey.order.product_id=products.product_id
foreignkey.product.country_id=countries.country_id

# Constraint Configurations
# Null Constraints (fields that cannot be null)
null.constraint.user=username,email
null.constraint.product=product_name,price
null.constraint.order=user_id,product_id,quantity
null.constraint.country=country_name,country_code

# Unique Constraints (fields that must be unique)
unique.constraint.user=username,email
unique.constraint.product=product_code
unique.constraint.order=order_number
unique.constraint.country=country_code

# Defect Table Configuration
defect.table.name=defects
defect.id.prefix=D_

# API Request Patterns Configuration
# Pattern 1: Direct API calls with headers and authentication
api.pattern.direct.base_url=http://localhost:8071
api.pattern.direct.auth_type=bearer
api.pattern.direct.bearer_token=your_bearer_token_here
api.pattern.direct.headers.Content-Type=application/json
api.pattern.direct.headers.Accept=application/json

# Pattern 2: Encrypted/Proxy API calls through decrypt endpoint
api.pattern.proxy.base_url=http://localhost:9762
api.pattern.proxy.endpoint=/decrypt
api.pattern.proxy.tenant_id=redberyl_redberyltech_com

# Excel Column Configuration
# Define which columns contain what information
excel.column.url=1
excel.column.request_body=2
excel.column.expected_result=3
excel.column.actual_result=4
excel.column.status=5
excel.column.entity=6
excel.column.pattern=7
excel.column.operation=8
excel.column.table_name=9

# Pattern Detection Methods
# auto = detect from Excel pattern column
# config = use entity.pattern.* configuration
# url = detect based on URL (if contains /decrypt then proxy, else direct)
pattern.detection.method=auto

# Default patterns if not specified in Excel
default.pattern=direct

# =============================================================================
# AUTOMATED CRUD TESTING FRAMEWORK CONFIGURATION
# =============================================================================

# Services and Tables Configuration
# Format: service.tables.{service_name}=table1,table2,table3

# Contact Service Tables
service.tables.contact=AddressType,ContactType,Address,Contact

# Authentication Service Tables
service.tables.authentication=User,Role,Permission,UserRole,UserPermission

# Core Service Tables
service.tables.core=Country,State,City,Currency,Language

# Order Service Tables
service.tables.order=Order,OrderItem,OrderStatus,Payment

# Product Service Tables
service.tables.product=Product,Category,Brand,ProductCategory

# =============================================================================
# URL PATTERNS CONFIGURATION
# =============================================================================

# Direct Pattern URL Structure
# Base URL + Service Path + Table + Operation
direct.pattern.base_url=http://localhost:8071
direct.pattern.url_structure={base_url}/{service}/api/{table}/{operation}

# Direct Pattern Operations Mapping
direct.operation.post=save
direct.operation.put=update
direct.operation.patch=patch
direct.operation.get=getById
direct.operation.getall=getAll
direct.operation.delete=delete

# Proxy Pattern URL Structure
proxy.pattern.base_url=http://localhost:9762
proxy.pattern.endpoint=/decrypt
proxy.pattern.url_structure={base_url}{endpoint}

# Proxy Pattern Internal Endpoint Structure
proxy.internal.endpoint_structure=/{service}/api/{table}/{operation}

# Proxy Pattern Operations Mapping (same as direct)
proxy.operation.post=save
proxy.operation.put=update
proxy.operation.patch=patch
proxy.operation.get=getById
proxy.operation.getall=getAll
proxy.operation.delete=delete

# =============================================================================
# TABLE TO PATTERN MAPPING
# =============================================================================

# Service-wise Pattern Assignment
service.pattern.contact=proxy
service.pattern.authentication=proxy
service.pattern.core=direct
service.pattern.order=proxy
service.pattern.product=direct

# =============================================================================
# DATABASE SCHEMA AUTO-DETECTION
# =============================================================================

# Enable automatic schema detection
auto.schema.detection=true

# Schema detection queries
schema.query.columns=SELECT column_name, data_type, is_nullable, column_default FROM information_schema.columns WHERE table_name = ? ORDER BY ordinal_position
schema.query.primary_keys=SELECT column_name FROM information_schema.key_column_usage WHERE table_name = ? AND constraint_name LIKE '%_pkey'
schema.query.foreign_keys=SELECT column_name, referenced_table_name, referenced_column_name FROM information_schema.key_column_usage WHERE table_name = ? AND referenced_table_name IS NOT NULL
schema.query.unique_constraints=SELECT column_name FROM information_schema.key_column_usage WHERE table_name = ? AND constraint_name LIKE '%_key' AND constraint_name NOT LIKE '%_pkey'
schema.query.not_null_constraints=SELECT column_name FROM information_schema.columns WHERE table_name = ? AND is_nullable = 'NO' AND column_name NOT LIKE '%_id'

# =============================================================================
# REQUEST BODY GENERATION RULES
# =============================================================================

# Data type to sample value mapping
sample.data.string=Sample Text
sample.data.integer=123
sample.data.bigint=123456789
sample.data.decimal=99.99
sample.data.boolean=true
sample.data.date=2024-01-01
sample.data.timestamp=2024-01-01T10:00:00
sample.data.uuid=550e8400-e29b-41d4-a716-************

# Foreign key handling
foreign.key.sample.strategy=existing_random
foreign.key.fallback.value=1

# =============================================================================
# AUTOMATED TEST EXECUTION CONFIGURATION
# =============================================================================

# Test types to execute automatically
auto.test.types=normal,null_constraint,unique_constraint,foreign_key_invalid

# Operations to test automatically
auto.test.operations=post,put,patch,get,getall,delete

# Validation rules
validation.status_code.post=201,200
validation.status_code.put=200
validation.status_code.patch=200
validation.status_code.get=200
validation.status_code.getall=200
validation.status_code.delete=200,204

validation.constraint_violation.expected_status=400,422
validation.foreign_key_invalid.expected_status=400,422




