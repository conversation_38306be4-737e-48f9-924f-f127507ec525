baseURI_Qa=http://localhost:8765

# Database connection settings
# Format: *********************************************
# Note: Change port to 5433 if your PostgreSQL is running on that port
JDBC_URL = ******************************************

# Database credentials
JDBC_USER = postgres
JDBC_PASSWORD = postgres

# Connection timeout settings (in seconds)
JDBC_CONNECTION_TIMEOUT = 10

# API Endpoints Configuration
api.post.user=/api/user/save
api.post.product=/api/product/save
api.post.order=/api/order/save
api.post.country=/api/country/save

api.get.user=/api/user/getById
api.get.product=/api/product/getById
api.get.order=/api/order/getById
api.get.country=/api/country/getById

api.put.user=/api/user/update
api.put.product=/api/product/update
api.put.order=/api/order/update
api.put.country=/api/country/update

api.delete.user=/api/user/delete
api.delete.product=/api/product/delete
api.delete.order=/api/order/delete
api.delete.country=/api/country/delete

# Database Table Mappings
table.user=users
table.product=products
table.order=orders
table.country=countries

# Primary Key Mappings
primarykey.user=user_id
primarykey.product=product_id
primarykey.order=order_id
primarykey.country=country_id

# Foreign Key Mappings (table.field=referenced_table.referenced_field)
foreignkey.order.user_id=users.user_id
foreignkey.order.product_id=products.product_id
foreignkey.product.country_id=countries.country_id

# Constraint Configurations
# Null Constraints (fields that cannot be null)
null.constraint.user=username,email
null.constraint.product=product_name,price
null.constraint.order=user_id,product_id,quantity
null.constraint.country=country_name,country_code

# Unique Constraints (fields that must be unique)
unique.constraint.user=username,email
unique.constraint.product=product_code
unique.constraint.order=order_number
unique.constraint.country=country_code

# Defect Table Configuration
defect.table.name=defects
defect.id.prefix=D_




