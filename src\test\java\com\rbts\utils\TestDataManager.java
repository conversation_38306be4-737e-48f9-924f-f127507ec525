package com.rbts.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.javafaker.Faker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Test Data Manager for CRUD Testing Framework
 * Provides utilities for generating and managing test data
 */
@Slf4j
@Component
public class TestDataManager {
    
    private final ObjectMapper objectMapper;
    private final Faker faker;
    
    public TestDataManager() {
        this.objectMapper = new ObjectMapper();
        this.faker = new Faker();
    }
    
    /**
     * Generate sample request body for User entity
     */
    public String generateUserRequestBody() {
        ObjectNode userNode = objectMapper.createObjectNode();
        userNode.put("username", faker.name().username() + "_" + System.currentTimeMillis());
        userNode.put("email", faker.internet().emailAddress());
        userNode.put("firstName", faker.name().firstName());
        userNode.put("lastName", faker.name().lastName());
        userNode.put("phone", faker.phoneNumber().phoneNumber());
        userNode.put("address", faker.address().fullAddress());
        
        try {
            return objectMapper.writeValueAsString(userNode);
        } catch (Exception e) {
            log.error("Error generating user request body: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Generate sample request body for Product entity
     */
    public String generateProductRequestBody() {
        ObjectNode productNode = objectMapper.createObjectNode();
        productNode.put("product_name", faker.commerce().productName());
        productNode.put("product_code", "PRD_" + faker.number().numberBetween(1000, 9999));
        productNode.put("price", faker.number().randomDouble(2, 10, 1000));
        productNode.put("description", faker.lorem().sentence());
        productNode.put("category", faker.commerce().department());
        productNode.put("country_id", faker.number().numberBetween(1, 10));
        
        try {
            return objectMapper.writeValueAsString(productNode);
        } catch (Exception e) {
            log.error("Error generating product request body: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Generate sample request body for Country entity
     */
    public String generateCountryRequestBody() {
        ObjectNode countryNode = objectMapper.createObjectNode();
        countryNode.put("country_name", faker.country().name());
        countryNode.put("country_code", faker.country().countryCode2().toUpperCase());
        countryNode.put("currency", faker.currency().code());
        countryNode.put("continent", faker.lorem().word());
        
        try {
            return objectMapper.writeValueAsString(countryNode);
        } catch (Exception e) {
            log.error("Error generating country request body: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Generate sample request body for Order entity
     */
    public String generateOrderRequestBody() {
        ObjectNode orderNode = objectMapper.createObjectNode();
        orderNode.put("order_number", "ORD_" + faker.number().numberBetween(10000, 99999));
        orderNode.put("user_id", faker.number().numberBetween(1, 100));
        orderNode.put("product_id", faker.number().numberBetween(1, 50));
        orderNode.put("quantity", faker.number().numberBetween(1, 10));
        orderNode.put("total_amount", faker.number().randomDouble(2, 50, 500));
        orderNode.put("order_date", faker.date().toString());
        orderNode.put("status", "PENDING");
        
        try {
            return objectMapper.writeValueAsString(orderNode);
        } catch (Exception e) {
            log.error("Error generating order request body: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Generate request body for any entity
     */
    public String generateRequestBodyForEntity(String entity) {
        switch (entity.toLowerCase()) {
            case "user":
                return generateUserRequestBody();
            case "product":
                return generateProductRequestBody();
            case "country":
                return generateCountryRequestBody();
            case "order":
                return generateOrderRequestBody();
            default:
                log.warn("No specific generator for entity: {}, returning generic body", entity);
                return generateGenericRequestBody(entity);
        }
    }
    
    /**
     * Generate generic request body for unknown entities
     */
    private String generateGenericRequestBody(String entity) {
        ObjectNode genericNode = objectMapper.createObjectNode();
        genericNode.put("name", faker.name().name());
        genericNode.put("description", faker.lorem().sentence());
        genericNode.put("code", entity.toUpperCase() + "_" + faker.number().numberBetween(1000, 9999));
        genericNode.put("status", "ACTIVE");
        genericNode.put("created_date", faker.date().toString());
        
        try {
            return objectMapper.writeValueAsString(genericNode);
        } catch (Exception e) {
            log.error("Error generating generic request body: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Generate expected result JSON for entity
     */
    public String generateExpectedResultForEntity(String entity, String requestBody) {
        try {
            ObjectNode requestNode = (ObjectNode) objectMapper.readTree(requestBody);
            
            // Add common fields that would be returned by API
            requestNode.put("id", faker.number().numberBetween(1, 1000));
            requestNode.put("created_date", faker.date().toString());
            requestNode.put("updated_date", faker.date().toString());
            requestNode.put("status", "ACTIVE");
            
            // Add entity-specific ID field
            String primaryKey = getPrimaryKeyForEntity(entity);
            if (primaryKey != null) {
                requestNode.put(primaryKey, faker.number().numberBetween(1, 1000));
            }
            
            return objectMapper.writeValueAsString(requestNode);
            
        } catch (Exception e) {
            log.error("Error generating expected result for entity {}: {}", entity, e.getMessage());
            return requestBody; // Return original request body as fallback
        }
    }
    
    /**
     * Get primary key field name for entity
     */
    private String getPrimaryKeyForEntity(String entity) {
        Map<String, String> primaryKeys = new HashMap<>();
        primaryKeys.put("user", "user_id");
        primaryKeys.put("product", "product_id");
        primaryKeys.put("country", "country_id");
        primaryKeys.put("order", "order_id");
        
        return primaryKeys.get(entity.toLowerCase());
    }
    
    /**
     * Create test data for Excel file
     */
    public void createTestDataForExcel(String filePath, String sheetName) {
        ExcelUtils excelUtils = new ExcelUtils();
        
        // Create header row
        excelUtils.setCellData(filePath, sheetName, 1, 1, "URL");
        excelUtils.setCellData(filePath, sheetName, 1, 2, "Request Body");
        excelUtils.setCellData(filePath, sheetName, 1, 3, "Expected Result");
        excelUtils.setCellData(filePath, sheetName, 1, 4, "Actual Result");
        excelUtils.setCellData(filePath, sheetName, 1, 5, "Status");
        excelUtils.setCellData(filePath, sheetName, 1, 6, "Entity");
        
        // Create sample test data
        String[] entities = {"user", "product", "country", "order"};
        int rowNum = 2;
        
        for (String entity : entities) {
            String requestBody = generateRequestBodyForEntity(entity);
            String expectedResult = generateExpectedResultForEntity(entity, requestBody);
            
            excelUtils.setCellData(filePath, sheetName, rowNum, 1, "/api/" + entity + "/save");
            excelUtils.setCellData(filePath, sheetName, rowNum, 2, requestBody);
            excelUtils.setCellData(filePath, sheetName, rowNum, 3, expectedResult);
            excelUtils.setCellData(filePath, sheetName, rowNum, 4, ""); // Will be filled by test
            excelUtils.setCellData(filePath, sheetName, rowNum, 5, ""); // Will be filled by test
            excelUtils.setCellData(filePath, sheetName, rowNum, 6, entity);
            
            rowNum++;
        }
        
        log.info("Test data created in Excel file: {} sheet: {}", filePath, sheetName);
    }
    
    /**
     * Validate request body JSON format
     */
    public boolean isValidJson(String jsonString) {
        try {
            objectMapper.readTree(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Pretty print JSON string
     */
    public String prettyPrintJson(String jsonString) {
        try {
            Object json = objectMapper.readValue(jsonString, Object.class);
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(json);
        } catch (Exception e) {
            log.warn("Could not pretty print JSON: {}", e.getMessage());
            return jsonString;
        }
    }
}
