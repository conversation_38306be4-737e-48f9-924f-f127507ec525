package com.rbts.defect;

import com.rbts.config.ConfigManager;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.time.LocalDateTime;

/**
 * Defect Manager for CRUD Testing Framework
 * Handles defect ID generation and tracking for failed test cases
 */
@Slf4j
public class DefectManager {
    
    private final ConfigManager configManager;
    
    public DefectManager() {
        this.configManager = ConfigManager.getInstance();
    }
    
    /**
     * Generate unique defect ID for failed test case
     * Format: D_tablename_operation_001
     */
    public String generateDefectId(String entity, String operation, String testType) {
        String tableName = configManager.getTableName(entity);
        String prefix = configManager.getDefectIdPrefix();
        
        // Create base defect ID pattern
        String basePattern = String.format("%s%s_%s", prefix, tableName, operation.toLowerCase());
        
        // Add test type if specified (for constraint violations)
        if (testType != null && !testType.trim().isEmpty()) {
            basePattern += "_" + testType.toLowerCase();
        }
        
        // Get next sequence number
        int sequenceNumber = getNextSequenceNumber(basePattern);
        
        // Format with leading zeros
        String defectId = String.format("%s_%03d", basePattern, sequenceNumber);
        
        log.info("Generated defect ID: {}", defectId);
        return defectId;
    }
    
    /**
     * Get next sequence number for defect ID pattern
     */
    private int getNextSequenceNumber(String basePattern) {
        String defectTableName = configManager.getDefectTableName();
        
        // Ensure defect table exists
        createDefectTableIfNotExists();
        
        String query = String.format(
            "SELECT COALESCE(MAX(CAST(SUBSTRING(defect_id FROM '%s_([0-9]+)$') AS INTEGER)), 0) + 1 " +
            "FROM %s WHERE defect_id LIKE ?",
            basePattern, defectTableName
        );
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            
            statement.setString(1, basePattern + "_%");
            
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getInt(1);
                }
            }
        } catch (SQLException e) {
            log.error("Error getting next sequence number for pattern {}: {}", basePattern, e.getMessage());
        }
        
        return 1; // Default to 1 if query fails
    }
    
    /**
     * Create defect record in database
     */
    public void createDefectRecord(String defectId, String entity, String operation, 
                                 String testType, String description, String expectedResult, 
                                 String actualResult, String requestBody) {
        String defectTableName = configManager.getDefectTableName();
        
        String insertQuery = String.format(
            "INSERT INTO %s (defect_id, entity, operation, test_type, description, " +
            "expected_result, actual_result, request_body, created_date, status) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            defectTableName
        );
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(insertQuery)) {
            
            statement.setString(1, defectId);
            statement.setString(2, entity);
            statement.setString(3, operation);
            statement.setString(4, testType);
            statement.setString(5, description);
            statement.setString(6, expectedResult);
            statement.setString(7, actualResult);
            statement.setString(8, requestBody);
            statement.setTimestamp(9, Timestamp.valueOf(LocalDateTime.now()));
            statement.setString(10, "OPEN");
            
            int rowsAffected = statement.executeUpdate();
            if (rowsAffected > 0) {
                log.info("Defect record created successfully: {}", defectId);
            } else {
                log.warn("Failed to create defect record: {}", defectId);
            }
            
        } catch (SQLException e) {
            log.error("Error creating defect record {}: {}", defectId, e.getMessage());
            throw new RuntimeException("Failed to create defect record", e);
        }
    }
    
    /**
     * Create defect table if it doesn't exist
     */
    private void createDefectTableIfNotExists() {
        String defectTableName = configManager.getDefectTableName();
        
        String createTableQuery = String.format(
            "CREATE TABLE IF NOT EXISTS %s (" +
            "id SERIAL PRIMARY KEY, " +
            "defect_id VARCHAR(255) UNIQUE NOT NULL, " +
            "entity VARCHAR(100) NOT NULL, " +
            "operation VARCHAR(50) NOT NULL, " +
            "test_type VARCHAR(100), " +
            "description TEXT, " +
            "expected_result TEXT, " +
            "actual_result TEXT, " +
            "request_body TEXT, " +
            "created_date TIMESTAMP NOT NULL, " +
            "updated_date TIMESTAMP, " +
            "status VARCHAR(20) DEFAULT 'OPEN', " +
            "assigned_to VARCHAR(100), " +
            "resolution TEXT" +
            ")",
            defectTableName
        );
        
        try (Connection connection = getConnection();
             Statement statement = connection.createStatement()) {
            
            statement.execute(createTableQuery);
            log.debug("Defect table {} ensured to exist", defectTableName);
            
        } catch (SQLException e) {
            log.error("Error creating defect table: {}", e.getMessage());
            throw new RuntimeException("Failed to create defect table", e);
        }
    }
    
    /**
     * Check if defect ID already exists
     */
    public boolean defectExists(String defectId) {
        String defectTableName = configManager.getDefectTableName();
        String query = String.format("SELECT 1 FROM %s WHERE defect_id = ?", defectTableName);
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            
            statement.setString(1, defectId);
            try (ResultSet resultSet = statement.executeQuery()) {
                return resultSet.next();
            }
            
        } catch (SQLException e) {
            log.error("Error checking defect existence: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Update defect status
     */
    public void updateDefectStatus(String defectId, String status, String resolution) {
        String defectTableName = configManager.getDefectTableName();
        String updateQuery = String.format(
            "UPDATE %s SET status = ?, resolution = ?, updated_date = ? WHERE defect_id = ?",
            defectTableName
        );
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(updateQuery)) {
            
            statement.setString(1, status);
            statement.setString(2, resolution);
            statement.setTimestamp(3, Timestamp.valueOf(LocalDateTime.now()));
            statement.setString(4, defectId);
            
            int rowsAffected = statement.executeUpdate();
            if (rowsAffected > 0) {
                log.info("Defect {} status updated to: {}", defectId, status);
            } else {
                log.warn("No defect found with ID: {}", defectId);
            }
            
        } catch (SQLException e) {
            log.error("Error updating defect status: {}", e.getMessage());
            throw new RuntimeException("Failed to update defect status", e);
        }
    }
    
    /**
     * Get database connection
     */
    private Connection getConnection() throws SQLException {
        String url = configManager.getJdbcUrl();
        String user = configManager.getJdbcUser();
        String password = configManager.getJdbcPassword();
        
        return DriverManager.getConnection(url, user, password);
    }
}
